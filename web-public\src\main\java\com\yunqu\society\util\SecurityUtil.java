package com.yunqu.society.util;

import javax.servlet.http.HttpServletRequest;
import java.security.MessageDigest;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 安全验证工具类
 */
public class SecurityUtil {
    
    // IP访问频率限制
    private static final ConcurrentHashMap<String, AtomicInteger> ipRequestCount = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Long> ipLastRequestTime = new ConcurrentHashMap<>();
    
    // 每分钟最大请求次数
    private static final int MAX_REQUESTS_PER_MINUTE = 10;
    
    /**
     * 检查IP访问频率
     */
    public static boolean checkRateLimit(String clientIp) {
        long currentTime = System.currentTimeMillis();
        Long lastRequestTime = ipLastRequestTime.get(clientIp);
        
        // 如果超过1分钟，重置计数
        if (lastRequestTime == null || currentTime - lastRequestTime > 60000) {
            ipRequestCount.put(clientIp, new AtomicInteger(1));
            ipLastRequestTime.put(clientIp, currentTime);
            return true;
        }
        
        // 检查请求次数
        AtomicInteger count = ipRequestCount.get(clientIp);
        if (count == null) {
            count = new AtomicInteger(0);
            ipRequestCount.put(clientIp, count);
        }
        
        return count.incrementAndGet() <= MAX_REQUESTS_PER_MINUTE;
    }
    
    /**
     * 获取客户端真实IP
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }
    
    /**
     * 生成安全令牌
     */
    public static String generateSecureToken(String sessionId, String clientIp) {
        try {
            String data = sessionId + clientIp + System.currentTimeMillis();
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(data.getBytes("UTF-8"));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成安全令牌失败", e);
        }
    }
    
    /**
     * 验证请求来源
     */
    public static boolean validateReferer(HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        if (referer == null) return false;
        
        String serverName = request.getServerName();
        return referer.contains(serverName);
    }
    
    /**
     * 检查User-Agent
     */
    public static boolean isValidUserAgent(String userAgent) {
        if (userAgent == null || userAgent.trim().isEmpty()) return false;
        
        // 检查是否为常见的恶意User-Agent
        String ua = userAgent.toLowerCase();
        String[] suspiciousAgents = {"bot", "crawler", "spider", "scraper", "curl", "wget"};
        
        for (String suspicious : suspiciousAgents) {
            if (ua.contains(suspicious)) return false;
        }
        
        return true;
    }
}