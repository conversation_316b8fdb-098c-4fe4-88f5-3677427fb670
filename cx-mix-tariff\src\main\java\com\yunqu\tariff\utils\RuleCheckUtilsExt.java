package com.yunqu.tariff.utils;

import com.yunqu.tariff.base.CommonLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 资费规则检查工具扩展类 - 增强版
 * 用于扩展和修改原有RuleCheckUtils的功能
 */
public class RuleCheckUtilsExt {
    private static final Logger logger = CommonLogger.getcheckJobLogger();
    /**
     * 检查免费业务是否附加在网期限
     * 如果资费标准为零或免费，且duration包含regexPatterns中定义的模式，则返回false（符合规则）
     * 如果资费标准为零或免费，但duration不包含在网期限，则返回true（存在问题）
     *
     * @param type2 资费二级分类，只有当type2=1（套餐）时才进行检查
     * @param fees 资费标准
     * @param duration 使用期限
     * @param ex1Array 排除规则，如果fees在排除规则中，则不检查
     * @param regexPatterns 用于匹配在网时间要求的正则表达式模式数组
     * @return 是否存在问题
     */
    public static boolean checkFreeTariffWithDuration(String type2, String fees, String duration, String[] ex1Array, String[] regexPatterns) {
        logger.info("开始检查免费业务是否附加在网期限，二级分类: {}, 资费标准: {}, 在网要求: {}", type2, fees, duration);

        if(StringUtils.isBlank(fees)){
            logger.info("资费标准为空，跳过检查，返回false");
            return false;
        }
        
        // 如果在网要求为"0个月"，直接返回false
        if ("0个月".equals(duration)) {
            logger.info("在网要求为0个月，无限制条件，返回false");
            return false;
        }

        // 条件1: 二级分类为套餐
        boolean isPackage = "1".equals(type2);
        logger.info("是否为套餐类型: {}", isPackage);

        // 条件2: 资费标准为0元
        boolean isFree = "0".equals(fees) || "0.0".equals(fees);
        logger.info("是否为免费资费: {}", isFree);

        // 条件3: "在网要求"字段包含regexPatterns中定义的模式
        boolean hasDuration = checkDurationPatterns(duration, regexPatterns);
        logger.info("是否含有在网期限要求: {}", hasDuration);

        // 如果是套餐类型、免费资费、但含有在网期限要求，则存在问题
        boolean result = isPackage && isFree && hasDuration;
        logger.info("检查结果: {}", result);
        return result;
    }

    /**
     * 检查加装包是否附加限制性条件
     * 如果二级分类为加装包（type2=2），且duration包含regexPatterns中定义的模式，则返回false（符合规则）
     * 如果二级分类为加装包（type2=2），但duration不包含限制性条件，则返回true（存在问题）
     *
     * @param type2 资费二级分类
     * @param duration 使用期限
     * @param ex1Array 排除规则
     * @param regexPatterns 用于匹配在网时间要求的正则表达式模式数组
     * @return 是否存在问题
     */
    public static boolean checkAddonWithRestriction(String type2, String duration, String[] ex1Array, String[] regexPatterns) {
        logger.info("开始检查加装包是否附加限制性条件，二级分类: {}, 使用期限: {}", type2, duration);

        // 条件1: 二级分类为加装包
        boolean isAddon = "2".equals(type2);
        logger.info("是否为加装包类型: {}", isAddon);

        if (!isAddon) {
            logger.info("非加装包类型，跳过检查，返回false");
            return false;
        }
        
        // 如果在网要求为"0个月"，直接返回false
        if ("0个月".equals(duration)) {
            logger.info("在网要求为0个月，无限制条件，返回false");
            return false;
        }

        // 条件2: "在网要求"字段包含regexPatterns中定义的模式
        boolean hasDuration = checkDurationPatterns(duration, regexPatterns);
        logger.info("是否含有在网期限要求: {}", hasDuration);

        // 如果是加装包类型但不含有在网期限要求，则存在问题
        boolean result = hasDuration;
        logger.info("检查结果: {}", result);
        return result;
    }

    /**
     * 检查营销活动是否附加限制性条件
     * 如果二级分类为营销活动（type2=3），且duration包含regexPatterns中定义的模式，则返回false（符合规则）
     * 如果二级分类为营销活动（type2=3），但duration不包含限制性条件，则返回true（存在问题）
     *
     * @param type2 资费二级分类
     * @param duration 使用期限
     * @param ex1Array 排除规则
     * @param regexPatterns 用于匹配在网时间要求的正则表达式模式数组
     * @return 是否存在问题
     */
    public static boolean checkMarketingWithRestriction(String type2, String duration, String[] ex1Array, String[] regexPatterns) {
        logger.info("开始检查营销活动是否附加限制性条件，二级分类: {}, 使用期限: {}", type2, duration);

        // 条件1: 二级分类为营销活动
        boolean isMarketing = "3".equals(type2);
        logger.info("是否为营销活动类型: {}", isMarketing);

        if (!isMarketing) {
            logger.info("非营销活动类型，跳过检查，返回false");
            return false;
        }
        
        // 如果在网要求为"0个月"，直接返回false
        if ("0个月".equals(duration)) {
            logger.info("在网要求为0个月，无限制条件，返回false");
            return false;
        }

        // 条件2: "在网要求"字段包含regexPatterns中定义的模式
        boolean hasDuration = checkDurationPatterns(duration, regexPatterns);
        logger.info("是否含有在网期限要求: {}", hasDuration);

        // 如果是营销活动类型但不含有在网期限要求，则存在问题
        boolean result = hasDuration;
        logger.info("检查结果: {}", result);
        return result;
    }

    /**
     * 16. 套餐附加限制性条件检查
     * 如果二级分类为套餐（type2=1），且duration包含regexPatterns中定义的模式，则返回false（符合规则）
     * 如果二级分类为套餐（type2=1），但duration不包含限制性条件，则返回true（存在问题）
     *
     * @param type2 资费二级分类
     * @param duration 使用期限
     * @param ex1Array 排除规则
     * @param regexPatterns 用于匹配在网时间要求的正则表达式模式数组
     * @return 是否存在问题
     */
    public static boolean checkPackageWithRestriction(String type2, String duration, String[] ex1Array, String[] regexPatterns) {
        logger.info("开始检查套餐附加限制性条件，二级分类: {}, 使用期限: {}", type2, duration);

        // 条件1: 二级分类为套餐
        boolean isPackage = "1".equals(type2);
        logger.info("是否为套餐类型: {}", isPackage);

        if (!isPackage) {
            logger.info("非套餐类型，跳过检查，返回false");
            return false;
        }
        
        // 如果在网要求为"0个月"，直接返回false
        if ("0个月".equals(duration)) {
            logger.info("在网要求为0个月，无限制条件，返回false");
            return false;
        }

        // 条件2: "在网要求"字段包含regexPatterns中定义的模式
        boolean hasDuration = checkDurationPatterns(duration, regexPatterns);
        logger.info("是否含有在网期限要求: {}", hasDuration);

        // 如果是套餐类型但不含有在网期限要求，则存在问题
        boolean result = hasDuration;
        logger.info("检查结果: {}", result);
        return result;
    }


    /**
     * 检查字符串是否匹配正则表达式模式数组中的任一模式
     * @param text 要检查的字符串
     * @param regexPatterns 正则表达式模式数组
     * @return 如果匹配任一模式则返回true，否则返回false
     */
    private static boolean checkDurationPatterns(String text, String[] regexPatterns) {
        logger.info("开始检查字符串是否匹配正则表达式模式，文本: {}, 正则表达式数量: {}",
                     text, regexPatterns != null ? regexPatterns.length : 0);

        if (StringUtils.isBlank(text) || regexPatterns == null || regexPatterns.length == 0) {
            logger.info("文本为空或正则表达式为空，返回false");
            return false;
        }

        for (String pattern : regexPatterns) {
            if (StringUtils.isNotBlank(pattern)) {
                try {
                    logger.info("尝试匹配正则表达式: {}", pattern);
                    Pattern compiledPattern = Pattern.compile(pattern);
                    Matcher matcher = compiledPattern.matcher(text);
                    if (matcher.find()) {
                        logger.info("匹配成功，返回true");
                        return true;
                    }
                } catch (Exception e) {
                    logger.error("正则表达式编译失败: {}", pattern, e);
                }
            }
        }

        logger.info("没有匹配任何正则表达式，返回false");
        return false;
    }

    public static void main(String[] args) {
//        System.out.println(checkDurationPatterns("30个月，到期自动取消",
//                new String[]{".*个月",".*年",".*天",".*日","长期"}));

        Pattern compiledPattern = Pattern.compile(".*个月");
        Matcher matcher = compiledPattern.matcher("30个月，到期自动取消");
        if (matcher.matches()) {
            System.out.println("匹配成功，返回true");
        }
        if (matcher.find()) {
            System.out.println("匹配成功，返回true");
        }
    }
}
