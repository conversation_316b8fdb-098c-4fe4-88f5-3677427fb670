package com.yunqu.society.service.strategy;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.society.base.mapper.HumpMapper;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 文件获取策略
 * 根据业务ID获取c_cf_attachment中的记录并返回文件流
 */
public class FileGetStrategy implements OutInterfaceStrategy {

    @Override
    public JSONObject execute(JSONObject params,HttpServletRequest request,HttpServletResponse response) throws Exception {
        EasyQuery query = getQuery();
        // 获取业务ID
        String id = params.getString("id");
        long startTime = System.currentTimeMillis();
        // 参数校验
        if (StringUtils.isBlank(id)) {
            return EasyResult.fail("附件ID不能为空");
        }
        
        // 查询附件信息
        EasySQL sql = new EasySQL();
        sql.append("SELECT * FROM " + getTableName("c_cf_attachment"));
        sql.append(id," WHERE ID = ?");
        sql.append("N"," AND IS_DEL = ?");
        
        JSONObject attachment = query.queryForRow(sql.getSQL(), sql.getParams(), new HumpMapper());
        
        if (attachment == null ) {
            return EasyResult.fail("[文件下载] 未找到附件信息, 附件ID: " + id);
        }
        
        // 获取第一个附件（如果有多个附件，可以根据需求修改逻辑）
        String filePath = attachment.getString("filePath");
        String fileName = attachment.getString("name");
        Long fileSize = attachment.getLong("fileSize");
        String busiType = attachment.getString("busiType");
        String createTime = attachment.getString("createTime");

        logger.info("[文件下载] 附件信息 - ID: " + id + ", 文件名: " + fileName + ", 大小: " + fileSize + " bytes, 业务类型: " + busiType + ", 创建时间: " + createTime + ", 文件路径: " + filePath);
        if (StringUtils.isBlank(filePath)) {
            return createResult(false, "[文件下载] 附件路径为空, 附件ID: " + id,null);
        }
         // 打开文件流
        URL url = new URL(filePath);
        URLConnection conn = url.openConnection();

        // 设置连接参数
        conn.setConnectTimeout(30000); // 30秒连接超时
        conn.setReadTimeout(60000);    // 60秒读取超时
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

        HttpURLConnection httpConn = (HttpURLConnection) conn;
        int responseCode = httpConn.getResponseCode();

        if (responseCode != 200) {
            return createResult(false, "[文件下载] HTTP响应异常, 附件ID: " + id + ", 响应码: " + responseCode,null);
        }

        try (InputStream inputStream = conn.getInputStream();) {
            // 输出文件流
            writeFile(inputStream, fileName,response,id);

            // 由于已经直接输出了文件流，这里返回null
            return null;
        } catch (Exception e) {
            long errorTime = System.currentTimeMillis();
            logger.error("[文件下载] 获取文件流失败, 附件ID: " + id + ", 耗时: " + (errorTime - startTime) + "ms, 异常: " + e.getMessage(), e);
            return createResult(false, "获取文件流失败: " + e.getMessage(),null);
        }finally {
            if (conn instanceof HttpURLConnection) {
                ((HttpURLConnection) conn).disconnect();
                logger.info("[文件下载] HTTP连接已断开, 附件ID: " + id);
            }
        }
    }

     /**
     * 直接输出文件流
     * @param inputStream 输入流
     * @param fileName 文件名
     * @throws IOException IO异常
     */
    public void writeFile(InputStream inputStream, String fileName,HttpServletResponse response, String id) {
        response.setContentType("application/octet-stream");
        try {
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
        } catch (Exception e) {
            logger.error("[文件下载] 编码文件名失败, 附件ID: " + id + ", 异常: " + e.getMessage(), e);
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        }
        long startTime = System.currentTimeMillis();

        try(OutputStream outputStream = response.getOutputStream();) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.flush();
            long endTime = System.currentTimeMillis();
            logger.info("[文件传输] 传输完成, 附件ID: " + id +  ", 传输耗时: " + (endTime - startTime) + "ms");
        } catch (Exception e) {
            long errorTime = System.currentTimeMillis();
            logger.error("[文件传输] 输出文件流失败, 附件ID: " + id + ", 传输耗时: " + (errorTime - startTime) + "ms, 异常: " + e.getMessage(), e);
        }
       
    }

     @Override
     public void confirm(JSONObject params, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // TODO Auto-generated method stub
     }
}