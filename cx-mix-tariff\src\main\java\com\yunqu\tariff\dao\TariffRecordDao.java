package com.yunqu.tariff.dao;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.annontation.InfAuthCheck;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.tariff.base.AppDaoContext;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.utils.BusiUtil;
import com.yunqu.tariff.utils.DimDateServer;
import com.yunqu.tariff.utils.ReportUtils;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 资费记录表Dao
 * <AUTHOR>
 *
 */
@WebObject(name = "tariffRecord")
public class TariffRecordDao extends AppDaoContext {
    Logger logger = CommonLogger.getLogger();

    @WebControl(name = "reporterList", type = Types.RECORD)
    public JSONObject reporterList() {
        UserModel user = UserUtil.getUser(request);
        // 权限控制
        JSONObject entByTypeCode = BusiUtil.getEntByTypeCode(user.getDeptGroupType());
        List<JSONObject> deptIndexs = BusiUtil.getDeptIndex(entByTypeCode.getString("ent"), user.getDeptProvinceCode(),"", user.getSchemaName());
        for (JSONObject deptIndex : deptIndexs) {
            String ent = deptIndex.getString("ENT");
            String provincePinyinSimple = deptIndex.getString("PROVINCE_PINYIN");
            String provinceName = deptIndex.getString("PROVINCE_NAME");
            String outEnt = BusiUtil.getOutEntCode(ent);
            if (StringUtils.isBlank(provincePinyinSimple)) {
                provincePinyinSimple = "JT";
            }
            String reporter = provincePinyinSimple + outEnt; // + index;
            String reporterName = "";
            if(StringUtils.isBlank(provinceName)){
                reporterName = BusiUtil.getEntByEntCode(outEnt)+"集团";
            }else{
                reporterName = provinceName + BusiUtil.getEntByEntCode(outEnt); // + deptName;
            }
            deptIndex.put("REPORTER", reporter);
            deptIndex.put("REPORTER_NAME", reporterName);
        }
        JSONObject data = new JSONObject();
        data.put("reporters", deptIndexs);
        String deptProvinceCode = user.getDeptProvinceCode();
        data.put("provinceCode", deptProvinceCode);
        String provinceName = "";
        if (StringUtils.isNotBlank(deptProvinceCode)) {
            try {
                EasyQuery easyQuery = QueryFactory.getReadQuery();
                EasySQL sql1 = new EasySQL("select   PROVINCE_NAME ");
                sql1.append(" from cc_province t1");
                sql1.append(" where 1=1");
                sql1.append(deptProvinceCode, "and t1.PROVINCE_CODE = ?");
                provinceName = easyQuery.queryForString(sql1.getSQL(), sql1.getParams());
            } catch (SQLException e) {
                logger.error(e.getMessage(), e);
            }
        }
        data.put("provinceName", provinceName);
        if (StringUtils.isBlank(user.getDeptProvinceCode())) {
            data.put("reporterType", "1");
        } else {
            data.put("reporterType", "2");
        }
        return EasyResult.ok(data);
    }


	@WebControl(name="reporterDict", type=Types.RECORD)
	public JSONObject reporterDict() {
        logger.info("获取关联备案主体字典=={}", param);
		String entCode = param.getString("entCode");
		String tariffProvinceCode = param.getString("tariffProvinceCode");

		UserModel user = UserUtil.getUser(request);
		// 权限控制
		List<JSONObject> deptIndexs = BusiUtil.getDeptIndexByTariffProvinceCode(entCode, tariffProvinceCode, user.getSchemaName());
		for (JSONObject deptIndex:deptIndexs) {
			String ent = deptIndex.getString("ENT");
			String index = deptIndex.getString("DEPT_INDEX");
			String deptName = deptIndex.getString("DEPT_NAME");
			String provincePinyinSimple = deptIndex.getString("PROVINCE_PINYIN");
			String provinceName = deptIndex.getString("PROVINCE_NAME");
			String outEnt = BusiUtil.getOutEntCode(ent);
			if (StringUtils.isBlank(provincePinyinSimple)) {
				provincePinyinSimple = "JT";
			}
			String reporter = provincePinyinSimple + outEnt;// + index;
			String reporterName = provinceName + BusiUtil.getEntByEntCode(outEnt) ;//+ deptName;
			deptIndex.put("REPORTER", reporter);
			deptIndex.put("REPORTER_NAME", reporterName);
		}
		JSONObject data = new JSONObject();
		data.put("reporters", deptIndexs);
		String deptProvinceCode = user.getDeptProvinceCode();
		data.put("provinceCode", deptProvinceCode);
		String  provinceName = "";
		if(StringUtils.isNotBlank(deptProvinceCode)){
			try {
				EasyQuery easyQuery = QueryFactory.getReadQuery();
				EasySQL sql1 = new EasySQL("select   PROVINCE_NAME ");
				sql1.append(" from cc_province t1");
				sql1.append(" where 1=1");
				sql1.append(deptProvinceCode, "and t1.PROVINCE_CODE = ?");
				provinceName = easyQuery.queryForString(sql1.getSQL(), sql1.getParams());
			} catch (SQLException e) {
				logger.error(e.getMessage(), e);
			}
		}
		data.put("provinceName", provinceName);
		if (StringUtils.isBlank(user.getDeptProvinceCode())) {
			data.put("reporterType", "1");
		} else {
			data.put("reporterType", "2");
		}
		return EasyResult.ok(data);
	}



	/*@WebControl(name="findReporter", type=Types.RECORD)
	public JSONObject findReporter() {
		UserModel user = UserUtil.getUser(request);
		String reporter = BusiUtil.getReporter(user.getDeptProvinceCode(), user.getDeptGroupType(), "");
		JSONObject entJson = BusiUtil.getEnt(reporter);
		JSONObject data = new JSONObject();
		data.put("reporter", reporter);
		data.put("provinceCode", user.getDeptProvinceCode());
		data.put("reporterType", entJson.getString("reporterType"));
		return EasyResult.ok(data);
	}*/

    @WebControl(name = "feeFillingInfo", type = Types.RECORD)
    public JSONObject feeFillingInfo() {
        String id = param.getString("id");
        if(StringUtils.isBlank(id)){
            return EasyResult.fail("该资费方案已删除");
        }
        try {
            EasySQL sql = new EasySQL();
            sql.append("select t1.*");
            sql.append("from " + getTableName("XTY_TARIFF_RECORD") + " t1");
            sql.append("where 1=1");
            sql.append(param.getString("id"), "and t1.ID=?");
            sql.append(param.getString("tariffNo"), "and t1.REPORT_NO=?");
            logger.info("获取资费详情==={}", sql.toFullSql());
            JSONObject jsonObject = queryForRecord(sql.getSQL(), sql.getParams());

            if(StringUtils.isBlank(id) && jsonObject != null){
                id = jsonObject.getJSONObject("data").getString("ID");
            }
            // 查询省市树
            EasyQuery easyQuery = QueryFactory.getReadQuery();
            // 先获取省编码
            EasySQL sql2 = new EasySQL();
            sql2.append("SELECT  PROVINCE_CODE , AREA_CODE,AREA_NAME,TYPE   from  ");
            sql2.append(getDbName() + ".XTY_TARIFF_AREQ ");
            sql2.append(" where 1 = 1");
            sql2.append(id, " and TARIFF_RECORD_ID = ?",false);
            logger.info("查询省市树==={}", sql2.toFullSql());
            List<JSONObject> provinceList = easyQuery.queryForList(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
            List<List<String>> areaTree = new ArrayList<>();
            for (JSONObject object : provinceList) {
                List<String> a = new ArrayList<>();
                a.add(object.getString("PROVINCE_CODE"));
                a.add(object.getString("AREA_CODE"));
                a.add(object.getString("AREA_NAME"));
                a.add(object.getString("TYPE"));
                areaTree.add(a);
            }
            jsonObject.put("areaTree", areaTree);
            return jsonObject;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    @WebControl(name = "feeDelFillingInfo", type = Types.RECORD)
    public JSONObject feeDelFillingInfo() {
        String id = param.getString("id");
        try {
            EasySQL sql = new EasySQL();
            sql.append("select t1.*");
            sql.append("from " + getTableName("XTY_TARIFF_RECORD_HIS") + " t1");
            sql.append("where 1=1");
            sql.append(param.getString("id"), "and t1.ID=?");
            sql.append(param.getString("tariffNo"), "and t1.REPORT_NO=?");
            logger.info("获取资费详情==={}", sql.toFullSql());
            JSONObject jsonObject = queryForRecord(sql.getSQL(), sql.getParams());

            if(StringUtils.isBlank(id) && jsonObject != null){
                id = jsonObject.getJSONObject("data").getString("ID");
            }
            // 查询省市树
            EasyQuery easyQuery = QueryFactory.getReadQuery();
            // 先获取省编码
            EasySQL sql2 = new EasySQL();
            sql2.append("SELECT  PROVINCE_CODE , AREA_CODE,AREA_NAME,TYPE   from  ");
            sql2.append(getDbName() + ".XTY_TARIFF_AREQ ");
            sql2.append(" where 1 = 1");
            sql2.append(id, " and TARIFF_RECORD_ID = ?",false);
            logger.info("查询省市树==={}", sql2.toFullSql());
            List<JSONObject> provinceList = easyQuery.queryForList(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
            List<List<String>> areaTree = new ArrayList<>();
            for (JSONObject object : provinceList) {
                List<String> a = new ArrayList<>();
                a.add(object.getString("PROVINCE_CODE"));
                a.add(object.getString("AREA_CODE"));
                a.add(object.getString("AREA_NAME"));
                a.add(object.getString("TYPE"));
                areaTree.add(a);
            }
            jsonObject.put("areaTree", areaTree);
            return jsonObject;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    /**
     * 资费填报列表 - 使用ES查询
     */
    @InfAuthCheck(resId = "cx-xty-tariff-enter")
    @WebControl(name = "feeFillingList", type = Types.LIST)
    public JSONObject feeFillingList() {
        try {
            UserModel user = UserUtil.getUser(request);
            
            // 构建ES查询参数
            JSONObject queryParams = buildFeeFillingEsQueryParams(user);
            
            // 直接调用ElasticsearchKit.search查询ES
            logger.info("[feeFillingList] queryParams: {}", JSON.toJSONString(queryParams));
            JSONObject esResult = ElasticsearchKit.search(
                    Constants.XTY_TARIFF_BAK_INFO_INDEX,
                    queryParams
            );
            
            // 解析ES查询结果
            return processFeeFillingEsResult(esResult, user);
        } catch (Exception e) {
            logger.error("[feeFillingList] Error: {}", e.getMessage(), e);
            return EasyResult.fail("查询ES索引失败: " + e.getMessage());
        }
    }

    /**
     * 构建资费填报列表ES查询参数
     */
    private JSONObject buildFeeFillingEsQueryParams(UserModel user) {
        int pageNumber = 1;
        int pageSize = 15;
        if(StringUtils.isNotBlank(param.getString("pageNumber"))){
            pageNumber = param.getIntValue("pageNumber");
        }
        if(StringUtils.isNotBlank(param.getString("pageSize"))){
            pageSize = param.getIntValue("pageSize");
        }

        JSONObject queryParams = new JSONObject();
        JSONObject queryObj = new JSONObject();
        JSONObject boolObj = new JSONObject();
        JSONArray mustArray = new JSONArray();

        // 权限控制 - 获取reporters
        JSONObject entByTypeCode = BusiUtil.getEntByTypeCode(user.getDeptGroupType());
        List<JSONObject> deptIndexs = BusiUtil.getDeptIndex(entByTypeCode.getString("ent"), user.getDeptProvinceCode(),"", user.getSchemaName());
        JSONArray reporterArray = new JSONArray();
        for (JSONObject deptIndex : deptIndexs) {
            String ent = deptIndex.getString("ENT");
            String provincePinyinSimple = deptIndex.getString("PROVINCE_PINYIN");
            String outEnt = BusiUtil.getOutEntCode(ent);
            if (StringUtils.isBlank(provincePinyinSimple)) {
                provincePinyinSimple = "JT";
            }
            String reporter = provincePinyinSimple + outEnt;
            reporterArray.add(reporter);
        }
        
        // REPORTER权限过滤
        if (!reporterArray.isEmpty()) {
            JSONObject reporterTerms = new JSONObject();
            reporterTerms.put("terms", new JSONObject().fluentPut("REPORTER.keyword", reporterArray));
            mustArray.add(reporterTerms);
        }

        // ENT_ID过滤
        if (StringUtils.isNotBlank(user.getEpCode())) {
            JSONObject entIdMatch = new JSONObject();
            entIdMatch.put("term", new JSONObject().fluentPut("ENT_ID.keyword", user.getEpCode()));
            mustArray.add(entIdMatch);
        }

        // BUSI_ORDER_ID过滤
        if (StringUtils.isNotBlank(user.getBusiOrderId())) {
            JSONObject busiOrderIdMatch = new JSONObject();
            busiOrderIdMatch.put("term", new JSONObject().fluentPut("BUSI_ORDER_ID.keyword", user.getBusiOrderId()));
            mustArray.add(busiOrderIdMatch);
        }

        // ENT过滤
        JSONObject entInfo = BusiUtil.getEntByTypeCode(user.getDeptGroupType());
        String ent = entInfo != null ? entInfo.getString("ent") : "";
        if (StringUtils.isNotBlank(ent)) {
            JSONObject entMatch = new JSONObject();
            entMatch.put("term", new JSONObject().fluentPut("ENT", ent));
            mustArray.add(entMatch);
        }

        // 资费名称
        String name = param.getString("name");
        if (StringUtils.isNotBlank(name)) {
            JSONObject nameMatch = new JSONObject();
            nameMatch.put("wildcard", new JSONObject().fluentPut("NAME", "*" + name + "*"));
            mustArray.add(nameMatch);
        }

        // 备案编号
        String reportNo = param.getString("reportNo");
        if (StringUtils.isNotBlank(reportNo)) {
            JSONObject reportNoMatch = new JSONObject();
            reportNoMatch.put("wildcard", new JSONObject().fluentPut("REPORT_NO", "*" + reportNo + "*"));
            mustArray.add(reportNoMatch);
        }

        // 备案者
        String reporter = param.getString("reporter");
        if (StringUtils.isNotBlank(reporter)) {
            if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) {
                JSONObject reporterMatch = new JSONObject();
                reporterMatch.put("term", new JSONObject().fluentPut("ENT.keyword", reporter));
                mustArray.add(reporterMatch);
            } else {
                JSONObject reporterMatch = new JSONObject();
                reporterMatch.put("wildcard", new JSONObject().fluentPut("REPORTER.keyword", "*" + reporter + "*"));
                mustArray.add(reporterMatch);
            }
        }

        // 资费类型1
        // 一级分类
        String type1 = param.getString("type1");
        if (StringUtils.isNotBlank(type1)) {
            JSONObject type1Query = new JSONObject();
            JSONArray type1OrArray = new JSONArray();

            for (String code : type1.split(",")) {
                if (StringUtils.isNotBlank(code)) {
                    JSONObject typeMatch = new JSONObject();
                    typeMatch.put("term", new JSONObject().fluentPut("TYPE1", code));
                    type1OrArray.add(typeMatch);
                }
            }

            type1Query.put("bool", new JSONObject().fluentPut("should", type1OrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(type1Query);
        }

        // 二级分类
        String type2 = param.getString("type2");
        if (StringUtils.isNotBlank(type2)) {
            JSONObject type2Query = new JSONObject();
            JSONArray type2OrArray = new JSONArray();

            for (String code : type2.split(",")) {
                if (StringUtils.isNotBlank(code)) {
                    JSONObject typeMatch = new JSONObject();
                    typeMatch.put("term", new JSONObject().fluentPut("TYPE2", code));
                    type2OrArray.add(typeMatch);
                }
            }

            type2Query.put("bool", new JSONObject().fluentPut("should", type2OrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(type2Query);
        }

        // 状态
        String status = param.getString("status");
        if (StringUtils.isNotBlank(status)) {
            JSONObject statusMatch = new JSONObject();
            statusMatch.put("term", new JSONObject().fluentPut("STATUS", status));
            mustArray.add(statusMatch);
        }

        // 资费属性
        String tariffAttr = param.getString("tariff_attr");
        if (StringUtils.isNotBlank(tariffAttr)) {
            JSONObject tariffAttrMatch = new JSONObject();
            tariffAttrMatch.put("term", new JSONObject().fluentPut("TARIFF_ATTR", tariffAttr));
            mustArray.add(tariffAttrMatch);
        }

        // 资费标准范围
        String feesBegin = param.getString("feesBegin");
        String feesEnd = param.getString("feesEnd");
        if (StringUtils.isNotBlank(feesBegin) || StringUtils.isNotBlank(feesEnd)) {
            // 创建bool查询用于组合多个条件
            JSONObject boolQuery = new JSONObject();
            JSONArray shouldArray = new JSONArray();

            // 使用优化后的FEES查询构建方法
            JSONObject feesQuery = buildFeesRegexQuery(feesBegin, feesEnd);
            if (feesQuery != null) {
                shouldArray.add(feesQuery);
                logger.info("[tariffRecordListNew] 主要FEES查询条件: {}", feesQuery.toJSONString());
            } else {
                // 如果buildFeesRegexQuery返回null，回退到基本查询
                // 1. 尝试数值范围查询
                try {
                    JSONObject numericRangeQuery = new JSONObject();
                    JSONObject numericRangeObj = new JSONObject();
                    boolean hasNumericRange = false;

                    if (StringUtils.isNotBlank(feesBegin)) {
                        double beginValue = Double.parseDouble(feesBegin);
                        numericRangeObj.put("gte", beginValue);
                        hasNumericRange = true;
                    }

                    if (StringUtils.isNotBlank(feesEnd)) {
                        double endValue = Double.parseDouble(feesEnd);
                        numericRangeObj.put("lte", endValue);
                        hasNumericRange = true;
                    }

                    if (hasNumericRange) {
                        numericRangeQuery.put("range", new JSONObject().fluentPut("FEES", numericRangeObj));
                        shouldArray.add(numericRangeQuery);
                    }
                } catch (NumberFormatException e) {
                    logger.warn("[tariffRecordListNew] 资费值解析为数值失败: {}", e.getMessage());
                }

                // 2. 对于精确查询，添加特殊处理
                if (StringUtils.isNotBlank(feesBegin) && StringUtils.isNotBlank(feesEnd) && feesBegin.equals(feesEnd)) {
                    // 精确匹配，既尝试原值也尝试带小数点的形式
                    JSONObject termsQuery = new JSONObject();
                    JSONArray termValues = new JSONArray();
                    termValues.add(feesBegin);

                    // 如果没有小数点，添加.0形式
                    if (!feesBegin.contains(".")) {
                        termValues.add(feesBegin + ".0");
                    }

                    termsQuery.put("terms", new JSONObject().fluentPut("FEES.str", termValues));
                    shouldArray.add(termsQuery);
                }

                // 3. 使用字符串匹配作为备选
                JSONObject strRangeQuery = new JSONObject();
                JSONObject strRangeObj = new JSONObject();

                if (StringUtils.isNotBlank(feesBegin)) {
                    strRangeObj.put("gte", feesBegin);
                }

                if (StringUtils.isNotBlank(feesEnd)) {
                    strRangeObj.put("lte", feesEnd);
                }

                strRangeQuery.put("range", new JSONObject().fluentPut("FEES.str", strRangeObj));
                shouldArray.add(strRangeQuery);
            }

            // 如果有足够的查询条件，添加bool查询
            if (shouldArray.size() > 0) {
                boolQuery.put("bool", new JSONObject()
                        .fluentPut("should", shouldArray)
                        .fluentPut("minimum_should_match", 1));

                mustArray.add(boolQuery);

                logger.info("[tariffRecordListNew] 最终费用范围查询条件: {}", boolQuery.toJSONString());
            }
        }

        // 是否通信类
        String isTelecom = param.getString("is_telecom");
        if (StringUtils.isNotBlank(isTelecom)) {
            JSONObject isTelecomMatch = new JSONObject();
            isTelecomMatch.put("term", new JSONObject().fluentPut("IS_TELECOM", isTelecom));
            mustArray.add(isTelecomMatch);
        }

        // 超出资费
        String extraFees = param.getString("extra_fees");
        if (StringUtils.isNotBlank(extraFees)) {
            JSONObject extraFeesMatch = new JSONObject();
            extraFeesMatch.put("wildcard", new JSONObject().fluentPut("EXTRA_FEES.keyword", "*" + extraFees + "*"));
            mustArray.add(extraFeesMatch);
        }

        // 其他费用
        String otherFees = param.getString("other_fees");
        if (StringUtils.isNotBlank(otherFees)) {
            JSONObject otherFeesMatch = new JSONObject();
            otherFeesMatch.put("wildcard", new JSONObject().fluentPut("OTHER_FEES.keyword", "*" + otherFees + "*"));
            mustArray.add(otherFeesMatch);
        }

        // 上线日期范围
        String onlineDay = param.getString("onlineDay");
        if (StringUtils.isNotBlank(onlineDay)) {
            onlineDay = onlineDay.replaceAll("\\[", "").replaceAll("]", "").replaceAll("\"", "");
            if (StringUtils.isNotBlank(onlineDay)) {
                String[] onlineDaySplit = onlineDay.split(",");
                if (onlineDaySplit.length == 2) {
                    JSONObject onlineDayRange = new JSONObject();
                    JSONObject rangeObj = new JSONObject();
                    rangeObj.put("gte", onlineDaySplit[0].replaceAll("-", ""));
                    rangeObj.put("lte", onlineDaySplit[1].replaceAll("-", ""));
                    onlineDayRange.put("range", new JSONObject().fluentPut("ONLINE_DAY", rangeObj));
                    mustArray.add(onlineDayRange);
                }
            }
        }

        // 下线日期范围
        String offlineDay = param.getString("offlineDay");
        if (StringUtils.isNotBlank(offlineDay)) {
            offlineDay = offlineDay.replaceAll("\\[", "").replaceAll("]", "").replaceAll("\"", "");
            if (StringUtils.isNotBlank(offlineDay)) {
                String[] offlineDaySplit = offlineDay.split(",");
                if (offlineDaySplit.length == 2) {
                    JSONObject offlineDayRange = new JSONObject();
                    JSONObject rangeObj = new JSONObject();
                    rangeObj.put("gte", offlineDaySplit[0].replaceAll("-", ""));
                    rangeObj.put("lte", offlineDaySplit[1].replaceAll("-", ""));
                    offlineDayRange.put("range", new JSONObject().fluentPut("OFFLINE_DAY", rangeObj));
                    mustArray.add(offlineDayRange);
                }
            }
        }

        // 资费渠道
        String channel = param.getString("channel");
        if (StringUtils.isNotBlank(channel)) {
            JSONObject channelMatch = new JSONObject();
            channelMatch.put("wildcard", new JSONObject().fluentPut("CHANNEL.keyword", "*" + channel + "*"));
            mustArray.add(channelMatch);
        }

        // 序列号
        String seqNo = param.getString("seqNo");
        if (StringUtils.isNotBlank(seqNo)) {
            JSONObject seqNoMatch = new JSONObject();
            seqNoMatch.put("wildcard", new JSONObject().fluentPut("SEQ_NO.keyword", "*" + seqNo + "*"));
            mustArray.add(seqNoMatch);
        }

        // 版本号
        String versionNum = param.getString("versionNum");
        if (StringUtils.isNotBlank(versionNum)) {
            if ("0".equals(versionNum)) {
                JSONObject versionNumMatch = new JSONObject();
                versionNumMatch.put("term", new JSONObject().fluentPut("VERSION_NUM.keyword", "V0"));
                mustArray.add(versionNumMatch);
            } else {
                // 不等于V0的版本号
                JSONObject versionNumNotMatch = new JSONObject();
                versionNumNotMatch.put("bool", new JSONObject().fluentPut("must_not",
                        new JSONArray().fluentAdd(new JSONObject().fluentPut("term",
                                new JSONObject().fluentPut("VERSION_NUM.keyword", "V0")))));
                mustArray.add(versionNumNotMatch);
            }
        }

        // 适用地区过滤 - 需要特殊处理
        String areaCode = param.getString("areaCode");
        String provinceCode = param.getString("provinceCode");

        // 处理省份查询
        if (StringUtils.isNotBlank(provinceCode)) {
            JSONObject provinceQuery = new JSONObject();
            JSONArray provinceOrArray = new JSONArray();

            // 从AREA_LIST中提取PROVINCE_CODE进行查询
            String[] provinceCodes = provinceCode.split(",");
            JSONArray provinceTermsArray = new JSONArray();
            for (String pCode : provinceCodes) {
                if (StringUtils.isNotBlank(pCode)) {
                    provinceTermsArray.add(pCode);
                }
            }

            if (provinceTermsArray.size() > 0) {
                // 使用PROVINCE_CODES数组字段进行查询
                JSONObject provinceCodesTerms = new JSONObject();
                provinceCodesTerms.put("terms", new JSONObject().fluentPut("PROVINCE_CODES", provinceTermsArray));
                provinceOrArray.add(provinceCodesTerms);
            }

            // 添加通用编码
            JSONObject allProvinceCodesQuery = new JSONObject();
            allProvinceCodesQuery.put("term", new JSONObject().fluentPut("PROVINCE_CODES", com.yunqu.tariff.base.Constants.AREA_CODE_ALL));
            provinceOrArray.add(allProvinceCodesQuery);

            provinceQuery.put("bool", new JSONObject().fluentPut("should", provinceOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(provinceQuery);
        }

        // 处理地区查询
        if (StringUtils.isNotBlank(areaCode)) {
            JSONObject areaQuery = new JSONObject();
            JSONArray areaOrArray = new JSONArray();

            // 将地区代码拆分为数组
            String[] areaCodes = areaCode.split(",");
            JSONArray areaCodesArray = new JSONArray();
            for (String aCode : areaCodes) {
                if (StringUtils.isNotBlank(aCode)) {
                    areaCodesArray.add(aCode);
                }
            }

            if (areaCodesArray.size() > 0) {
                // 使用AREA_CODES数组字段进行查询
                JSONObject areaCodesTerms = new JSONObject();
                areaCodesTerms.put("terms", new JSONObject().fluentPut("AREA_CODES", areaCodesArray));
                areaOrArray.add(areaCodesTerms);
            }

            // 添加通用编码
            JSONObject allAreaCodesQuery = new JSONObject();
            allAreaCodesQuery.put("term", new JSONObject().fluentPut("AREA_CODES", com.yunqu.tariff.base.Constants.AREA_CODE_ALL));
            areaOrArray.add(allAreaCodesQuery);

            areaQuery.put("bool", new JSONObject().fluentPut("should", areaOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(areaQuery);
        }

        boolObj.put("must", mustArray);
        queryObj.put("bool", boolObj);
        queryParams.put("query", queryObj);

        // 设置分页
        queryParams.put("from", (pageNumber - 1) * pageSize);
        queryParams.put("size", pageSize);
        queryParams.put("track_total_hits", true);

        // 排序
        JSONArray sortArray = new JSONArray();
        sortArray.add(new JSONObject().fluentPut("CREATE_TIME.keyword", new JSONObject().fluentPut("order", "desc")));
        queryParams.put("sort", sortArray);

        return queryParams;
    }

    /**
     * 处理资费填报列表ES查询结果
     */
    private JSONObject processFeeFillingEsResult(JSONObject esResult, UserModel user) {
        try {
            if (esResult == null || !esResult.containsKey("hits")) {
                return EasyResult.fail("ES查询失败");
            }
            int pageSize=0;
            if(StringUtils.isBlank(param.getString("pageSize"))){
                pageSize = 15;
            }else{
                pageSize = param.getIntValue("pageSize");
            }

            JSONObject hitsObject = esResult.getJSONObject("hits");
            int totalHits = hitsObject.getJSONObject("total").getIntValue("value");
            int totalPage = (totalHits + pageSize - 1) / pageSize;

            // 提取记录列表
            JSONArray hitsArray = hitsObject.getJSONArray("hits");
            List<JSONObject> recordList = new ArrayList<>();

            // 获取用户权限信息
            JSONObject entInfo = BusiUtil.getEntByTypeCode(user.getDeptGroupType());
            String reporterType = entInfo != null ? entInfo.getString("reporterType") : "";
            String ent = entInfo != null ? entInfo.getString("ent") : "";

            for (int i = 0; i < hitsArray.size(); i++) {
                JSONObject hit = hitsArray.getJSONObject(i);
                JSONObject source = hit.getJSONObject("_source");
                
                // 添加CAN_EDIT字段
                String recordEnt = source.getString("ENT");
                String recordReporterType = source.getString("REPORTER_TYPE");
                int canEdit = (ent.equals(recordEnt) && reporterType.equals(recordReporterType)) ? 1 : 0;
                source.put("CAN_EDIT", canEdit);
                
                recordList.add(source);
            }

            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("state", 1);
            result.put("data", recordList);
            result.put("totalRow", totalHits);
            result.put("totalPage", totalPage);

            return result;
        } catch (Exception e) {
            logger.error("[processFeeFillingEsResult] 处理ES结果失败", e);
            return EasyResult.fail("处理查询结果失败");
        }
    }

    /**
     * 资费记录表
     */
    @WebControl(name = "tariffRecord", type = Types.RECORD)
    public JSONObject tariffRecord() {
        String id = param.getString("tariffRecord.ID");
        JSONObject tariffRecord = queryForRecord(new EasyRecord(getTableName("XTY_TARIFF_RECORD"), "ID").setPrimaryValues(id));
        return tariffRecord;
    }

    /**
     * 资费记录列表
     */
    @InfAuthCheck(resId = "cx-xty-tariff-list")
    @WebControl(name = "tariffRecordFields", type = Types.LIST)
    public JSONObject tariffRecordFields() {
        try {
            return EasyResult.ok(BusiUtil.FieldZhMap);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    /**
     * 资费记录列表
     */
    @InfAuthCheck(resId = "cx-xty-tariff-list")
    @WebControl(name = "tariffRecordList", type = Types.LIST)
    public JSONObject tariffRecordList() {
        try {
            UserModel user = UserUtil.getUser(request);
            String deptGroupType = user.getDeptGroupType();

            // 如果前端传入了reportNo，则只根据reportNo查询，不需要其他条件
            String reportNo = param.getString("reportNo");
            if (StringUtils.isNotBlank(reportNo)) {
                StringBuffer selectFields = new StringBuffer();
                for (Map.Entry<String, Object> entry : BusiUtil.FieldZhMap.entrySet()) {
                    String fieldName = entry.getKey();
                    if ("PROVINCE_NAME".equals(fieldName) || "AREA_NAME".equals(fieldName)) {
                        continue;
                    }
                    if (StringUtils.isNotBlank(selectFields)) {
                        selectFields.append(", ");
                    }
                    selectFields.append("t1." + fieldName);
                }
                EasySQL sql = new EasySQL(" select distinct t1.ID," + selectFields.toString() + ",  t1.REPORT_KEY,t1.VERSION_NO,t1.FIELD_CHECK_NO,t1.FIELD_CHECK_RESULT,t1.FIELD_CHECK_TIME ");
                sql.append("from " + getTableName("XTY_TARIFF_RECORD") + " t1");
                sql.append("WHERE 1=1");
                sql.appendLike(reportNo, "and t1.REPORT_NO like ?");
                sql.append(DictConstants.DICT_SY_YN_N, "and t1.IS_HISTORY=?");
                sql.append("order by t1.CREATE_TIME desc");
                logger.info("[tariffRecord] tariffRecordList by reportNo:{}", sql.getFullSq());

                JSONObject queryForPageList = queryForPageList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                List<JSONObject> list = (List<JSONObject>) queryForPageList.get("data");
                // 处理地区树信息
                processAreaTree(list);
                queryForPageList.put("data", list);
                return queryForPageList;
            }

            // 原有的查询逻辑
            StringBuffer selectFields = new StringBuffer();
            for (Map.Entry<String, Object> entry : BusiUtil.FieldZhMap.entrySet()) {
                String fieldName = entry.getKey();
                if ("PROVINCE_NAME".equals(fieldName) || "AREA_NAME".equals(fieldName)) {
                    continue;
                }
                if (StringUtils.isNotBlank(selectFields)) {
                    selectFields.append(", ");
                }
                selectFields.append("t1." + fieldName);
            }
            EasySQL sql = new EasySQL(" select distinct t1.ID," + selectFields.toString() + ",  t1.REPORT_KEY,t1.VERSION_NO,t1.FIELD_CHECK_NO,t1.FIELD_CHECK_RESULT,t1.FIELD_CHECK_TIME ");
            sql.append("from " + getTableName("XTY_TARIFF_RECORD") + " t1");
            sql.append("WHERE 1=1");
            // 如果跳转来源渠道是来自企业各类资费数量分布
            sql.append(getEntId(), "and t1.ENT_ID=?");
            sql.append(getBusiOrderId(), "and t1.BUSI_ORDER_ID=?");
            // 省企业
            if (StringUtils.startsWith(deptGroupType, Constants.GROUP_TYPE_PE)) {
                String provinceCode = user.getDeptProvinceCode();
                sql.append("and exists ( ");
                sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
                sql.append("where tt.TARIFF_RECORD_ID = t1.ID");
                sql.append(provinceCode, "AND (tt.PROVINCE_CODE=?");
                sql.append(Constants.AREA_CODE_ALL, "OR tt.PROVINCE_CODE=?)");
                sql.append(") ");
            }
			/*// 集团
			else if (StringUtils.startsWith(deptGroupType, Constants.GROUP_TYPE_GE)) { }
			// 工信部
			else if (Constants.GROUP_TYPE_B_XGJ.equals(deptGroupType) ||
					 Constants.GROUP_TYPE_B_SSC.equals(deptGroupType) ||
					 Constants.GROUP_TYPE_B_XTY.equals(deptGroupType)) { }*/
            // 类型
            String type = param.getString("type");
            if (Constants.TARIFF_ATTR_01.equals(type)) { // 全国
                sql.append(Constants.TARIFF_ATTR_01, "AND t1.TARIFF_ATTR=?", false);
            } else if (Constants.TARIFF_ATTR_02.equals(type)) {  // 省内
                sql.append(Constants.TARIFF_ATTR_02, "AND (t1.TARIFF_ATTR=?", false);
                sql.append(Constants.TARIFF_ATTR_03, "OR t1.TARIFF_ATTR=?)", false);
            }
            // 企业, 电信001、移动002、联通003、广电004
            sql.append(param.getString("ent"), "and t1.ENT = ?");
            // 备案方类型, 1-集团 、2-省企业
            sql.append(param.getString("reporterType"), "and t1.REPORTER_TYPE = ?");
            //资费状态
            String status = param.getString("status");
            String statTime = param.getString("statTime");
            if (StringUtils.isNotBlank(statTime)) {
                if (StringUtils.isNotBlank(status)) {
                    String[] statTimeDayArray = statTime.split("~");
                    String startTime = statTimeDayArray[0].replaceAll("-", "");
                    String endTime = statTimeDayArray[1].replaceAll("-", "");
                    sql.append("and (  1 =0 ");
                    for (String code : status.split(",")) {
                        switch (code) {
                            case "1":  // 在售：1上架时间 小于等于 终止时间  2. 下架时间为空 或者 下架时间 大于等于 开始时间
                                sql.append(endTime, "  or (  t1.ONLINE_DAY <= ?  AND (t1.OFFLINE_DAY IS NULL or t1.OFFLINE_DAY =''  OR  ");
                                sql.append(startTime, "  t1.OFFLINE_DAY >= ?) ) ");
                                break;
                            case "2":  // 无效：删除时间 小于等于 终止时间
                                sql.append(endTime, "  or (    DATE(t1.DEL_TIME) <= ?  ");
                                sql.append(startTime, "  and  DATE(t1.DEL_TIME) >= ?  ");
                                sql.append(startTime, " )  ");
                                break;
                            case "3":  // 下架： 下架时间 小于等于 终止时间
                                sql.append(endTime, "  or(   t1.OFFLINE_DAY <= ?  ");
                                sql.append(startTime, " and   t1.OFFLINE_DAY >= ?  ");
                                sql.append(startTime, " )  ");
                                break;
                            case "4":  // 未售: 上架时间 >= 开始时间
                                sql.append(endTime, "  or (  t1.ONLINE_DAY >= ? )");
                                break;
                        }
                    }
                    sql.append(" ) ");
                }
            } else {
                if (StringUtils.isNotBlank(status)) {
                    sql.append("and ( t1.STATUS in ('-1'");
                    for (String code : status.split(",")) {
                        sql.append(code, ", ?");
                    }
                    sql.append("))");
                }
            }

            // 备案者
            sql.appendLike(param.getString("reportObj"), "and t1.REPORT_OBJ like ?");
            //序列号
            sql.appendLike(param.getString("seq_no"), "and t1.SEQ_NO like ?");
            //别名
            sql.appendLike(param.getString("tariff_another_name"), "and t1.TARIFF_ANOTHER_NAME like ?");
            // 备案号
            sql.appendLike(param.getString("reportNo"), "and t1.REPORT_NO like ?");
            // 适用省份
            String provinceCode = param.getString("provinceCode");
            // 适用地区
            String areaCode = param.getString("areaCode");
            if (StringUtils.isNotBlank(provinceCode) || StringUtils.isNotBlank(areaCode)) {
                sql.append("and exists ( ");
                sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
                sql.append("where tt.TARIFF_RECORD_ID = t1.ID");
                if (StringUtils.isNotBlank(provinceCode)) {
                    provinceCode = provinceCode + ",000";
                    sql.appendIn(provinceCode.split(","), "AND tt.PROVINCE_CODE");
                }
                if (StringUtils.isNotBlank(areaCode)) {
                    areaCode = areaCode + ",000";
                    sql.appendIn(areaCode.split(","), "AND tt.AREA_CODE");
                }
                sql.append(") ");
            }
            // 适用范围
            sql.appendLike(param.getString("applicablePeople"), "and t1.APPLICABLE_PEOPLE like ?");
            // 退订方式
            sql.appendLike(param.getString("unsubscribe"), "and t1.UNSUBSCRIBE like ?");
            // 违约责任
            sql.appendLike(param.getString("responsibility"), "and t1.RESPONSIBILITY like ?");
            // 其他服务内容
            sql.appendLike(param.getString("otherContent"), "and t1.OTHER_CONTENT like ?");
            //其他事项
            sql.appendLike(param.getString("others"), "and t1.OTHERS like ?");
            // 销售渠道
            sql.appendLike(param.getString("channel"), "and t1.CHANNEL like ?");
            // 是否通信类
            sql.append(param.getString("is_telecom"), "and t1.IS_TELECOM = ?");
            // 超出资费
            sql.appendLike(param.getString("extra_fees"), "and t1.EXTRA_FEES like ?");
            // 其他费用
            sql.appendLike(param.getString("other_fees"), "and t1.OTHER_FEES like ?");

            //一级分类
            String type1 = param.getString("type1");
            if (StringUtils.isNotBlank(type1)) {
                sql.append("and ( t1.TYPE1 in ('-1'");
                for (String code : type1.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            //二级分类
            String type2 = param.getString("type2");
            if (StringUtils.isNotBlank(type2)) {
                sql.append("and ( t1.TYPE2 in ('-1'");
                for (String code : type2.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 名称
            sql.appendLike(param.getString("name"), "and t1.NAME like ?");
            // 资费标准
            sql.append(param.getInteger("feesBegin"), "and t1.FEES >= ?");
            sql.append(param.getInteger("feesEnd"), "and t1.FEES <= ?");
            // 其他服务内容
            sql.appendLike(param.getString("otherContent"), "and t1.OTHER_CONTENT like ?");
            // 版本号
            if(StringUtils.isNotBlank(param.getString("versionNum"))){
                if("0".equals(param.getString("versionNum"))){
                    sql.append("and t1.VERSION_NUM = 'V0' ");
                }else{
                    sql.append("and t1.VERSION_NUM <> 'V0' ");
                }
            }
            // 修改时间
            String updateTime = param.getString("updateTime");
            if (StringUtils.isNotBlank(updateTime) && updateTime.contains("~")) {
                String[] updateTimeArray = updateTime.split("~");
                sql.append(updateTimeArray[0], "and t1.UPDATE_TIME >= ?");
                sql.append(updateTimeArray[1], "and t1.UPDATE_TIME <= ?");
            }
            // 创建时间
            String createTime = param.getString("createTime");
            if (StringUtils.isNotBlank(createTime) && createTime.contains("~")) {
                String[] createTimeArray = createTime.split("~");
                sql.append(createTimeArray[0], "and t1.CREATE_TIME >= ?");
                sql.append(createTimeArray[1], "and t1.CREATE_TIME <= ?");
            }
            // 上线日期
            String onlineDayStart = param.getString("onlineDayStart");
            if(StringUtils.isNotBlank(onlineDayStart)){
                sql.append(onlineDayStart.replaceAll("-", ""), "and t1.ONLINE_DAY >= ?");
            }
            String onlineDayEnd = param.getString("onlineDayEnd");
            if(StringUtils.isNotBlank(onlineDayEnd)){
                sql.append(onlineDayEnd.replaceAll("-", ""), "and t1.ONLINE_DAY <= ?");
            }
            // 下线日期
            String offlineDayStart = param.getString("offlineDayStart");
            if(StringUtils.isNotBlank(offlineDayStart)){
                sql.append(offlineDayStart.replaceAll("-", ""), "and t1.OFFLINE_DAY >= ?");
            }
            String offlineDayEnd = param.getString("offlineDayEnd");
            if(StringUtils.isNotBlank(offlineDayEnd)){
                sql.append(offlineDayEnd.replaceAll("-", ""), "and t1.OFFLINE_DAY <= ?");
            }

            // 资费属性
            String tariff_attr = param.getString("tariff_attr");
            if (StringUtils.isNotBlank(tariff_attr)) {
                sql.append("and ( t1.TARIFF_ATTR in ('-1'");
                for (String code : tariff_attr.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 备案主体
//            String reporter = param.getString("reporter");
//            if ("001".compareTo(reporter) <= 0 && "005".compareTo(reporter) >= 0) { //reporter在"001"到"005"之间
//                sql.append("005".equals(reporter) ? "004" : reporter, "and t1.ENT = ?");
//            } else {
//                sql.appendLike(reporter, "and t1.REPORTER like ?");
//            }


            // 资费规则核查时间
            String fieldCheckTime = param.getString("fieldCheckTime");
            if (StringUtils.isNotBlank(fieldCheckTime) && fieldCheckTime.contains("~")) {
                String[] fieldCheckTimeArray = fieldCheckTime.split("~");
                sql.append(fieldCheckTimeArray[0], "and t1.FIELD_CHECK_TIME >= ?");
                sql.append(fieldCheckTimeArray[1], "and t1.FIELD_CHECK_TIME <= ?");
            }

            // 资费字段编号（逗号分隔的多个编号）
            String fieldCheckNo = param.getString("fieldCheckNo");
            if (StringUtils.isNotBlank(fieldCheckNo)) {
                String[] fieldCheckNoArray = fieldCheckNo.split(",");
                for (String no : fieldCheckNoArray) {
                    if (StringUtils.isNotBlank(no)) {
                        sql.append(no, "and FIND_IN_SET(?, t1.FIELD_CHECK_NO) > 0");
                    }
                }
            }

            String reporter = param.getString("reporter");
            if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) { //reporter在"1"到"5"之间
                sql.append(reporter, "and t1.ENT = ?");
            } else {
                String[] reporterArr = reporter.split(",");
                if(reporterArr.length >1){
                    sql.append(" and ( ");
                    int count = 0;
                    for (String s: reporterArr){
                        if(count > 0){
                            sql.appendLike(s, " or t1.REPORTER like ?");
                        }else{
                            sql.appendLike(s, " t1.REPORTER like ?");
                        }
                        count ++;
                    }
                    sql.append(" ) ");
                }else{
                    sql.appendLike(reporter, "and t1.REPORTER like ?");
                }
            }
            // 公示状态
//            String is_public = param.getString("is_public");
//            if (StringUtils.isNotBlank(is_public)) {
//                sql.append("and ( t1.IS_PUBLIC in ('-1'");
//                for (String code : is_public.split(",")) {
//                    sql.append(code, ", ?");
//                }
//                sql.append("))");
//            }
            // 资费单位 fees_unit
            sql.appendLike(param.getString("fees_unit"), "and t1.FEES_UNIT like ?");
            // 有限期限 valid_period
            sql.appendLike(param.getString("valid_period"), "and t1.VALID_PERIOD like ?");
            // 在网要求 duration
            sql.appendLike(param.getString("duration"), "and t1.DURATION like ?");
            sql.append(DictConstants.DICT_SY_YN_N, "and t1.IS_HISTORY=?");
            // sql.append("group by "+ selectFields.toString() +", t1.REPORT_KEY");
            sql.append("order by t1.CREATE_TIME desc");
            logger.info("[tariffRecord] tariffRecordList:{}", sql.getFullSq());
            JSONObject queryForPageList = queryForPageList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            List<JSONObject> list = (List<JSONObject>) queryForPageList.get("data");
            if (list.size() > 0) {
                // 获取适用地区省市树
                list.stream().forEach(json -> {
                    try {
                        EasyQuery easyQuery = QueryFactory.getReadQuery();
                        easyQuery.setConvertField(1);
                        // 先获取省编码
                        EasySQL sql2 = new EasySQL();
                        sql2.append("SELECT distinct PROVINCE_CODE as value, PROVINCE_NAME as label   from  ");
                        sql2.append(getDbName() + ".XTY_TARIFF_AREQ ");
                        sql2.append(" where 1 = 1");
                        sql2.append(" and PROVINCE_CODE != ''");
                        sql2.append(json.getString("ID"), " and TARIFF_RECORD_ID = ?");
                        List<JSONObject> provinceList = easyQuery.queryForList(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
                        int provinceCount = provinceList.size();// 省个数
                        // 获取省下的市信息，组织成树
                        for (JSONObject jsonObject : provinceList) {
                            try {
                                EasySQL sql3 = new EasySQL();
                                sql3.append("SELECT AREA_CODE as value, AREA_NAME as label   from  ");
                                sql3.append(getDbName() + ".XTY_TARIFF_AREQ ");
                                sql3.append(" where 1 = 1");
                                sql3.append(json.getString("ID"), " and TARIFF_RECORD_ID = ?");
                                sql3.append(jsonObject.getString("value"), " and PROVINCE_CODE = ?");
                                List<JSONObject> areaList = easyQuery.queryForList(sql3.getSQL(), sql3.getParams(), new JSONMapperImpl());
                                jsonObject.put("children", areaList);
                            } catch (Exception e) {
                                logger.error(e.getMessage(), e);
                            }
                        }
                        json.put("areaTree", provinceList);
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }
                });
            }
            // 4.重新封装
            queryForPageList.put("data", list);
            return queryForPageList;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }


    @InfAuthCheck(resId = "cx-xty-tariff-list-del")
    @WebControl(name = "tariffDelRecordList", type = Types.LIST)
    public JSONObject tariffDelRecordList() {
        try {
            UserModel user = UserUtil.getUser(request);
            String deptGroupType = user.getDeptGroupType();
            StringBuffer selectFields = new StringBuffer();
            for (Map.Entry<String, Object> entry : BusiUtil.FieldZhMap.entrySet()) {
                String fieldName = entry.getKey();
                if ("PROVINCE_NAME".equals(fieldName) || "AREA_NAME".equals(fieldName)) {
                    continue;
                }
                if (StringUtils.isNotBlank(selectFields)) {
                    selectFields.append(", ");
                }
                selectFields.append("t1." + fieldName);
            }
            EasySQL sql = new EasySQL(" select t1.* ");
            sql.append("from " + getTableName("XTY_TARIFF_RECORD_HIS") + " t1");
            sql.append("WHERE 1=1");
            // 如果跳转来源渠道是来自企业各类资费数量分布
            sql.append(getEntId(), "and t1.ENT_ID=?");
            sql.append(getBusiOrderId(), "and t1.BUSI_ORDER_ID=?");
            // 省企业
            if (StringUtils.startsWith(deptGroupType, Constants.GROUP_TYPE_PE)) {
                String provinceCode = user.getDeptProvinceCode();
                sql.append("and exists ( ");
                sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
                sql.append("where tt.TARIFF_RECORD_ID = t1.ID");
                sql.append(provinceCode, "AND (tt.PROVINCE_CODE=?");
                sql.append(Constants.AREA_CODE_ALL, "OR tt.PROVINCE_CODE=?)");
                sql.append(") ");
            }
            // 企业, 电信001、移动002、联通003、广电004
            sql.append(param.getString("ent"), "and t1.ENT = ?");
            sql.append(param.getString("reportNo"), "and t1.REPORT_NO = ?");
            //序列号
            sql.appendLike(param.getString("seq_no"), "and t1.SEQ_NO like ?");
            // 适用省份
            String provinceCode = param.getString("provinceCode");
            // 适用地区
            String areaCode = param.getString("areaCode");
            if (StringUtils.isNotBlank(provinceCode) || StringUtils.isNotBlank(areaCode)) {
                sql.append("and exists ( ");
                sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
                sql.append("where tt.TARIFF_RECORD_ID = t1.ID");
                if (StringUtils.isNotBlank(provinceCode)) {
                    provinceCode = provinceCode + ",000";
                    sql.appendIn(provinceCode.split(","), "AND tt.PROVINCE_CODE");
                }
                if (StringUtils.isNotBlank(areaCode)) {
                    areaCode = areaCode + ",000";
                    sql.appendIn(areaCode.split(","), "AND tt.AREA_CODE");
                }
                sql.append(") ");
            }
            //一级分类
            String type1 = param.getString("type1");
            if (StringUtils.isNotBlank(type1)) {
                sql.append("and ( t1.TYPE1 in ('-1'");
                for (String code : type1.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            //二级分类
            String type2 = param.getString("type2");
            if (StringUtils.isNotBlank(type2)) {
                sql.append("and ( t1.TYPE2 in ('-1'");
                for (String code : type2.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 名称
            sql.appendLike(param.getString("name"), "and t1.NAME like ?");
            // 删除时间
            String delTime = param.getString("delTime");
            if (StringUtils.isNotBlank(delTime) && delTime.contains("~")) {
                String[] updateTimeArray = delTime.split("~");
                sql.append(updateTimeArray[0], "and t1.DEL_TIME >= ?");
                sql.append(updateTimeArray[1], "and t1.DEL_TIME <= ?");
            }
            // 备案主体
            String reporter = param.getString("reporter");
            if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) { //reporter在"1"到"5"之间
                sql.append(reporter, "and t1.ENT = ?");
            } else {
                String[] reporterArr = reporter.split(",");
                if(reporterArr.length >1){
                    sql.append(" and ( ");
                    int count = 0;
                    for (String s: reporterArr){
                        if(count > 0){
                            sql.appendLike(s, " or t1.REPORTER like ?");
                        }else{
                            sql.appendLike(s, " t1.REPORTER like ?");
                        }
                        count ++;
                    }
                    sql.append(" ) ");
                }else{
                    sql.appendLike(reporter, "and t1.REPORTER like ?");
                }
            }
            sql.append(Constants.TARIFF_STATUS_2, "and t1.STATUS=?");
            sql.append("order by t1.CREATE_TIME desc");
            logger.info("[tariffRecord] tariffRecordList:{}", sql.getFullSq());
            JSONObject queryForPageList = queryForPageList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            List<JSONObject> list = (List<JSONObject>) queryForPageList.get("data");
            if (list.size() > 0) {
                // 获取适用地区省市树
                list.stream().forEach(json -> {
                    try {
                        EasyQuery easyQuery = QueryFactory.getReadQuery();
                        easyQuery.setConvertField(1);
                        // 先获取省编码
                        EasySQL sql2 = new EasySQL();
                        sql2.append("SELECT distinct PROVINCE_CODE as value, PROVINCE_NAME as label   from  ");
                        sql2.append(getDbName() + ".XTY_TARIFF_AREQ ");
                        sql2.append(" where 1 = 1");
                        sql2.append(" and PROVINCE_CODE != ''");
                        sql2.append(json.getString("ID"), " and TARIFF_RECORD_ID = ?");
                        List<JSONObject> provinceList = easyQuery.queryForList(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
                        int provinceCount = provinceList.size();// 省个数
                        // 获取省下的市信息，组织成树
                        for (JSONObject jsonObject : provinceList) {
                            try {
                                EasySQL sql3 = new EasySQL();
                                sql3.append("SELECT AREA_CODE as value, AREA_NAME as label   from  ");
                                sql3.append(getDbName() + ".XTY_TARIFF_AREQ ");
                                sql3.append(" where 1 = 1");
                                sql3.append(json.getString("ID"), " and TARIFF_RECORD_ID = ?");
                                sql3.append(jsonObject.getString("value"), " and PROVINCE_CODE = ?");
                                List<JSONObject> areaList = easyQuery.queryForList(sql3.getSQL(), sql3.getParams(), new JSONMapperImpl());
                                jsonObject.put("children", areaList);
                            } catch (Exception e) {
                                logger.error(e.getMessage(), e);
                            }
                        }
                        json.put("areaTree", provinceList);
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }
                });
            }
            // 4.重新封装
            queryForPageList.put("data", list);
            return queryForPageList;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    // 提取处理地区树的方法
    private void processAreaTree(List<JSONObject> list) {
        if (list.size() > 0) {
            // 获取适用地区省市树
            list.stream().forEach(json -> {
                try {
                    EasyQuery easyQuery = QueryFactory.getReadQuery();
                    easyQuery.setConvertField(1);
                    // 先获取省编码
                    EasySQL sql2 = new EasySQL();
                    sql2.append("SELECT distinct PROVINCE_CODE as value, PROVINCE_NAME as label from ");
                    sql2.append(getDbName() + ".XTY_TARIFF_AREQ ");
                    sql2.append(" where 1 = 1");
                    sql2.append(" and PROVINCE_CODE != ''");
                    sql2.append(json.getString("ID"), " and TARIFF_RECORD_ID = ?");
                    List<JSONObject> provinceList = easyQuery.queryForList(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
                    // 获取省下的市信息，组织成树
                    for (JSONObject jsonObject : provinceList) {
                        try {
                            EasySQL sql3 = new EasySQL();
                            sql3.append("SELECT AREA_CODE as value, AREA_NAME as label from ");
                            sql3.append(getDbName() + ".XTY_TARIFF_AREQ ");
                            sql3.append(" where 1 = 1");
                            sql3.append(json.getString("ID"), " and TARIFF_RECORD_ID = ?");
                            sql3.append(jsonObject.getString("value"), " and PROVINCE_CODE = ?");
                            List<JSONObject> areaList = easyQuery.queryForList(sql3.getSQL(), sql3.getParams(), new JSONMapperImpl());
                            jsonObject.put("children", areaList);
                        } catch (Exception e) {
                            logger.error(e.getMessage(), e);
                        }
                    }
                    json.put("areaTree", provinceList);
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            });
        }
    }

    @WebControl(name = "findHistoryList", type = Types.LIST)
    public JSONObject findHistoryList() {
        try {
            String fieldNames = "t1.ID, t1.ENT_ID, t1.BUSI_ORDER_ID, t1.CREATE_TIME, t1.UPDATE_TIME, t1.CREATE_ACC, "
                    + "t1.UPDATE_ACC, t1.CREATE_DEPT, t1.PROVINCE, t1.CITY, t1.CITY_NAME, t1.ENT, t1.REPORTER_NAME, "
                    + "t1.ENT_NAME, t1.REPORTER_TYPE, t1.CREATE_TYPE, t1.DATE_ID, t1.SEQ_NO, t1.REPORT_OBJ, t1.REPORT_NO, "
                    + "t1.REPORT_KEY, t1.VERSION_NO, t1.IS_HISTORY, t1.REPORTER, t1.IS_PUBLIC, t1.REASON_NO_PUBLIC, "
                    + "t1.TYPE1, t1.TYPE2, t1.NAME, t1.FEES, t1.FEES_UNIT, t1.CALL_NUM, t1.DATA_NUM, t1.DATA_UNIT, t1.SMS_NUM, t1.INTERNATIONAL_CALL, "
                    + "t1.INTERNATIONAL_ROAMING_DATA, t1.INTERNATIONAL_SMS, t1.ORIENT_TRAFFIC, t1.ORIENT_TRAFFIC_UNIT, t1.IPTV, t1.BANDWIDTH, "
                    + "t1.RIGHTS, t1.OTHER_CONTENT, t1.APPLICABLE_PEOPLE, t1.APPLICABLE_AREA, t1.VALID_PERIOD, "
                    + "t1.RESPONSIBILITY, t1.RESTRICTIONS, t1.ONLINE_DAY, t1.OFFLINE_DAY, t1.OTHERS, t1.CHANNEL, "
                    + "t1.DURATION, t1.STATUS, t1.DEL_TIME, t1.REASON, t1.PLAN, t1.DEL_ACC, t1.APPLICABLE_PROVINCE, t1.AREA_DESC,"
                    + "t1.VERSION_NUM,t1.TARIFF_ANOTHER_NAME,t1.UNSUBSCRIBE,t1.TARIFF_ATTR,t1.EXTRA_FEES,t1.OTHER_FEES,t1.IS_TELECOM";
            String groupConcatNames = ", GROUP_CONCAT(t2.AREA_NAME) AREA_NAME, GROUP_CONCAT(t2.PROVINCE_NAME) PROVINCE_NAME";
            EasySQL sql = new EasySQL();
            sql.append("select * from (");
            sql.append("select " + fieldNames + groupConcatNames);
            sql.append("from " + getTableName("XTY_TARIFF_RECORD") + " t1");
            sql.append("left join " + getTableName("XTY_TARIFF_AREQ") + " t2 on t1.ID=t2.TARIFF_RECORD_ID");
            sql.append("where 1=1");
            sql.append(param.getString("reportKey"), "and t1.REPORT_KEY=?", false);
            sql.append("group by " + fieldNames);
            sql.append("union all");
            sql.append("select " + fieldNames + groupConcatNames);
            sql.append("from " + getTableName("XTY_TARIFF_RECORD_HIS") + " t1");
            sql.append("left join " + getTableName("XTY_TARIFF_AREQ") + " t2 on t1.ID=t2.TARIFF_RECORD_ID");
            sql.append("where 1=1");
            sql.append(param.getString("reportKey"), "and t1.REPORT_KEY=?", false);
            sql.append("group by " + fieldNames);
            sql.append(") temp");
            sql.append("order by VERSION_NO desc");
            return queryForList(sql.getSQL(), sql.getParams());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    @WebControl(name = "findTariffUpdate", type = Types.LIST)
    public JSONObject findTariffUpdate() {
        try {
            EasySQL sql = new EasySQL();
            sql.append("select t2.*");
            sql.append("from " + getTableName("XTY_TARIFF_UPDATE") + " t1");
            sql.append("left join " + getTableName("XTY_TARIFF_UPDATE_DETAIL") + " t2 on t2.TARIFF_UPDATE_ID=t1.ID");
            sql.append("where 1=1");
            sql.append(param.getString("tariffId"), "and t1.BUSI_ID=?", false);
            return queryForList(sql.getSQL(), sql.getParams());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    /**
     * 资费记录列表 - 从ES查询
     */
    @InfAuthCheck(resId = "cx-xty-tariff-list")
    @WebControl(name = "tariffRecordListNew", type = Types.LIST)
    public JSONObject tariffRecordListNew() {
        try {
            UserModel user = UserUtil.getUser(request);
            String deptGroupType = user.getDeptGroupType();

            // 如果前端传入了reportNo，则只根据reportNo查询，不需要其他条件
            String reportNo = param.getString("reportNo");
            if (StringUtils.isNotBlank(reportNo)) {
                return queryTariffByReportNo(reportNo);
            }

            // 构建ES查询参数
            JSONObject queryParams = buildEsQueryParams(user, deptGroupType);

            // 直接调用ElasticsearchKit.search查询ES
            logger.info("[tariffRecordListNew] queryParams: {}", JSON.toJSONString(queryParams));
            JSONObject esResult = ElasticsearchKit.search(
                    Constants.XTY_TARIFF_BAK_INFO_INDEX,
                    queryParams
            );

            // 解析ES查询结果
            return processEsResult(esResult);
        } catch (Exception e) {
            logger.error("[tariffRecordListNew] Error: {}", e.getMessage(), e);
            return EasyResult.fail("查询ES索引失败: " + e.getMessage());
        }
    }

    /**
     * 根据reportNo查询资费信息
     */
    private JSONObject queryTariffByReportNo(String reportNo) {
        try {
            UserModel user = UserUtil.getUser(request);
            String deptGroupType = user.getDeptGroupType();

            int pageNumber = 1;
            int pageSize = 15;
            if(StringUtils.isNotBlank(param.getString("pageNumber"))){
                pageNumber = param.getIntValue("pageNumber");
            }
            if(StringUtils.isNotBlank(param.getString("pageSize"))){
                pageSize = param.getIntValue("pageSize");
            }

            // 构建ES查询JSON
            JSONObject queryParams = new JSONObject();
            JSONObject queryObj = new JSONObject();
            JSONObject boolObj = new JSONObject();
            JSONArray mustArray = new JSONArray();

            // 报告编号匹配条件
            JSONObject reportNoMatch = new JSONObject();
            reportNoMatch.put("wildcard", new JSONObject().fluentPut("REPORT_NO", "*" + reportNo + "*"));
            mustArray.add(reportNoMatch);

            // 省企业权限控制
            if (StringUtils.startsWith(deptGroupType, com.yunqu.tariff.base.Constants.GROUP_TYPE_PE)) {
                String provinceCode = user.getDeptProvinceCode();
                if (StringUtils.isNotBlank(provinceCode)) {
                    JSONObject provinceQuery = new JSONObject();
                    JSONArray provinceOrArray = new JSONArray();

                    // 使用PROVINCE_CODES数组字段进行查询
                    JSONObject provinceCodesTerms = new JSONObject();
                    provinceCodesTerms.put("terms", new JSONObject().fluentPut("PROVINCE_CODES", new JSONArray().fluentAdd(provinceCode)));
                    provinceOrArray.add(provinceCodesTerms);

                    // 通用编码
                    JSONObject allProvinceCodesQuery = new JSONObject();
                    allProvinceCodesQuery.put("term", new JSONObject().fluentPut("PROVINCE_CODES", com.yunqu.tariff.base.Constants.AREA_CODE_ALL));
                    provinceOrArray.add(allProvinceCodesQuery);

                    provinceQuery.put("bool", new JSONObject().fluentPut("should", provinceOrArray).fluentPut("minimum_should_match", 1));
                    mustArray.add(provinceQuery);
                }
            }

            boolObj.put("must", mustArray);
            queryObj.put("bool", boolObj);
            queryParams.put("query", queryObj);

            // 设置分页
            queryParams.put("from", (pageNumber - 1) * pageSize);
            queryParams.put("size", pageSize);
            queryParams.put("track_total_hits", true);

            // 排序
            JSONArray sortArray = new JSONArray();
            sortArray.add(new JSONObject().fluentPut("CREATE_TIME.keyword", new JSONObject().fluentPut("order", "desc")));
            queryParams.put("sort", sortArray);

            // 直接调用ElasticsearchKit.search查询ES
            JSONObject esResult = com.yunqu.xty.commonex.kit.ElasticsearchKit.search(
                com.yunqu.tariff.base.Constants.XTY_TARIFF_BAK_INFO_INDEX,
                queryParams
            );

            // 解析ES查询结果
            return processEsResult(esResult);
        } catch (Exception e) {
            logger.error("[queryTariffByReportNo] Error: {}", e.getMessage(), e);
            return EasyResult.fail("根据报告编号查询失败: " + e.getMessage());
        }
    }

    /**
     * 构建ES查询参数
     */
    private JSONObject buildEsQueryParams(UserModel user, String deptGroupType) {
        int pageNumber = 1;
        int pageSize = 15;
        if(StringUtils.isNotBlank(param.getString("pageIndex"))){
            pageNumber = param.getIntValue("pageIndex");
        }
        if(StringUtils.isNotBlank(param.getString("pageSize"))){
            pageSize = param.getIntValue("pageSize");
        }

        // 构建ES查询JSON
        JSONObject queryParams = new JSONObject();
        JSONObject queryObj = new JSONObject();
        JSONObject boolObj = new JSONObject();
        JSONArray mustArray = new JSONArray();

        // ENT_ID和BUSI_ORDER_ID条件
        if (StringUtils.isNotBlank(getEntId())) {
            JSONObject entIdMatch = new JSONObject();
            entIdMatch.put("term", new JSONObject().fluentPut("ENT_ID.keyword", getEntId()));
            mustArray.add(entIdMatch);
        }

        if (StringUtils.isNotBlank(getBusiOrderId())) {
            JSONObject busiOrderIdMatch = new JSONObject();
            busiOrderIdMatch.put("term", new JSONObject().fluentPut("BUSI_ORDER_ID.keyword", getBusiOrderId()));
            mustArray.add(busiOrderIdMatch);
        }

        // 省企业权限控制
        if (StringUtils.startsWith(deptGroupType, com.yunqu.tariff.base.Constants.GROUP_TYPE_PE)) {
            String provinceCode = user.getDeptProvinceCode();
            if (StringUtils.isNotBlank(provinceCode)) {
                JSONObject provinceQuery = new JSONObject();
                JSONArray provinceOrArray = new JSONArray();

                JSONObject provinceCodesTerms = new JSONObject();
                provinceCodesTerms.put("terms", new JSONObject().fluentPut("PROVINCE_CODES", new JSONArray().fluentAdd(provinceCode)));
                provinceOrArray.add(provinceCodesTerms);

                // 通用编码
                JSONObject allProvinceCodesQuery = new JSONObject();
                allProvinceCodesQuery.put("term", new JSONObject().fluentPut("PROVINCE_CODES", com.yunqu.tariff.base.Constants.AREA_CODE_ALL));
                provinceOrArray.add(allProvinceCodesQuery);

                provinceQuery.put("bool", new JSONObject().fluentPut("should", provinceOrArray).fluentPut("minimum_should_match", 1));
                mustArray.add(provinceQuery);
            }
        }

        // 资费类型
        String type = param.getString("type");
        if (com.yunqu.tariff.base.Constants.TARIFF_ATTR_01.equals(type)) { // 全国
            JSONObject typeMatch = new JSONObject();
            typeMatch.put("term", new JSONObject().fluentPut("TARIFF_ATTR", com.yunqu.tariff.base.Constants.TARIFF_ATTR_01));
            mustArray.add(typeMatch);
        } else if (com.yunqu.tariff.base.Constants.TARIFF_ATTR_02.equals(type)) {  // 省内
            JSONObject typeQuery = new JSONObject();
            JSONArray typeOrArray = new JSONArray();

            JSONObject type2Match = new JSONObject();
            type2Match.put("term", new JSONObject().fluentPut("TARIFF_ATTR", com.yunqu.tariff.base.Constants.TARIFF_ATTR_02));
            typeOrArray.add(type2Match);

            JSONObject type3Match = new JSONObject();
            type3Match.put("term", new JSONObject().fluentPut("TARIFF_ATTR", com.yunqu.tariff.base.Constants.TARIFF_ATTR_03));
            typeOrArray.add(type3Match);

            typeQuery.put("bool", new JSONObject().fluentPut("should", typeOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(typeQuery);
        }

        // 添加其他查询条件
        addEsSearchConditions(mustArray);

        boolObj.put("must", mustArray);
        queryObj.put("bool", boolObj);
        queryParams.put("query", queryObj);

        // 设置分页
        queryParams.put("from", (pageNumber - 1) * pageSize);
        queryParams.put("size", pageSize);
        queryParams.put("track_total_hits", true);

        JSONArray sortArray = new JSONArray();

        // 第一排序字段：date_id 降序
        JSONObject dateIdSort = new JSONObject();
        dateIdSort.put("CREATE_TIME.keyword", new JSONObject().fluentPut("order", "desc"));
        sortArray.add(dateIdSort);

        // 第二排序字段：_id 降序
        JSONObject idSort = new JSONObject();
        idSort.put("ID.keyword", new JSONObject().fluentPut("order", "desc"));
        sortArray.add(idSort);

        queryParams.put("sort", sortArray);

        return queryParams;
    }

    /**
     * 添加其他通用查询条件
     */
    private void addEsSearchConditions(JSONArray mustArray) {
        // 企业
        String ent = param.getString("ent");
        if (StringUtils.isNotBlank(ent)) {
            JSONObject entMatch = new JSONObject();
            entMatch.put("term", new JSONObject().fluentPut("ENT", ent));
            mustArray.add(entMatch);
        }

        // 是否公示
        String isPublic = param.getString("isPublic");
        if (StringUtils.isNotBlank(isPublic)) {
            JSONObject entMatch = new JSONObject();
            entMatch.put("term", new JSONObject().fluentPut("IS_PUBLIC.keyword", isPublic));
            mustArray.add(entMatch);
        }

        //公示版本
        String version = param.getString("publicVersions");
        if (org.easitline.common.utils.string.StringUtils.isNotBlank(version)) {
            JSONObject query = new JSONObject();
            query.put("terms", new JSONObject().fluentPut("PUBLIC_VERSIONS.keyword", new JSONArray().fluentAdd(version)));
            mustArray.add(query);
        }

        // 备案方类型
        String reporterType = param.getString("reporterType");
        if (StringUtils.isNotBlank(reporterType)) {
            JSONObject reporterTypeMatch = new JSONObject();
            reporterTypeMatch.put("term", new JSONObject().fluentPut("REPORTER_TYPE", reporterType));
            mustArray.add(reporterTypeMatch);
        }

        // 资费状态
        String status = param.getString("status");
        if (StringUtils.isNotBlank(status)) {
            JSONObject statusQuery = new JSONObject();
            JSONArray statusOrArray = new JSONArray();

            for (String statusCode : status.split(",")) {
                if (StringUtils.isNotBlank(statusCode)) {
                    JSONObject statusMatch = new JSONObject();
                    statusMatch.put("term", new JSONObject().fluentPut("STATUS", statusCode));
                    statusOrArray.add(statusMatch);
                }
            }

            statusQuery.put("bool", new JSONObject().fluentPut("should", statusOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(statusQuery);
        }

        // 适用省份和地区过滤
        String provinceCode = param.getString("provinceCode");
        String areaCode = param.getString("areaCode");

        // 处理省份查询
        if (StringUtils.isNotBlank(provinceCode)) {
            JSONObject provinceQuery = new JSONObject();
            JSONArray provinceOrArray = new JSONArray();

            // 从AREA_LIST中提取PROVINCE_CODE进行查询
            String[] provinceCodes = provinceCode.split(",");
            JSONArray provinceTermsArray = new JSONArray();
            for (String pCode : provinceCodes) {
                if (StringUtils.isNotBlank(pCode)) {
                    provinceTermsArray.add(pCode);
                }
            }

            if (provinceTermsArray.size() > 0) {
                // 使用PROVINCE_CODES数组字段进行查询
                JSONObject provinceCodesTerms = new JSONObject();
                provinceCodesTerms.put("terms", new JSONObject().fluentPut("PROVINCE_CODES", provinceTermsArray));
                provinceOrArray.add(provinceCodesTerms);
            }

            // 添加通用编码
            JSONObject allProvinceCodesQuery = new JSONObject();
            allProvinceCodesQuery.put("term", new JSONObject().fluentPut("PROVINCE_CODES", com.yunqu.tariff.base.Constants.AREA_CODE_ALL));
            provinceOrArray.add(allProvinceCodesQuery);

            provinceQuery.put("bool", new JSONObject().fluentPut("should", provinceOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(provinceQuery);
        }

            // 处理地区查询
    if (StringUtils.isNotBlank(areaCode)) {
        JSONObject areaQuery = new JSONObject();
        JSONArray areaOrArray = new JSONArray();

        // 将地区代码拆分为数组
        String[] areaCodes = areaCode.split(",");
        JSONArray areaCodesArray = new JSONArray();
        for (String aCode : areaCodes) {
            if (StringUtils.isNotBlank(aCode)) {
                areaCodesArray.add(aCode);
            }
        }

        if (areaCodesArray.size() > 0) {
            // 使用AREA_CODES数组字段进行查询
            JSONObject areaCodesTerms = new JSONObject();
            areaCodesTerms.put("terms", new JSONObject().fluentPut("AREA_CODES", areaCodesArray));
            areaOrArray.add(areaCodesTerms);
        }

        // 添加通用编码
        JSONObject allAreaCodesQuery = new JSONObject();
        allAreaCodesQuery.put("term", new JSONObject().fluentPut("AREA_CODES", com.yunqu.tariff.base.Constants.AREA_CODE_ALL));
        areaOrArray.add(allAreaCodesQuery);

        areaQuery.put("bool", new JSONObject().fluentPut("should", areaOrArray).fluentPut("minimum_should_match", 1));
        mustArray.add(areaQuery);
    }

        // 名称
        String name = param.getString("name");
        if (StringUtils.isNotBlank(name)) {
            JSONObject nameMatch = new JSONObject();
            nameMatch.put("wildcard", new JSONObject().fluentPut("NAME", "*" + name + "*"));
            mustArray.add(nameMatch);
        }

        // 序列号
        String seqNo = param.getString("seq_no");
        if (StringUtils.isNotBlank(seqNo)) {
            JSONObject seqNoMatch = new JSONObject();
            seqNoMatch.put("wildcard", new JSONObject().fluentPut("SEQ_NO.keyword", "*" + seqNo + "*"));
            mustArray.add(seqNoMatch);
        }

        // 资费属性
        String tariff_attr = param.getString("tariff_attr");
        if (StringUtils.isNotBlank(tariff_attr)) {
            JSONObject tariffAttrQuery = new JSONObject();
            JSONArray tariffAttrOrArray = new JSONArray();

            for (String attrCode : tariff_attr.split(",")) {
                if (StringUtils.isNotBlank(attrCode)) {
                    JSONObject attrMatch = new JSONObject();
                    attrMatch.put("term", new JSONObject().fluentPut("TARIFF_ATTR", attrCode));
                    tariffAttrOrArray.add(attrMatch);
                }
            }

            tariffAttrQuery.put("bool", new JSONObject().fluentPut("should", tariffAttrOrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(tariffAttrQuery);
        }

        // 备案号
        String reportNo = param.getString("reportNo");
        if (StringUtils.isNotBlank(reportNo)) {
            JSONObject reportNoMatch = new JSONObject();
            reportNoMatch.put("wildcard", new JSONObject().fluentPut("REPORT_NO", "*" + reportNo + "*"));
            mustArray.add(reportNoMatch);
        }

        // 备案主体
        String reporter = param.getString("reporter");
        if (StringUtils.isNotBlank(reporter)) {
            if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) {
                JSONObject entMatch = new JSONObject();
                entMatch.put("term", new JSONObject().fluentPut("ENT", reporter));
                mustArray.add(entMatch);
            } else {
                JSONObject reporterMatch = new JSONObject();
                reporterMatch.put("wildcard", new JSONObject().fluentPut("REPORTER.keyword", "*" + reporter + "*"));
                mustArray.add(reporterMatch);
            }
        }

        // 备案者
        String reportObj = param.getString("reportObj");
        if (StringUtils.isNotBlank(reportObj)) {
            JSONObject reportObjMatch = new JSONObject();
            reportObjMatch.put("wildcard", new JSONObject().fluentPut("REPORT_OBJ.keyword", "*" + reportObj + "*"));
            mustArray.add(reportObjMatch);
        }

        // 资费标准范围 - 优化FEES字段查询
        String feesBegin = param.getString("feesBegin");
        String feesEnd = param.getString("feesEnd");

        logger.info("[tariffRecordListNew] 查询资费范围: feesBegin={}, feesEnd={}", feesBegin, feesEnd);

        if (StringUtils.isNotBlank(feesBegin) || StringUtils.isNotBlank(feesEnd)) {
            // 创建bool查询用于组合多个条件
            JSONObject boolQuery = new JSONObject();
            JSONArray shouldArray = new JSONArray();

            // 使用优化后的FEES查询构建方法
            JSONObject feesQuery = buildFeesRegexQuery(feesBegin, feesEnd);
            if (feesQuery != null) {
                shouldArray.add(feesQuery);
                logger.info("[tariffRecordListNew] 主要FEES查询条件: {}", feesQuery.toJSONString());
            } else {
                // 如果buildFeesRegexQuery返回null，回退到基本查询
                // 1. 尝试数值范围查询
                try {
                    JSONObject numericRangeQuery = new JSONObject();
                    JSONObject numericRangeObj = new JSONObject();
                    boolean hasNumericRange = false;

                    if (StringUtils.isNotBlank(feesBegin)) {
                        double beginValue = Double.parseDouble(feesBegin);
                        numericRangeObj.put("gte", beginValue);
                        hasNumericRange = true;
                    }

                    if (StringUtils.isNotBlank(feesEnd)) {
                        double endValue = Double.parseDouble(feesEnd);
                        numericRangeObj.put("lte", endValue);
                        hasNumericRange = true;
                    }

                    if (hasNumericRange) {
                        numericRangeQuery.put("range", new JSONObject().fluentPut("FEES", numericRangeObj));
                        shouldArray.add(numericRangeQuery);
                    }
                } catch (NumberFormatException e) {
                    logger.warn("[tariffRecordListNew] 资费值解析为数值失败: {}", e.getMessage());
                }

                // 2. 对于精确查询，添加特殊处理
                if (StringUtils.isNotBlank(feesBegin) && StringUtils.isNotBlank(feesEnd) && feesBegin.equals(feesEnd)) {
                    // 精确匹配，既尝试原值也尝试带小数点的形式
                    JSONObject termsQuery = new JSONObject();
                    JSONArray termValues = new JSONArray();
                    termValues.add(feesBegin);

                    // 如果没有小数点，添加.0形式
                    if (!feesBegin.contains(".")) {
                        termValues.add(feesBegin + ".0");
                    }

                    termsQuery.put("terms", new JSONObject().fluentPut("FEES.str", termValues));
                    shouldArray.add(termsQuery);
                }

                // 3. 使用字符串匹配作为备选
                JSONObject strRangeQuery = new JSONObject();
                JSONObject strRangeObj = new JSONObject();

                if (StringUtils.isNotBlank(feesBegin)) {
                    strRangeObj.put("gte", feesBegin);
                }

                if (StringUtils.isNotBlank(feesEnd)) {
                    strRangeObj.put("lte", feesEnd);
                }

                strRangeQuery.put("range", new JSONObject().fluentPut("FEES.str", strRangeObj));
                shouldArray.add(strRangeQuery);
            }

            // 如果有足够的查询条件，添加bool查询
            if (shouldArray.size() > 0) {
                boolQuery.put("bool", new JSONObject()
                    .fluentPut("should", shouldArray)
                    .fluentPut("minimum_should_match", 1));

                mustArray.add(boolQuery);

                logger.info("[tariffRecordListNew] 最终费用范围查询条件: {}", boolQuery.toJSONString());
            }
        }

        // 适用范围
        String applicablePeople = param.getString("applicablePeople");
        if (StringUtils.isNotBlank(applicablePeople)) {
            JSONObject applicablePeopleMatch = new JSONObject();
            applicablePeopleMatch.put("wildcard", new JSONObject().fluentPut("APPLICABLE_PEOPLE.keyword", "*" + applicablePeople + "*"));
            mustArray.add(applicablePeopleMatch);
        }

        // 退订方式
        String unsubscribe = param.getString("unsubscribe");
        if (StringUtils.isNotBlank(unsubscribe)) {
            JSONObject unsubscribeMatch = new JSONObject();
            unsubscribeMatch.put("wildcard", new JSONObject().fluentPut("UNSUBSCRIBE.keyword", "*" + unsubscribe + "*"));
            mustArray.add(unsubscribeMatch);
        }

        // 违约责任
        String responsibility = param.getString("responsibility");
        if (StringUtils.isNotBlank(responsibility)) {
            JSONObject responsibilityMatch = new JSONObject();
            responsibilityMatch.put("wildcard", new JSONObject().fluentPut("RESPONSIBILITY.keyword", "*" + responsibility + "*"));
            mustArray.add(responsibilityMatch);
        }

        // 其他服务内容
        String otherContent = param.getString("otherContent");
        if (StringUtils.isNotBlank(otherContent)) {
            JSONObject otherContentMatch = new JSONObject();
            otherContentMatch.put("wildcard", new JSONObject().fluentPut("OTHER_CONTENT.keyword", "*" + otherContent + "*"));
            mustArray.add(otherContentMatch);
        }

        // 其他事项
        String others = param.getString("others");
        if (StringUtils.isNotBlank(others)) {
            JSONObject othersMatch = new JSONObject();
            othersMatch.put("wildcard", new JSONObject().fluentPut("OTHERS.keyword", "*" + others + "*"));
            mustArray.add(othersMatch);
        }

        // 销售渠道
        String channel = param.getString("channel");
        if (StringUtils.isNotBlank(channel)) {
            JSONObject channelMatch = new JSONObject();
            channelMatch.put("wildcard", new JSONObject().fluentPut("CHANNEL.keyword", "*" + channel + "*"));
            mustArray.add(channelMatch);
        }

        // 是否通信类
        String is_telecom = param.getString("is_telecom");
        if (StringUtils.isNotBlank(is_telecom)) {
            JSONObject isTelecomMatch = new JSONObject();
            isTelecomMatch.put("term", new JSONObject().fluentPut("IS_TELECOM", is_telecom));
            mustArray.add(isTelecomMatch);
        }

        // 超出资费
        String extra_fees = param.getString("extra_fees");
        if (StringUtils.isNotBlank(extra_fees)) {
            JSONObject extraFeesMatch = new JSONObject();
            extraFeesMatch.put("wildcard", new JSONObject().fluentPut("EXTRA_FEES.keyword", "*" + extra_fees + "*"));
            mustArray.add(extraFeesMatch);
        }

        // 其他费用
        String other_fees = param.getString("other_fees");
        if (StringUtils.isNotBlank(other_fees)) {
            JSONObject otherFeesMatch = new JSONObject();
            otherFeesMatch.put("wildcard", new JSONObject().fluentPut("OTHER_FEES.keyword", "*" + other_fees + "*"));
            mustArray.add(otherFeesMatch);
        }

        // 一级分类
        String type1 = param.getString("type1");
        if (StringUtils.isNotBlank(type1)) {
            JSONObject type1Query = new JSONObject();
            JSONArray type1OrArray = new JSONArray();

            for (String code : type1.split(",")) {
                if (StringUtils.isNotBlank(code)) {
                    JSONObject typeMatch = new JSONObject();
                    typeMatch.put("term", new JSONObject().fluentPut("TYPE1", code));
                    type1OrArray.add(typeMatch);
                }
            }

            type1Query.put("bool", new JSONObject().fluentPut("should", type1OrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(type1Query);
        }

        // 二级分类
        String type2 = param.getString("type2");
        if (StringUtils.isNotBlank(type2)) {
            JSONObject type2Query = new JSONObject();
            JSONArray type2OrArray = new JSONArray();

            for (String code : type2.split(",")) {
                if (StringUtils.isNotBlank(code)) {
                    JSONObject typeMatch = new JSONObject();
                    typeMatch.put("term", new JSONObject().fluentPut("TYPE2", code));
                    type2OrArray.add(typeMatch);
                }
            }

            type2Query.put("bool", new JSONObject().fluentPut("should", type2OrArray).fluentPut("minimum_should_match", 1));
            mustArray.add(type2Query);
        }

        // 资费单位
        String fees_unit = param.getString("fees_unit");
        if (StringUtils.isNotBlank(fees_unit)) {
            JSONObject feesUnitMatch = new JSONObject();
            feesUnitMatch.put("wildcard", new JSONObject().fluentPut("FEES_UNIT.keyword", "*" + fees_unit + "*"));
            mustArray.add(feesUnitMatch);
        }

        // 有效期限
        String valid_period = param.getString("valid_period");
        if (StringUtils.isNotBlank(valid_period)) {
            JSONObject validPeriodMatch = new JSONObject();
            validPeriodMatch.put("wildcard", new JSONObject().fluentPut("VALID_PERIOD.keyword", "*" + valid_period + "*"));
            mustArray.add(validPeriodMatch);
        }

        // 在网要求
        String duration = param.getString("duration");
        if (StringUtils.isNotBlank(duration)) {
            JSONObject durationMatch = new JSONObject();
            durationMatch.put("wildcard", new JSONObject().fluentPut("DURATION.keyword", "*" + duration + "*"));
            mustArray.add(durationMatch);
        }

        // 别名
        String tariff_another_name = param.getString("tariff_another_name");
        if (StringUtils.isNotBlank(tariff_another_name)) {
            JSONObject anotherNameMatch = new JSONObject();
            anotherNameMatch.put("wildcard", new JSONObject().fluentPut("TARIFF_ANOTHER_NAME.keyword", "*" + tariff_another_name + "*"));
            mustArray.add(anotherNameMatch);
        }

        // 版本号
        String versionNum = param.getString("versionNum");
        if (StringUtils.isNotBlank(versionNum)) {
            if ("0".equals(versionNum)) {
                JSONObject versionNumMatch = new JSONObject();
                versionNumMatch.put("term", new JSONObject().fluentPut("VERSION_NUM.keyword", "V0"));
                mustArray.add(versionNumMatch);
            } else {
                // 不等于V0的版本号
                JSONObject versionNumNotMatch = new JSONObject();
                versionNumNotMatch.put("bool", new JSONObject().fluentPut("must_not",
                    new JSONArray().fluentAdd(new JSONObject().fluentPut("term",
                        new JSONObject().fluentPut("VERSION_NUM.keyword", "V0")))));
                mustArray.add(versionNumNotMatch);
            }
        }

        // 上线日期
        String onlineDayStart = param.getString("onlineDayStart");
        if (StringUtils.isNotBlank(onlineDayStart)) {
            JSONObject onlineDayStartMatch = new JSONObject();
            onlineDayStartMatch.put("range", new JSONObject().fluentPut("ONLINE_DAY",
                new JSONObject().fluentPut("gte", onlineDayStart.replaceAll("-", ""))));
            mustArray.add(onlineDayStartMatch);
        }

        String onlineDayEnd = param.getString("onlineDayEnd");
        if (StringUtils.isNotBlank(onlineDayEnd)) {
            JSONObject onlineDayEndMatch = new JSONObject();
            onlineDayEndMatch.put("range", new JSONObject().fluentPut("ONLINE_DAY",
                new JSONObject().fluentPut("lte", onlineDayEnd.replaceAll("-", ""))));
            mustArray.add(onlineDayEndMatch);
        }

        // 下线日期
        String offlineDayStart = param.getString("offlineDayStart");
        if (StringUtils.isNotBlank(offlineDayStart)) {
            JSONObject offlineDayStartMatch = new JSONObject();
            offlineDayStartMatch.put("range", new JSONObject().fluentPut("OFFLINE_DAY",
                new JSONObject().fluentPut("gte", offlineDayStart.replaceAll("-", ""))));
            mustArray.add(offlineDayStartMatch);
        }

        String offlineDayEnd = param.getString("offlineDayEnd");
        if (StringUtils.isNotBlank(offlineDayEnd)) {
            JSONObject offlineDayEndMatch = new JSONObject();
            offlineDayEndMatch.put("range", new JSONObject().fluentPut("OFFLINE_DAY",
                new JSONObject().fluentPut("lte", offlineDayEnd.replaceAll("-", ""))));
            mustArray.add(offlineDayEndMatch);
        }

        // 创建时间
        String createTime = param.getString("createTime");
        if (StringUtils.isNotBlank(createTime) && createTime.contains("~")) {
            String[] createTimeArray = createTime.split("~");
            if (createTimeArray.length == 2) {
                JSONObject createTimeStartMatch = new JSONObject();
                createTimeStartMatch.put("range", new JSONObject().fluentPut("CREATE_TIME.keyword",
                    new JSONObject().fluentPut("gte", createTimeArray[0])));
                mustArray.add(createTimeStartMatch);

                JSONObject createTimeEndMatch = new JSONObject();
                createTimeEndMatch.put("range", new JSONObject().fluentPut("CREATE_TIME.keyword",
                    new JSONObject().fluentPut("lte", createTimeArray[1])));
                mustArray.add(createTimeEndMatch);
            }
        }

        // 修改时间
        String updateTime = param.getString("updateTime");
        if (StringUtils.isNotBlank(updateTime) && updateTime.contains("~")) {
            String[] updateTimeArray = updateTime.split("~");
            if (updateTimeArray.length == 2) {
                JSONObject updateTimeStartMatch = new JSONObject();
                updateTimeStartMatch.put("range", new JSONObject().fluentPut("UPDATE_TIME.keyword",
                    new JSONObject().fluentPut("gte", updateTimeArray[0])));
                mustArray.add(updateTimeStartMatch);

                JSONObject updateTimeEndMatch = new JSONObject();
                updateTimeEndMatch.put("range", new JSONObject().fluentPut("UPDATE_TIME.keyword",
                    new JSONObject().fluentPut("lte", updateTimeArray[1])));
                mustArray.add(updateTimeEndMatch);
            }
        }

        // 资费规则核查时间
        String fieldCheckTime = param.getString("fieldCheckTime");
        if (StringUtils.isNotBlank(fieldCheckTime) && fieldCheckTime.contains("~")) {
            String[] fieldCheckTimeArray = fieldCheckTime.split("~");
            if (fieldCheckTimeArray.length == 2) {
                JSONObject fieldCheckTimeStartMatch = new JSONObject();
                fieldCheckTimeStartMatch.put("range", new JSONObject().fluentPut("FIELD_CHECK_TIME.keyword",
                    new JSONObject().fluentPut("gte", fieldCheckTimeArray[0])));
                mustArray.add(fieldCheckTimeStartMatch);

                JSONObject fieldCheckTimeEndMatch = new JSONObject();
                fieldCheckTimeEndMatch.put("range", new JSONObject().fluentPut("FIELD_CHECK_TIME.keyword",
                    new JSONObject().fluentPut("lte", fieldCheckTimeArray[1])));
                mustArray.add(fieldCheckTimeEndMatch);
            }
        }
        // 资费字段编号
        String fieldCheckNo = param.getString("fieldCheckNo");
        if (StringUtils.isNotBlank(fieldCheckNo)) {
            JSONArray fileCheckNoArray = JSONArray.parseArray("[" + fieldCheckNo + "]");
            mustArray.add(new JSONObject()
                    .fluentPut("terms", new JSONObject().fluentPut("FIELD_CHECK_NOS.keyword", fileCheckNoArray)));
        }
    }

    /**
     * 处理ES查询结果
     */
    private JSONObject processEsResult(JSONObject esResult) {
        try {
            JSONObject result = new JSONObject();
            int pageNumber = 1;
            int pageSize = 15;
            if(StringUtils.isNotBlank(param.getString("pageIndex"))){
                pageNumber = param.getIntValue("pageIndex");
            }
            if(StringUtils.isNotBlank(param.getString("pageSize"))){
                pageSize = param.getIntValue("pageSize");
            }

            if (esResult == null || !esResult.containsKey("hits")) {
                result.put("data", new ArrayList<>());
                result.put("state", 1);
                result.put("msg", "请求成功，未找到数据");
                result.put("pageNumber", pageNumber);
                result.put("pageSize", pageSize);
                result.put("totalPage", 0);
                result.put("totalRow", 0);
                result.put("pageType", 3);
                return result;
            }

            JSONObject hitsObject = esResult.getJSONObject("hits");
            int totalHits = hitsObject.getJSONObject("total").getIntValue("value");
            int totalPage = (totalHits + pageSize - 1) / pageSize;

            // 提取记录列表
            JSONArray hitsArray = hitsObject.getJSONArray("hits");
            List<JSONObject> recordList = new ArrayList<>();

            for (int i = 0; i < hitsArray.size(); i++) {
                JSONObject hit = hitsArray.getJSONObject(i);
                JSONObject source = hit.getJSONObject("_source");
                if(!source.containsKey("FEES")){
                    source.put("FEES", "-");
                }
                recordList.add(source);
            }

            result.put("data", recordList);
            result.put("state", 1);
            result.put("msg", "查询成功!");
            result.put("pageNumber", pageNumber);
            result.put("pageSize", pageSize);
            result.put("totalPage", totalPage);
            result.put("totalRow", totalHits);
            result.put("pageType", 3);

            return result;
        } catch (Exception e) {
            logger.error("[processEsResult] Error processing ES result: {}", e.getMessage(), e);
            return EasyResult.fail("处理ES查询结果异常: " + e.getMessage());
        }
    }

    /**
     * 将AREA_LIST转换为树形结构
     */
    private List<JSONObject> convertAreaListToTree(List<JSONObject> areaList) {
        Map<String, JSONObject> provinceMap = new HashMap<>();

        // 提取所有省份
        for (JSONObject area : areaList) {
            String provinceCode = area.getString("PROVINCE_CODE");
            String provinceName = area.getString("PROVINCE_NAME");

            if (StringUtils.isNotBlank(provinceCode) && !provinceMap.containsKey(provinceCode)) {
                JSONObject province = new JSONObject();
                province.put("value", provinceCode);
                province.put("label", provinceName);
                province.put("children", new JSONArray());
                provinceMap.put(provinceCode, province);
            }
        }

        // 将地区添加到对应省份下
        for (JSONObject area : areaList) {
            String provinceCode = area.getString("PROVINCE_CODE");
            String areaCode = area.getString("AREA_CODE");
            String areaName = area.getString("AREA_NAME");

            if (StringUtils.isNotBlank(provinceCode) && StringUtils.isNotBlank(areaCode) &&
                    provinceMap.containsKey(provinceCode)) {
                JSONObject areaObj = new JSONObject();
                areaObj.put("value", areaCode);
                areaObj.put("label", areaName);

                JSONObject province = provinceMap.get(provinceCode);
                JSONArray children = province.getJSONArray("children");
                children.add(areaObj);
            }
        }

        return new ArrayList<>(provinceMap.values());
    }

    @WebControl(name = "tariffReauditLogList", type = Types.LIST)
    public JSONObject tariffReauditLogList() {
        try {
            EasySQL sql = new EasySQL();
            sql.append("select * ");
            sql.append("from " + getTableName("xty_tariff_oper_log") );
            sql.append("where 1=1");
            sql.appendLike(param.getString("createName"), "and CREATE_NAME like ?");
            sql.appendLike(param.getString("tariffName"), "and TARIFF_NAME like  ?");
            sql.append(param.getString("provinceCode"), "and PROVINCE_CODE = ?");
            sql.append(param.getString("ent"), "and ENT = ?");
            String createTime = param.getString("createTime");
            if (StringUtils.isNotBlank(createTime) && createTime.contains("~")) {
                String[] createTimeArray = createTime.split("~");
                sql.append(createTimeArray[0], "and CREATE_TIME >= ?");
                sql.append(createTimeArray[1], "and CREATE_TIME <= ?");
            }
            return queryForList(sql.getSQL(), sql.getParams());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    /**
     * 企业各类资费数量分布
     */
    @InfAuthCheck(resId = "cx-xty-statistics-count")
    @WebControl(name = "findDistributionTotal", type = Types.LIST)
    public JSONObject findDistributionTotal() {
        try {
            EasySQL sql = new EasySQL();
            sql.append("select * from (");
            sql.append("select t1.TYPE2,");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_001 + "' then 1 else 0 end) TELECOM, ");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_002 + "' then 1 else 0 end) MOBILE, ");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_003 + "' then 1 else 0 end) UNICOM, ");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_004 + "' then 1 else 0 end) BROAD, ");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_001 + "' then 1 else 0 end) +");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_002 + "' then 1 else 0 end) +");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_003 + "' then 1 else 0 end) +");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_004 + "' then 1 else 0 end) ALLTOTAL ");
            sql.append("from " + getTableName("XTY_TARIFF_RECORD") + " t1");
            sql.append("where 1=1");
            sql.append(DictConstants.DICT_SY_YN_N, "and t1.IS_HISTORY=?");
            //适用地区
            String areaCode = param.getString("areaCode");
            String provinceCode = param.getString("provinceCode");
            if (StringUtils.isNotBlank(provinceCode) || StringUtils.isNotBlank(areaCode)) {
                sql.append("and exists ( ");
                sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
                sql.append("where tt.TARIFF_RECORD_ID = t1.ID");
                if (StringUtils.isNotBlank(provinceCode)) {
                    provinceCode = provinceCode + ",000";
                    sql.appendIn(provinceCode.split(","), "AND tt.PROVINCE_CODE");
                }
                if (StringUtils.isNotBlank(areaCode)) {
                    areaCode = areaCode + ",000";
                    sql.appendIn(areaCode.split(","), "AND tt.AREA_CODE");
                }
                sql.append(") ");
            }
            //一级分类
            String type1 = param.getString("type1");
            if (StringUtils.isNotBlank(type1)) {
                sql.append("and ( t1.TYPE1 in ('-1'");
                for (String code : type1.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            //二级分类
            String type2 = param.getString("type2");
            if (StringUtils.isNotBlank(type2)) {
                sql.append("and ( t1.TYPE2 in ('-1'");
                for (String code : type2.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 资费属性
            String tariff_attr = param.getString("tariff_attr");
            if (StringUtils.isNotBlank(tariff_attr)) {
                sql.append("and ( t1.TARIFF_ATTR in ('-1'");
                for (String code : tariff_attr.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 备案主体-
            // 备案主体
//            String reporter = param.getString("reporter");
//            if ("001".compareTo(reporter) <= 0 && "005".compareTo(reporter) >= 0) { //reporter在"001"到"005"之间
//                sql.append("005".equals(reporter) ? "004" : reporter, "and t1.ENT = ?");
//            } else {
//                sql.appendLike(reporter, "and t1.REPORTER like ?");
//            }
            String reporter = param.getString("reporter");
            sql.appendLike(reporter, "and t1.REPORTER like ?");
//            if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) { //reporter在"1"到"5"之间
//                sql.append(reporter, "and t1.ENT = ?");
//            } else {
//                sql.appendLike(reporter, "and t1.REPORTER like ?");
//            }
            //上线日期
            String onlineDay = param.getString("onlineDay");
            if (StringUtils.isNotBlank(onlineDay) && onlineDay.contains("~")) {
                String[] onlineDayArray = onlineDay.split("~");
                sql.append(onlineDayArray[0].replaceAll("-", ""), "and t1.ONLINE_DAY >= ?");
                sql.append(onlineDayArray[1].replaceAll("-", ""), "and t1.ONLINE_DAY <= ?");
            }
            //下线日期
            String offlineDay = param.getString("offlineDay");
            if (StringUtils.isNotBlank(offlineDay) && offlineDay.contains("~")) {
                String[] offlineDayArray = offlineDay.split("~");
                sql.append(offlineDayArray[0].replaceAll("-", ""), "and t1.OFFLINE_DAY >= ?");
                sql.append(offlineDayArray[1].replaceAll("-", ""), "and t1.OFFLINE_DAY <= ?");
            }
            //资费状态
            String status = param.getString("status");
            if (StringUtils.isNotBlank(status)) {
                sql.append("and ( t1.STATUS in ('-1'");
                for (String code : status.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 资费名称
            sql.appendLike(param.getString("name"), "and t1.NAME like ?");
            //备案者
            // sql.appendRLike(param.getString("reportObj"), "and t1.REPORT_OBJ like ?");
            // 资费标准
            sql.append(param.getString("feesBegin"), "and t1.FEES >= ?", true);
            sql.append(param.getString("feesEnd"), "and t1.FEES <=?", true);
            // 资费单位 fees_unit
            sql.appendLike(param.getString("fees_unit"), "and t1.FEES_UNIT like ?");

            sql.append("group by t1.TYPE2 ");
            sql.append("union all");
            sql.append("select '合计' TYPE2,");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_001 + "' then 1 else 0 end) TELECOM, ");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_002 + "' then 1 else 0 end) MOBILE, ");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_003 + "' then 1 else 0 end) UNICOM, ");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_004 + "' then 1 else 0 end) BROAD, ");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_001 + "' then 1 else 0 end) +");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_002 + "' then 1 else 0 end) +");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_003 + "' then 1 else 0 end) +");
            sql.append("sum(case when t1.ENT='" + Constants.ENT_TYPE_004 + "' then 1 else 0 end) ALLTOTAL ");
            sql.append("from " + getTableName("XTY_TARIFF_RECORD") + " t1");
            sql.append("where 1=1");
            sql.append(DictConstants.DICT_SY_YN_N, "and t1.IS_HISTORY=?");
            //适用地区
            if (StringUtils.isNotBlank(provinceCode) || StringUtils.isNotBlank(areaCode)) {
                sql.append("and exists ( ");
                sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
                sql.append("where tt.TARIFF_RECORD_ID = t1.ID");
                if (StringUtils.isNotBlank(provinceCode)) {
                    provinceCode = provinceCode + ",000";
                    sql.appendIn(provinceCode.split(","), "AND tt.PROVINCE_CODE");
                }
                if (StringUtils.isNotBlank(areaCode)) {
                    areaCode = areaCode + ",000";
                    sql.appendIn(areaCode.split(","), "AND tt.AREA_CODE");
                }
                sql.append(") ");
            }
            //一级分类
            if (StringUtils.isNotBlank(type1)) {
                sql.append("and ( t1.TYPE1 in ('-1'");
                for (String code : type1.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            //二级分类
            if (StringUtils.isNotBlank(type2)) {
                sql.append("and ( t1.TYPE2 in ('-1'");
                for (String code : type2.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 资费属性
            if (StringUtils.isNotBlank(tariff_attr)) {
                sql.append("and ( t1.TARIFF_ATTR in ('-1'");
                for (String code : tariff_attr.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 备案主体
//            if ("001".compareTo(reporter) <= 0 && "005".compareTo(reporter) >= 0) { //reporter在"001"到"005"之间
//                sql.append("005".equals(reporter) ? "004" : reporter, "and t1.ENT = ?");
//            } else {
//                sql.appendLike(reporter, "and t1.REPORTER like ?");
//            }
            sql.appendLike(reporter, "and t1.REPORTER like ?");
//            if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) { //reporter在"1"到"5"之间
//                sql.append(reporter, "and t1.ENT = ?");
//            } else {
//                sql.appendLike(reporter, "and t1.REPORTER like ?");
//            }
            //上线日期
            if (StringUtils.isNotBlank(onlineDay) && onlineDay.contains("~")) {
                String[] onlineDayArray = onlineDay.split("~");
                sql.append(onlineDayArray[0].replaceAll("-", ""), "and t1.ONLINE_DAY >= ?");
                sql.append(onlineDayArray[1].replaceAll("-", ""), "and t1.ONLINE_DAY <= ?");
            }
            //下线日期
            if (StringUtils.isNotBlank(offlineDay) && offlineDay.contains("~")) {
                String[] offlineDayArray = offlineDay.split("~");
                sql.append(offlineDayArray[0].replaceAll("-", ""), "and t1.OFFLINE_DAY >= ?");
                sql.append(offlineDayArray[1].replaceAll("-", ""), "and t1.OFFLINE_DAY <= ?");
            }
            //资费状态
            if (StringUtils.isNotBlank(status)) {
                sql.append("and ( t1.STATUS in ('-1'");
                for (String code : status.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 资费名称
            sql.appendLike(param.getString("name"), "and t1.NAME like ?");
            //备案者
            // sql.appendRLike(param.getString("reportObj"), "and t1.REPORT_OBJ like ?");
            // 资费标准
            sql.append(param.getString("feesBegin"), "and t1.FEES >= ?", true);
            sql.append(param.getString("feesEnd"), "and t1.FEES <=?", true);
            // 资费单位 fees_unit
            sql.appendLike(param.getString("fees_unit"), "and t1.FEES_UNIT like ?");
            sql.append(") temp");
            sql.append("order by TYPE2 asc");
            logger.info("企业各类资费数量分布SQL：{}", sql.getFullSq());
            return queryForPageList(sql.getSQL(), sql.getParams());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    /**
     * 企业资费数量变化--在售、无效、下架、未售
     */
    @InfAuthCheck(resId = "cx-xty-statistics-changes")
    @WebControl(name = "findTimeChangeTotal2", type = Types.LIST)
    public JSONObject findTimeChangeTotal2() {
        try {
            String stType = param.getString("type");
            if (StringUtils.isBlank(stType)) {
                stType = Constants.CYCLE_TYPE_03;
            }
            String stime = param.getString("stime");
            String etime = param.getString("etime");
            String startTime = "";
            String endTime = "";
            String mdtime = "";
            stime = stime.replace("-", "");
            etime = etime.replace("-", "");
            String entId = getEntId();
            EasySQL sql = new EasySQL();
            switch (stType) {
                case "year":  // 年报
                    startTime = stime + "0101";
                    endTime = etime + "1231";
                    String temptime = stime + "0101";
                    mdtime = stime + "1231";
                    stime = temptime;
                    etime = etime + "0101";
                    break;
                case "quarter":  // 季度报
                    String[] time = getTime(stime);
                    startTime = time[0];
                    stime = time[0];
                    mdtime = time[1];
                    String[] time1 = getTime(etime);
                    etime = time1[0];
                    endTime = time1[1];
                    break;
                case "month":  // 月报
                    startTime = stime + "01";
                    endTime = ReportUtils.getCurrMonthEndDay(etime);
                    stime = stime + "01";
                    etime = etime + "01";

                    break;
            }
            sql.append("WITH RECURSIVE date_type AS ( ");
            sql.append(stime, " SELECT   DATE(?) AS start_time, ");
            switch (stType) {
                case "year":  // 年报
                    sql.append(mdtime, " DATE(?) AS end_time ");
                    sql.append(" UNION ALL ");
                    sql.append(" SELECT  DATE_ADD(start_time, INTERVAL 1 YEAR),   LAST_DAY(DATE_ADD(DATE_ADD(start_time, INTERVAL 1 YEAR), INTERVAL 11 MONTH))  ");
                    break;
                case "quarter":  // 季度报
                    sql.append(mdtime, " DATE(?) AS end_time ");
                    sql.append(" UNION ALL ");
                    sql.append(" SELECT  DATE_ADD(start_time, INTERVAL 3 MONTH),  LAST_DAY(DATE_ADD(DATE_ADD(start_time, INTERVAL 1 QUARTER), INTERVAL 2 MONTH)) AS end_time  ");
                    break;
                case "month":  // 月报
                    sql.append(stime, " LAST_DAY(DATE(?)) AS end_time ");
                    sql.append(" UNION ALL ");
                    sql.append(" SELECT  DATE_ADD(start_time, INTERVAL 1 MONTH),  LAST_DAY(DATE_ADD(start_time, INTERVAL 1 MONTH))  ");
                    break;
            }
            sql.append("  FROM date_type ");
            sql.append(etime, "  WHERE start_time < ? ) ");
            sql.append("  SELECT ");
            switch (stType) {
                case "year":  // 年报
                    sql.append("   YEAR(start_time)  AS MONTH, ");
                    break;
                case "quarter":  // 季度报
                    sql.append("   CONCAT(YEAR(start_time), '年', QUARTER(start_time),'季度')  AS MONTH, ");
                    break;
                case "month":  // 月报
                    // sql.append("   DATE_FORMAT(start_time, '%Y年%m月') AS MONTH, ");
                    sql.append("   CONCAT(YEAR(start_time), '年', MONTH(start_time), '月') AS MONTH, ");
                    break;
            }
            sql.append("    SUM(CASE WHEN t.ent = '1' THEN 1 ELSE 0 END) AS TELECOM, ");
            sql.append("    SUM(CASE WHEN t.ent = '2' THEN 1 ELSE 0 END) AS MOBILE, ");
            sql.append("   SUM(CASE WHEN t.ent = '3' THEN 1 ELSE 0 END) AS UNICOM, ");
            sql.append("  SUM(CASE WHEN t.ent = '5' THEN 1 ELSE 0 END) AS BROAD, ");
            sql.append("sum(case when t.ENT='" + Constants.ENT_TYPE_001 + "' then 1 else 0 end) +");
            sql.append("sum(case when t.ENT='" + Constants.ENT_TYPE_002 + "' then 1 else 0 end) +");
            sql.append("sum(case when t.ENT='" + Constants.ENT_TYPE_003 + "' then 1 else 0 end) +");
            sql.append("sum(case when t.ENT='" + Constants.ENT_TYPE_004 + "' then 1 else 0 end) ALLTOTAL ");
            sql.append("  FROM ");
            sql.append("  date_type ");
            sql.append("  LEFT JOIN ( SELECT t.id,t.ent,t.ONLINE_DAY,t.TYPE2,t.OFFLINE_DAY,t.IS_HISTORY,t.DEL_TIME,t.TYPE1,t.TARIFF_ATTR,t.REPORTER,t.NAME,t.FEES,t.FEES_UNIT  FROM " + getTableName("xty_tariff_record") + " t");
            sql.append("  where 1= 1 ");
            sql.append(DictConstants.DICT_SY_YN_N, "and t.IS_HISTORY=?");
            String areaCode = param.getString("areaCode");
            String provinceCode = param.getString("provinceCode");
            if (StringUtils.isNotBlank(provinceCode) || StringUtils.isNotBlank(areaCode)) {
                sql.append("and exists ( ");
                sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
                sql.append("where tt.TARIFF_RECORD_ID = t.ID");
                if (StringUtils.isNotBlank(provinceCode)) {
                    provinceCode = provinceCode + ",000";
                    sql.appendIn(provinceCode.split(","), "AND tt.PROVINCE_CODE");
                }
                if (StringUtils.isNotBlank(areaCode)) {
                    areaCode = areaCode + ",000";
                    sql.appendIn(areaCode.split(","), "AND tt.AREA_CODE");
                }
                sql.append(") ");
            }
            sql.append(") t ");
            String status = param.getString("status");
            // 5 20240501 20240531
            if ("1".equals(status)) { // 在售：1上架时间 小于等于 终止时间   并且 2. 下架时间为空 或者 下架时间 大于等于 开始时间
                sql.append("  ON  t.ONLINE_DAY <= end_time 	AND (t.OFFLINE_DAY IS NULL or t.OFFLINE_DAY =''  OR t.OFFLINE_DAY >= start_time) ");
            } else if ("2".equals(status)) { // 无效：开始时间 <= 删除时间 <= 终止时间
                sql.append("  ON   start_time <=  DATE(t.DEL_TIME)  and  DATE(t.DEL_TIME)<= end_time ");
            } else if ("3".equals(status)) { // 下架：开始时间 <= 下架时间 <= 终止时间
                sql.append("  ON   start_time <= t.OFFLINE_DAY and  t.OFFLINE_DAY <= end_time ");
            } else if ("4".equals(status)) { // 未售: 上架时间 >= 终止时间
                sql.append("  ON   t.ONLINE_DAY >= end_time ");
            }

            //一级分类
            String type1 = param.getString("type1");
            if (StringUtils.isNotBlank(type1)) {
                sql.append("and ( t.TYPE1 in ('-1'");
                for (String code : type1.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            //二级分类
            String type2 = param.getString("type2");
            if (StringUtils.isNotBlank(type2)) {
                sql.append("and ( t.TYPE2 in ('-1'");
                for (String code : type2.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 资费属性
            String tariff_attr = param.getString("tariff_attr");
            if (StringUtils.isNotBlank(tariff_attr)) {
                sql.append("and ( t.TARIFF_ATTR in ('-1'");
                for (String code : tariff_attr.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 备案主体
            String reporter = param.getString("reporter");
            sql.appendLike(reporter, "and t.REPORTER like ?");
//            if ("001".compareTo(reporter) <= 0 && "005".compareTo(reporter) >= 0) { //reporter在"001"到"005"之间
//                sql.append("005".equals(reporter) ? "004" : reporter, "and t.ENT = ?");
//            } else {
//                sql.appendLike(reporter, "and t.REPORTER like ?");
//            }
            // 资费名称
            sql.appendLike(param.getString("name"), "and t.NAME like ?");
            // 资费标准
            sql.append(param.getInteger("feesBegin"), "and t.FEES >= ?", true);
            sql.append(param.getInteger("feesEnd"), "and t.FEES <=?", true);
            // 资费单位 fees_unit
            sql.appendLike(param.getString("fees_unit"), "and t.FEES_UNIT like ?");
            sql.append("   GROUP BY MONTH ");

            sql.append("union all ");
            //////////////////////////////////////////////////////////////
            sql.append("  SELECT ");
            sql.append("   '合计'  AS MONTH, ");
            sql.append("    SUM(CASE WHEN t.ent = '1' THEN 1 ELSE 0 END) AS TELECOM, ");
            sql.append("    SUM(CASE WHEN t.ent = '2' THEN 1 ELSE 0 END) AS MOBILE, ");
            sql.append("   SUM(CASE WHEN t.ent = '3' THEN 1 ELSE 0 END) AS UNICOM, ");
            sql.append("  SUM(CASE WHEN t.ent = '5' THEN 1 ELSE 0 END) AS BROAD, ");
            sql.append("sum(case when t.ENT='" + Constants.ENT_TYPE_001 + "' then 1 else 0 end) +");
            sql.append("sum(case when t.ENT='" + Constants.ENT_TYPE_002 + "' then 1 else 0 end) +");
            sql.append("sum(case when t.ENT='" + Constants.ENT_TYPE_003 + "' then 1 else 0 end) +");
            sql.append("sum(case when t.ENT='" + Constants.ENT_TYPE_004 + "' then 1 else 0 end) ALLTOTAL ");
            sql.append("  FROM ");
            sql.append("   " + getTableName("xty_tariff_record") + " t");
            sql.append("where 1=1");
            if ("1".equals(status)) { // 在售：1上架时间 小于等于 终止时间   并且 2. 下架时间为空 或者 下架时间 大于等于 开始时间
                sql.append(endTime, "  and  t.ONLINE_DAY <= ?  ");
                sql.append(startTime, "  AND (t.OFFLINE_DAY IS NULL or t.OFFLINE_DAY =''  OR t.OFFLINE_DAY >= ?)   ");
            } else if ("2".equals(status)) { // 无效：开始时间 <= 删除时间 <= 终止时间
                sql.append(startTime, "  and    DATE(t.DEL_TIME) >= ? ");
                sql.append(endTime, " and  DATE(t.DEL_TIME)<= ? ");
            } else if ("3".equals(status)) { // 下架：开始时间 <= 下架时间 <= 终止时间
                sql.append(startTime, "  and   t.OFFLINE_DAY >= ? ");
                sql.append(endTime, "  and  t.OFFLINE_DAY <= ? ");
            } else if ("4".equals(status)) { // 未售: 上架时间 >= 终止时间
                sql.append(endTime, "  and   t.ONLINE_DAY >= ? ");
            }
            sql.append(DictConstants.DICT_SY_YN_N, "and t.IS_HISTORY=?");
            if (StringUtils.isNotBlank(provinceCode) || StringUtils.isNotBlank(areaCode)) {
                sql.append("and exists ( ");
                sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
                sql.append("where tt.TARIFF_RECORD_ID = t.ID");
                if (StringUtils.isNotBlank(provinceCode)) {
                    provinceCode = provinceCode + ",000";
                    sql.appendIn(provinceCode.split(","), "AND tt.PROVINCE_CODE");
                }
                if (StringUtils.isNotBlank(areaCode)) {
                    areaCode = areaCode + ",000";
                    sql.appendIn(areaCode.split(","), "AND tt.AREA_CODE");
                }
                sql.append(") ");
            }
            //一级分类
            if (StringUtils.isNotBlank(type1)) {
                sql.append("and ( t.TYPE1 in ('-1'");
                for (String code : type1.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            //二级分类
            if (StringUtils.isNotBlank(type2)) {
                sql.append("and ( t.TYPE2 in ('-1'");
                for (String code : type2.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 资费属性
            if (StringUtils.isNotBlank(tariff_attr)) {
                sql.append("and ( t.TARIFF_ATTR in ('-1'");
                for (String code : tariff_attr.split(",")) {
                    sql.append(code, ", ?");
                }
                sql.append("))");
            }
            // 备案主体
//            if ("001".compareTo(reporter) <= 0 && "005".compareTo(reporter) >= 0) { //reporter在"001"到"005"之间
//                sql.append("005".equals(reporter) ? "004" : reporter, "and t.ENT = ?");
//            } else {
//                sql.appendLike(reporter, "and t.REPORTER like ?");
//            }
            sql.appendLike(reporter, "and t.REPORTER like ?");

//            if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) { //reporter在"1"到"5"之间
//                sql.append(reporter, "and t.ENT = ?");
//            } else {
//                sql.appendLike(reporter, "and t.REPORTER like ?");
//            }
            // 资费名称
            sql.appendLike(param.getString("name"), "and t.NAME like ?");
            // 资费标准
            sql.append(param.getString("feesBegin"), "and t.FEES >= ?", true);
            sql.append(param.getString("feesEnd"), "and t.FEES <=?", true);
            // 资费单位 fees_unit
            sql.appendLike(param.getString("fees_unit"), "and t.FEES_UNIT like ?");
            if (Constants.CYCLE_TYPE_03.equals(stType)) {
                sql.append("order by STR_TO_DATE(CONCAT(MONTH, '01'), '%Y年%m月%d') desc");
            } else {
                sql.append("  ORDER BY  case WHEN MONTH = '合计' then 0 ELSE MONTH end  DESC");
            }
            EasyQuery query = QueryFactory.getReadQuery();
            logger.info("企业资费数量变化SQL：{}", sql.getFullSq());
            List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            return EasyResult.ok(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    /**
     * 企业资费上线变化
     */
    @InfAuthCheck(resId = "cx-xty-statistics-online")
    @WebControl(name = "findStatusChangeTotal", type = Types.LIST)
    public JSONObject findStatusChangeTotal() {
        try {
            String stType = param.getString("type");
            if (StringUtils.isBlank(stType)) {
                stType = Constants.CYCLE_TYPE_03;
            }
            String stime = param.getString("stime");
            String etime = param.getString("etime");
            stime = stime.replace("-", "");
            etime = etime.replace("-", "");
            String entId = getEntId();

            EasyQuery query = QueryFactory.getReadQuery();
            String ent = param.getString("ent");

            EasySQL onlineSql = statusChangeSql(stType, stime, etime, entId, "ONLINE_DAY");
            logger.info("企业资费上线SQL：{}", onlineSql.getFullSq());
            List<JSONObject> onlineList = query.queryForList(onlineSql.getSQL(), onlineSql.getParams(), new JSONMapperImpl());

            EasySQL offlineSql = statusChangeSql(stType, stime, etime, entId, "OFFLINE_DAY");
            logger.info("企业资费下线SQL：{}", offlineSql.getFullSq());
            List<JSONObject> offlineList = query.queryForList(offlineSql.getSQL(), offlineSql.getParams(), new JSONMapperImpl());
            // 合并两个列表的数据
            Map<String, JSONObject> offlineMap = offlineList.stream().collect(Collectors.toMap(item -> item.getString("DATE_ID"), item -> item));
            List<JSONObject> resultData = new ArrayList<>();
            int len = onlineList.size() >= offlineList.size() ? onlineList.size() : offlineList.size();
            JSONObject dataTotal = new JSONObject();
            for (JSONObject online : onlineList) {
                String onlineDateId = online.getString("DATE_ID");
                String minDate = online.getString("MIN_DATE");
                String maxDate = online.getString("MAX_DATE");
                int onlineNum = online.getIntValue("NUM");
                JSONObject offline = offlineMap.get(onlineDateId);
                JSONObject data = new JSONObject();
                if (offline == null) {
                    data.put("MONTH", onlineDateId);
                    data.put("ONLINE_NUM", onlineNum);
                    data.put("OFFLINE_NUM", 0);
                    data.put("INCREMENT_NUM", onlineNum);
                    data.put("MIN_DATE", minDate);
                    data.put("MAX_DATE", maxDate);
                } else {
                    int offlineNum = offline.getIntValue("NUM");
                    data.put("MONTH", onlineDateId);
                    data.put("ONLINE_NUM", onlineNum);
                    data.put("OFFLINE_NUM", offlineNum);
                    data.put("INCREMENT_NUM", onlineNum - offlineNum);
                    data.put("MIN_DATE", minDate.compareTo(offline.getString("MIN_DATE")) > 0 ? offline.getString("MIN_DATE") : minDate);
                    data.put("MAX_DATE", maxDate.compareTo(offline.getString("MAX_DATE")) > 0 ? maxDate : offline.getString("MAX_DATE"));
                }
                offlineMap.remove(onlineDateId);
                if (!"合计".equals(onlineDateId)) {
                    resultData.add(data);
                } else {
                    dataTotal = data;
                }
            }
            for (String key : offlineMap.keySet()) { // 将最后剩余的进行排序
                JSONObject offline = offlineMap.get(key);
                String offlineDateId = offline.getString("DATE_ID");
                String minDate = offline.getString("MIN_DATE");
                String maxDate = offline.getString("MAX_DATE");
                int offlineNum = offline.getIntValue("NUM");
                JSONObject data = new JSONObject();
                data.put("MONTH", offlineDateId);
                data.put("ONLINE_NUM", 0);
                data.put("OFFLINE_NUM", offlineNum);
                data.put("INCREMENT_NUM", -offlineNum);
                data.put("MIN_DATE", minDate);
                data.put("MAX_DATE", maxDate);
                String dateId = "";
                String listDateId = "";
                for (int i = 0; i < len; i++) {
                    if (i > resultData.size() - 1) {
                        resultData.add(i, data);
                        break;
                    }
                    if (Constants.CYCLE_TYPE_03.equals(stType)) {
                        dateId = offlineDateId.replaceAll("年", "").replaceAll("月", "");
                        listDateId = resultData.get(i).getString("MONTH").replaceAll("年", "").replaceAll("月", "");
                    } else if (Constants.CYCLE_TYPE_04.equals(stType)) {
                        dateId = offlineDateId.replaceAll("年", "").replaceAll("季度", "");
                        listDateId = resultData.get(i).getString("MONTH").replaceAll("年", "").replaceAll("季度", "");

                    }
                    if (Integer.parseInt(dateId) > Integer.parseInt(listDateId)) {
                        resultData.add(i, data);
                        break;
                    }
                }
            }
            resultData.add(dataTotal); // 最后加入"合计"
            return EasyResult.ok(resultData);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    public EasySQL statusChangeSql(String stType, String stime, String etime, String entId, String dateField) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append("select *, DATE_ID as MONTH from (");
        sql.append("select");
        if ("year".equals(stType)) {
            sql.append(" t1.YEAR as DATE_ID,");
            sql.append(" min(t1.DATE_VALUE) as MIN_DATE,");
            sql.append(" max(t1.DATE_VALUE) as MAX_DATE,");
            stime = stime + "0101";
            etime = etime + "1231";
        } else if ("quarter".equals(stType)) {
            String[] timeArr = DimDateServer.getInstance().getDateArr(stime, stType, entId);
            stime = timeArr[0];
            timeArr = DimDateServer.getInstance().getDateArr(etime, stType, entId);
            etime = timeArr[1];
            sql.append(" t1.QUARTER_IN_YEAR as DATE_ID,");
            sql.append(" min(t1.DATE_VALUE) as MIN_DATE,");
            sql.append(" max(t1.DATE_VALUE) as MAX_DATE,");
        } else if ("month".equals(stType)) {
            stime = stime + "01";
            etime = ReportUtils.getCurrMonthEndDay(etime);
            sql.append(" t1.MONTH_IN_YEAR as DATE_ID,");
            sql.append(" min(t1.DATE_VALUE) as MIN_DATE,");
            sql.append(" max(t1.DATE_VALUE) as MAX_DATE,");
        } else {
            sql.append(" t1.DATE_ID as DATE_ID,");
        }
        sql.append(" COALESCE(SUM(CASE WHEN  t2." + dateField + " IS NOT NULL THEN 1 ELSE 0 END), 0) AS NUM");
        sql.append("from " + Constants.getStatSchema() + ".CC_DIM_DATE t1");
        sql.append("LEFT JOIN ( SELECT * FROM " + getTableName("XTY_TARIFF_RECORD"));
        sql.append("WHERE 1=1");
        sql.append(DictConstants.DICT_SY_YN_N, "and IS_HISTORY=? ) t2 on t1.DATE_ID = t2." + dateField);
        sql.append("where 1=1");
        statusChangeWhereSql(sql);
        sql.append(stime, " and t1.DATE_ID>=? ");
        sql.append(etime, " and t1.DATE_ID<=? ");
        sql.append("group by");
        if ("year".equals(stType)) {
            sql.append(" t1.YEAR");
        } else if ("quarter".equals(stType)) {
            sql.append(" t1.QUARTER_IN_YEAR");
        } else if ("month".equals(stType)) {
            sql.append(" t1.MONTH_IN_YEAR ");
        } else {
            sql.append(" t1.DATE_ID ");
        }
        sql.append("union all");
        sql.append("select '合计' MONTH,");
        sql.append(" min(t1.DATE_VALUE) as MIN_DATE,");
        sql.append(" max(t1.DATE_VALUE) as MAX_DATE,");
        sql.append(" sum( case when t1.DATE_ID = t2." + dateField + " then 1 else 0 end ) NUM");
        sql.append("from " + Constants.getStatSchema() + ".CC_DIM_DATE t1");
        sql.append("left join " + getTableName("XTY_TARIFF_RECORD") + " t2 on t1.DATE_ID = t2." + dateField);
        sql.append("where 1=1");
        sql.append(DictConstants.DICT_SY_YN_N, "and t2.IS_HISTORY=?");
        statusChangeWhereSql(sql);
        sql.append(stime, " and t1.DATE_ID>=? ");
        sql.append(etime, " and t1.DATE_ID<=? ");
        sql.append(") temp");
        if (Constants.CYCLE_TYPE_03.equals(stType)) {
            sql.append("order by STR_TO_DATE(CONCAT(MONTH, '01'), '%Y年%m月%d') desc");
        } else {
            sql.append("order by MONTH desc");
        }
        return sql;
    }

    private void statusChangeWhereSql(EasySQL sql) {
        //适用地区
        String areaCode = param.getString("areaCode");
        String provinceCode = param.getString("provinceCode");
        if (StringUtils.isNotBlank(provinceCode) || StringUtils.isNotBlank(areaCode)) {
            sql.append("and exists ( ");
            sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
            sql.append("where tt.TARIFF_RECORD_ID = t2.ID");
            if (StringUtils.isNotBlank(provinceCode)) {
                provinceCode = provinceCode + ",000";
                sql.appendIn(provinceCode.split(","), "AND tt.PROVINCE_CODE");
            }
            if (StringUtils.isNotBlank(areaCode)) {
                areaCode = areaCode + ",000";
                sql.appendIn(areaCode.split(","), "AND tt.AREA_CODE");
            }
            sql.append(") ");
        }
        //一级分类
        String type1 = param.getString("type1");
        if (StringUtils.isNotBlank(type1)) {
            sql.append("and ( t2.TYPE1 in ('-1'");
            for (String code : type1.split(",")) {
                sql.append(code, ", ?");
            }
            sql.append("))");
        }
        //二级分类
        String type2 = param.getString("type2");
        if (StringUtils.isNotBlank(type2)) {
            sql.append("and ( t2.TYPE2 in ('-1'");
            for (String code : type2.split(",")) {
                sql.append(code, ", ?");
            }
            sql.append("))");
        }
        // 资费属性
        String tariff_attr = param.getString("tariff_attr");
        if (StringUtils.isNotBlank(tariff_attr)) {
            sql.append("and ( t2.TARIFF_ATTR in ('-1'");
            for (String code : tariff_attr.split(",")) {
                sql.append(code, ", ?");
            }
            sql.append("))");
        }
        // 备案主体-
        String reporter = param.getString("reporter");
        if(StringUtils.isNotBlank(reporter)){
            if ("1".compareTo(reporter) <= 0 && "5".compareTo(reporter) >= 0) { //reporter在"1"到"5"之间
                sql.append(reporter, "and t2.ENT = ?");
            } else {
                sql.appendLike(reporter, "and t2.REPORTER like ?");
            }
        }
        //上线日期
        String onlineDay = param.getString("onlineDay");
        if (StringUtils.isNotBlank(onlineDay) && onlineDay.contains("~")) {
            String[] onlineDayArray = onlineDay.split("~");
            sql.append(onlineDayArray[0].replaceAll("-", ""), "and t2.ONLINE_DAY >= ?");
            sql.append(onlineDayArray[1].replaceAll("-", ""), "and t2.ONLINE_DAY <= ?");
        }
        //下线日期
        String offlineDay = param.getString("offlineDay");
        if (StringUtils.isNotBlank(offlineDay) && offlineDay.contains("~")) {
            String[] offlineDayArray = offlineDay.split("~");
            sql.append(offlineDayArray[0].replaceAll("-", ""), "and t2.OFFLINE_DAY >= ?");
            sql.append(offlineDayArray[1].replaceAll("-", ""), "and t2.OFFLINE_DAY <= ?");
        }
        //资费状态
        String status = param.getString("status");
        if (StringUtils.isNotBlank(status)) {
            sql.append("and ( t2.STATUS in ('-1'");
            for (String code : status.split(",")) {
                sql.append(code, ", ?");
            }
            sql.append("))");
        }
        // 资费名称
        sql.appendLike(param.getString("name"), "and t2.NAME like ?");
        //备案者
        // sql.appendRLike(param.getString("reportObj"), "and t2.REPORT_OBJ like ?");
        // 资费标准
        sql.append(param.getString("feesBegin"), "and t2.FEES >= ?", true);
        sql.append(param.getString("feesEnd"), "and t2.FEES <=?", true);
        // 资费单位 fees_unit
        sql.appendLike(param.getString("fees_unit"), "and t2.FEES_UNIT like ?");
        //企业
        sql.append(param.getString("ent"),"and t2.ENT = ?");
    }

    /**
     * 根据部门查询资费部门下的所有成员
     */
    @WebControl(name = "tariffDeptUserList", type = Types.LIST)
    public JSONObject tariffDeptUserList() {
        try {
            EasySQL sql = new EasySQL("SELECT t1.USER_ID, t1.AGENT_NAME, t2.SEX, t1.AGENT_PHONE, t2.MOBILE, t2.USER_ACCT, t4.SKILL_GROUP_ID DEPT_ID, t4.SKILL_GROUP_CODE DEPT_CODE, t4.SKILL_GROUP_NAME DEPT_NAME ");
            sql.append(" from " + getTableName("CC_BUSI_USER") + " t1");
            sql.append(" LEFT JOIN CC_USER t2 ON t1.USER_ID = t2.USER_ID");

            sql.append(" LEFT JOIN " + getTableName("cc_skill_group_user") + "  t3 ON t1.USER_ID = t3.USER_ID");
            sql.append(" LEFT JOIN " + getTableName("cc_skill_group") + "  t4 ON t4.SKILL_GROUP_ID = t3.SKILL_GROUP_ID");

            // sql.append(" LEFT JOIN  "+getTableName("V_CC_DEPT_USER")+" t4 ON t1.USER_ID = t4.USER_ID AND t4.BUSI_ORDER_ID = t1.BUSI_ORDER_ID ");
            // sql.append(" LEFT JOIN "+getTableName("xty_dept_user")+" t5 on t5.USER_ID = t1.USER_ID");
            sql.append(" where 1=1");
            sql.append(" AND t2.USER_STATE = 0 ");
            sql.append(getBusiOrderId(), "AND t1.BUSI_ORDER_ID = ? ");
            sql.append(getEntId(), "AND t1.ENT_ID = ? ");
            sql.append("AND t4.SKILL_GROUP_ID != ''");
            sql.append("AND t4.SKILL_GROUP_TYPE = 'struct'");
            sql.appendLike(param.getString("userName"), "AND t1.AGENT_NAME like ?");
            sql.appendLike(param.getString("userAcct"), "AND t2.USER_ACCT like ?");
            sql.append(param.getString("deptId"), "AND t4.SKILL_GROUP_ID = ?");
            sql.append(param.getString("deptName"), "AND t4.SKILL_GROUP_NAME = ?");
            if ("2".equals(param.getString("type"))) { // 不在改部门下的所有成员
                sql.append("and t1.USER_ID not in (select USER_ID from   " + getTableName("xty_dept_user") + " where 1=1");
                sql.append(param.getString("id"), "AND TARIFF_DEPT_ID = ?");
                sql.append(")");
                String ent = param.getString("ent");
                // 组织类型过滤
                String provinceCode = param.getString("provinceCode");
                String groupType = "";
                if (StringUtils.isNotBlank(provinceCode)) {
                    if (Constants.ENT_TYPE_001.equals(ent)) { //省电信
                        groupType = Constants.GROUP_TYPE_PE_TELECOM;
                    } else if (Constants.ENT_TYPE_002.equals(ent)) { //省移动
                        groupType = Constants.GROUP_TYPE_PE_MOBILE;
                    } else if (Constants.ENT_TYPE_003.equals(ent)) { //省联通
                        groupType = Constants.GROUP_TYPE_PE_UNICOM;
                    } else if (Constants.ENT_TYPE_004.equals(ent)) { //省广电
                        groupType = Constants.GROUP_TYPE_PE_BROAD;
                    }
                } else {
                    if (Constants.ENT_TYPE_001.equals(ent)) { //集团电信
                        groupType = Constants.GROUP_TYPE_GE_TELECOM;
                    } else if (Constants.ENT_TYPE_002.equals(ent)) { //集团移动
                        groupType = Constants.GROUP_TYPE_GE_MOBILE;
                    } else if (Constants.ENT_TYPE_003.equals(ent)) { //集团联通
                        groupType = Constants.GROUP_TYPE_GE_UNICOM;
                    } else if (Constants.ENT_TYPE_004.equals(ent)) { //集团广电
                        groupType = Constants.GROUP_TYPE_GE_BROAD;
                    }
                }
                sql.append(groupType, "and t4.GROUP_TYPE = ?");
                sql.append(provinceCode, "and t4.PROVINCE_CODE = ?");

            } else { // 在部门下的所有成员
                sql.append("and t1.USER_ID in (select USER_ID from   " + getTableName("xty_dept_user") + " where 1=1");
                sql.append(param.getString("id"), "AND TARIFF_DEPT_ID = ?");
                sql.append(")");
            }
            logger.info("查询成员sql:{}", sql.toFullSql());
            JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            return result;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 查询二级分类字典值数
     */
    @WebControl(name = "getType2Count", type = Types.RECORD)
    public JSONObject getType2Count() {
        try {
            EasyQuery query = QueryFactory.getReadQuery();
            EasySQL sql = new EasySQL("SELECT count(1) from  " + getTableName("c_cf_dictgroup") + "  ta1 ");
            sql.append("LEFT JOIN  " + getTableName("c_cf_dict") + "  ta2 ON ta1.id = ta2.DICT_GROUP_ID ");
            sql.append("where 1=1 ");
            sql.append("XTY_TARIFF_TWO_TYPE", " and ta1.CODE = ?");
            sql.append(getEntId(), " and ta2.ENT_ID = ? ");
            sql.append(getBusiOrderId(), " and ta2.BUSI_ORDER_ID = ?");
            sql.append("Y", " and ta2.ENABLE_STATUS = ?");
            int type2Count = query.queryForInt(sql.getSQL(), sql.getParams());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type2Count", type2Count);
            return jsonObject;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 未备案资费方案关联备案库信息弹出列表
     */
    @InfAuthCheck(resId = "cx-xty-tariff-list")
    @WebControl(name = "tariffRecordList2", type = Types.LIST)
    public JSONObject tariffRecordList2() {
        try {
            UserModel user = UserUtil.getUser(request);
            String deptGroupType = user.getDeptGroupType();

            StringBuffer selectFields = new StringBuffer();
            for (Map.Entry<String, Object> entry : BusiUtil.FieldZhMap.entrySet()) {
                String fieldName = entry.getKey();
                if ("PROVINCE_NAME".equals(fieldName) || "AREA_NAME".equals(fieldName)) {
                    continue;
                }
                if (StringUtils.isNotBlank(selectFields)) {
                    selectFields.append(", ");
                }
                selectFields.append("t1." + fieldName);
            }
            EasyQuery queryRead = QueryFactory.getReadQuery();

            EasySQL sql2 = new EasySQL("select  GROUP_CONCAT(tariff_id SEPARATOR ',') ids");

            sql2.append(" from " + getTableName("xty_tariff_auditname_rel"));
            sql2.append(" where 1=1");
            sql2.append(param.getString("tariffName"), " and  audit_tariff_name = ?");
            String relateIds = queryRead.queryForString(sql2.getSQL(), sql2.getParams());
            logger.info("名称关联的资费Id为：{}", relateIds);

            String relateStatus = param.getString("relateStatus");
            String status = param.getString("status");
            String onlineDay = param.getString("onlineDay");
            String offlineDay = param.getString("offlineDay");
            String tariff_attr = param.getString("tariff_attr");
            String provinceCode = param.getString("provinceCode");
            String type = param.getString("type");
            EasySQL sql = new EasySQL("");
		/*	if ("1".equals(relateStatus)) { // 已关联
				if(StringUtils.isNotBlank(relateIds)){
					getYSql(sql, selectFields, relateIds, deptGroupType, provinceCode, type, status, onlineDay, offlineDay, tariff_attr);
				}
			} else if ("0".equals(relateStatus) ) { // 未关联
				getNSql(sql, selectFields, relateIds, deptGroupType, provinceCode, type, status, onlineDay, offlineDay, tariff_attr);
			} else {*/
            if (StringUtils.isNotBlank(relateIds)) {
                getYSql(sql, selectFields, relateIds, deptGroupType, provinceCode, type, status, onlineDay, offlineDay, tariff_attr);
                sql.append("union all ");
                getNSql(sql, selectFields, relateIds, deptGroupType, provinceCode, type, status, onlineDay, offlineDay, tariff_attr);
                logger.info("未备案方案关联弹出窗SQL:{}", sql.getFullSq());
            } else {
                getNSql(sql, selectFields, relateIds, deptGroupType, provinceCode, type, status, onlineDay, offlineDay, tariff_attr);
            }
            /*}*/
            JSONObject queryForPageList = queryForPageList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            return queryForPageList;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }


    /**
     * 备案库资费关联未备案资费弹出列表
     */
    @WebControl(name = "tariffAuditList", type = Types.LIST)
    public JSONObject tariffAuditList() {
        try {
            String id = param.getString("id");
            EasySQL xtyTariffRecord = new EasySQL("select * from ").append(getTableName("xty_tariff_record")).append("where 1=1");
            xtyTariffRecord.append(id, "and id=?");
            JSONObject jsonObject = getQuery().queryForRow(xtyTariffRecord.getSQL(), xtyTariffRecord.getParams(), new JSONMapperImpl());
            String areaSelectType = jsonObject.getString("AREA_SELECT_TYPE");


            EasySQL sql = new EasySQL("select * from ( select t.* from ").append(getTableName("xty_tariff_plan_record t"));
            sql.append("where t.TARIFF_TYPE='1'");
            sql.append(param.getString("ENT_NAME"), "and t.ENT_NAME=?");
            sql.appendLike(param.getString("tariffName"), "and t.TARIFF_NAME like ?");
            sql.append(param.getString("provinceCode"), "and t.PROVINCE_CODE = ?");
            sql.append("and EXISTS (select 1 from " + getTableName("xty_tariff_auditname_rel t1") + " where t.id=t1.plan_record_id");
            sql.append(param.getString("id"),"and t1.tariff_id = ?");
            sql.append(")");
//            String relateStatus = param.getString("relateStatus");
//            if ("1".equals(relateStatus)) {
//                sql.append("and EXISTS (select 1 from " + getTableName("xty_tariff_auditname_rel t1") + " where t.id=t1.plan_record_id");
//                sql.append(param.getString("id"),"and t1.tariff_id = ?");
//                sql.append(")");
//            } else if ("0".equals(relateStatus)) {
//                sql.append("and not EXISTS (select 1 from " + getTableName("xty_tariff_auditname_rel t1") + " where t.id=t1.plan_record_id)");
//            }
            if (StrUtil.equals("2", areaSelectType)) {
                //指定地市
                sql.append("and EXISTS (select 1 from ").append(getTableName("XTY_TARIFF_AREQ t2"));
                sql.append("where 1=1");
                sql.append(id, "and  t2.TARIFF_RECORD_ID = ?");
                sql.append("and t2.PROVINCE_CODE=t.PROVINCE_CODE)");
            } else if (StrUtil.equals("3", areaSelectType)) {
                //排除地市
                sql.append("and NOT EXISTS (select 1 from ").append(getTableName("XTY_TARIFF_AREQ t2"));
                sql.append("where 1=1");
                sql.append(id, "and  t2.TARIFF_RECORD_ID = ?");
                sql.append("and t2.PROVINCE_CODE=t.PROVINCE_CODE)");
            }
            sql.append("union all");

            sql.append("select t.* from ").append(getTableName("xty_tariff_plan_record t"));
            sql.append("where t.TARIFF_TYPE='1'");
            sql.append(param.getString("ENT_NAME"), "and t.ENT_NAME=?");
            sql.appendLike(param.getString("tariffName"), "and t.TARIFF_NAME like ?");
            sql.append(param.getString("provinceCode"), "and t.PROVINCE_CODE = ?");
            sql.append("and not EXISTS (select 1 from " + getTableName("xty_tariff_auditname_rel t1") + " where t.id=t1.plan_record_id)");
            if (StrUtil.equals("2", areaSelectType)) {
                //指定地市
                sql.append("and EXISTS (select 1 from ").append(getTableName("XTY_TARIFF_AREQ t2"));
                sql.append("where 1=1");
                sql.append(id, "and  t2.TARIFF_RECORD_ID = ?");
                sql.append("and t2.PROVINCE_CODE=t.PROVINCE_CODE)");
            } else if (StrUtil.equals("3", areaSelectType)) {
                //排除地市
                sql.append("and NOT EXISTS (select 1 from ").append(getTableName("XTY_TARIFF_AREQ t2"));
                sql.append("where 1=1");
                sql.append(id, "and  t2.TARIFF_RECORD_ID = ?");
                sql.append("and t2.PROVINCE_CODE=t.PROVINCE_CODE)");
            }
            sql.append(") tmp");
            sql.append("order by CORRELATION DESC");

            logger.info("备案库资费关联未备案资费弹出列表SQL:{}", sql.toFullSql());
            return queryForPageList(sql.getSQL(), sql.getParams(), new EasyRowMapper<JSONObject>() {
                @Override
                public JSONObject mapRow(ResultSet rs, int i) {
                    JSONObject result = new JSONObject();
                    try {
                        result.put("PLAN_RECORD_ID", rs.getString("ID"));
                        result.put("TARIFF_NAME", rs.getString("TARIFF_NAME"));
                        result.put("ENT", rs.getString("ENT"));
                        result.put("ENT_NAME", rs.getString("ENT_NAME"));
                        result.put("PROVINCE_CODE", rs.getString("PROVINCE_CODE"));
                        result.put("PROVINCE_NAME", rs.getString("PROVINCE_NAME"));
                        result.put("RELATE_STATUS", rs.getString("CORRELATION"));
                    } catch (Exception e) {
                    	logger.error(e.getMessage(), e);
                    }
                    return result;
                }
            });
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return getJsonResult(null);
    }


    /**
     * 备案库资费关联未备案资费弹出列表
     */
	/*@WebControl(name="tariffAuditList",type=Types.LIST)
	public  JSONObject tariffAuditList(){
		try {
			// 获取备案库资费Id
			String id = param.getString("id");
			if(StringUtils.isBlank(id)){
				return EasyResult.fail("未获取到备案资费ID");
			}
			EasyQuery queryRead = QueryFactory.getReadQuery();

			EasySQL sql2 = new EasySQL("");
			sql2.append("select TARIFF_ANOTHER_NAME ");
			sql2.append("  from  "+ getTableName("xty_tariff_record"));
			sql2.append(" where 1= 1 ");
			sql2.append(id, " and  id = ? ");
			String auditTariffName = queryRead.queryForString(sql2.getSQL(), sql2.getParams());

			String relateStatus = param.getString("relateStatus");

			// 查询该资费获取到的适用省份信息
			EasySQL sql3 = new EasySQL("");
			sql3.append("select GROUP_CONCAT(PROVINCE_CODE SEPARATOR ',除')  A  ");
			sql3.append("  from  "+ getTableName("xty_tariff_areq"));
			sql3.append(" where 1= 1 ");
			sql3.append(id, " and  TARIFF_RECORD_ID = ? ");
			sql3.append( "  GROUP BY PROVINCE_CODE ");
			String provinceCodes = queryRead.queryForString(sql3.getSQL(), sql3.getParams());

			EasySQL sql = new EasySQL();
			sql.append("select");
			sql.append(" distinct t1.TARIFF_NAME, t2.PROVINCE_CODE, t2.PROVINCE_NAME, t1.ent, t1.ENT_NAME ");
			sql.append("from "+ getTableName("XTY_TARIFF_AUDIT") +" t1");
			sql.append("left join "+ getTableName("XTY_TARIFF_ORDER_AREA") +" t2 on t1.ID=t2.TARIFF_AUDIT_ID");
			sql.append("where 1=1");
			sql.append(param.getString("ENT_NAME")," and t1.ENT_NAME = ? ");
			sql.append("N"," and t1.EXIST_REPORT = ? ");
			sql.appendLike(param.getString("tariffName")," and t1.TARIFF_NAME like ? ");
			sql.append(param.getString("provinceCode")," and t2.PROVINCE_CODE = ? ");

			if(!"000".equals(provinceCodes)){
				sql.append("and ( t2.PROVINCE_CODE in ('-1'");
				for (String code : provinceCodes.split(",")) {
					sql.append(code, ", ?");
				}
				sql.append("))");
			}

			if("1".equals(relateStatus)){
				if(StringUtils.isNotBlank(auditTariffName)){ // 1.已关联
					sql.append("and ( t1.TARIFF_NAME in ('-1'");
					for (String code : auditTariffName.split(";")) {
						sql.append(code, ", ?");
					}
					sql.append("))");
				}else {
					JSONObject json = new JSONObject();
					json.put("data","");
					json.put("msg","请求成功!");
					json.put("pageNumber",1);
					json.put("pageSize",15);
					json.put("pageType",3);
					json.put("state",1);
					json.put("totalPage",0);
					json.put("totalRow",0);
					return json;
				}
			}else if("0".equals(relateStatus)) { // 0未关联
				if(StringUtils.isNotBlank(auditTariffName)){
					sql.append("and ( t1.TARIFF_NAME not in ('-1'");
					for (String code : auditTariffName.split(";")) {
						sql.append(code, ", ?");
					}
					sql.append("))");
				}
			}
			logger.info("列表查询Sql:"+sql.getFullSq()+".【..获取到的已关联名称串】："+auditTariffName);
			JSONObject queryForPageList = queryForPageList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			List<JSONObject> list = (List<JSONObject>)queryForPageList.get("data");
			if(list != null && list.size()>0){
				if(StringUtils.isBlank(auditTariffName)){
					list.stream().map(json -> {
						json.put("RELATE_STATUS", "0");
						return json;
					}).collect(Collectors.toList());
				}else {
					list.stream().map(json -> {
						if(auditTariffName.contains(json.getString("TARIFF_NAME")+";")){
							json.put("RELATE_STATUS", "1");
						}else {
							json.put("RELATE_STATUS", "0");
						}
						return json;
					}).collect(Collectors.toList());
				}
			}
			// 重新封装
			queryForPageList.put("data",list);
			return queryForPageList;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return EasyResult.fail();
		}
	}*/
    private void getYSql(EasySQL sql, StringBuffer selectFields, String relateIds, String deptGroupType, String provinceCode, String type, String status, String onlineDay, String offlineDay, String tariff_attr) {
        sql.append("select DISTINCT t1.ID ," + selectFields.toString() + " , '1' RELATE_STATUS,VERSION_NO ");
        sql.append("from " + getTableName("XTY_TARIFF_RECORD") + " t1");
        sql.append("WHERE 1=1");
        // 已关联的
        if (StringUtils.isNotBlank(relateIds)) {
            sql.append("and ( t1.ID  in ('-1'");
            for (String code : relateIds.split(",")) {
                sql.append(code, ", ?");
            }
            sql.append("))");
        }


        sql.append(getEntId(), "and t1.ENT_ID=?");
        sql.append(getBusiOrderId(), "and t1.BUSI_ORDER_ID=?");
        // 省企业
        if (StringUtils.startsWith(deptGroupType, Constants.GROUP_TYPE_PE)) {
            sql.append("and exists ( ");
            sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
            sql.append("where tt.TARIFF_RECORD_ID = t1.ID");
            sql.append(provinceCode, "AND (tt.PROVINCE_CODE=?");
            sql.append(Constants.AREA_CODE_ALL, "OR tt.PROVINCE_CODE=?)");
            sql.append(") ");
        }
        // 类型
        if (Constants.TARIFF_ATTR_01.equals(type)) { // 全国
            sql.append(Constants.TARIFF_ATTR_01, "AND t1.TARIFF_ATTR=?", false);
        } else if (Constants.TARIFF_ATTR_02.equals(type)) {  // 省内
            sql.append(Constants.TARIFF_ATTR_02, "AND (t1.TARIFF_ATTR=?", false);
            sql.append(Constants.TARIFF_ATTR_03, "OR t1.TARIFF_ATTR=?)", false);
        }
        // 企业, 电信001、移动002、联通003、广电004
//        sql.append("005".equals(param.getString("ent")) ? "004" : param.getString("ent"), "and t1.ENT = ?");
        sql.append(param.getString("ent"), "and t1.ENT = ?");
        // 备案方类型, 1-集团 、2-省企业
        sql.append(param.getString("reporterType"), "and t1.REPORTER_TYPE = ?");
        //资费状态
        if (StringUtils.isNotBlank(status)) {
            sql.append("and ( t1.STATUS in ('-1','3'");
            for (String code : status.split(",")) {
                sql.append(code, ", ?");
            }
            sql.append("))");
        }

        if (StringUtils.isNotBlank(provinceCode)) {
            sql.append("and exists ( ");
            sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
            sql.append("where tt.TARIFF_RECORD_ID = t1.ID");
            if (StringUtils.isNotBlank(provinceCode)) {
                provinceCode = provinceCode + ",000";
                sql.appendIn(provinceCode.split(","), "AND tt.PROVINCE_CODE");
            }
            sql.append(") ");
        }

        // 名称
        sql.appendLike(param.getString("name"), "and t1.NAME like ?");

        // 上线日期
        if (StringUtils.isNotBlank(onlineDay) && onlineDay.contains("~")) {
            String[] onlineDayArray = onlineDay.split("~");
            sql.append(onlineDayArray[0].replaceAll("-", ""), "and t1.ONLINE_DAY >= ?");
            sql.append(onlineDayArray[1].replaceAll("-", ""), "and t1.ONLINE_DAY <= ?");
        }
        // 下线日期
        if (StringUtils.isNotBlank(offlineDay) && offlineDay.contains("~")) {
            String[] offlineDayDayArray = offlineDay.split("~");
            sql.append(offlineDayDayArray[0].replaceAll("-", ""), "and t1.OFFLINE_DAY >= ?");
            sql.append(offlineDayDayArray[1].replaceAll("-", ""), "and t1.OFFLINE_DAY <= ?");
        }
        // 资费属性
        if (StringUtils.isNotBlank(tariff_attr)) {
            sql.append("and ( t1.TARIFF_ATTR in ('-1'");
            for (String code : tariff_attr.split(",")) {
                sql.append(code, ", ?");
            }
            sql.append("))");
        }
        sql.append(DictConstants.DICT_SY_YN_N, "and t1.IS_HISTORY=?");
        logger.info("未备案方案关联弹出窗YSQL:{}", sql.getFullSq());
    }

    private void getNSql(EasySQL sql, StringBuffer selectFields, String relateIds, String deptGroupType, String provinceCode, String type, String status, String onlineDay, String offlineDay, String tariff_attr) {
        sql.append("select DISTINCT t1.ID , " + selectFields.toString() + ", '0' RELATE_STATUS,VERSION_NO ");
        sql.append("from " + getTableName("XTY_TARIFF_RECORD") + " t1");
        sql.append("WHERE 1=1");
        // 未关联的
        if (StringUtils.isNotBlank(relateIds)) {
            sql.append("and ( t1.ID not in ('-1'");
            for (String code : relateIds.split(",")) {
                sql.append(code, ", ?");
            }
            sql.append("))");
        }


        sql.append(getEntId(), "and t1.ENT_ID=?");
        sql.append(getBusiOrderId(), "and t1.BUSI_ORDER_ID=?");
        // 省企业
        if (StringUtils.startsWith(deptGroupType, Constants.GROUP_TYPE_PE)) {

            sql.append("and exists ( ");
            sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
            sql.append("where tt.TARIFF_RECORD_ID = t1.ID");
            sql.append(provinceCode, "AND (tt.PROVINCE_CODE=?");
            sql.append(Constants.AREA_CODE_ALL, "OR tt.PROVINCE_CODE=?)");
            sql.append(") ");
        }
        // 类型
        if (Constants.TARIFF_ATTR_01.equals(type)) { // 全国
            sql.append(Constants.TARIFF_ATTR_01, "AND t1.TARIFF_ATTR=?", false);
        } else if (Constants.TARIFF_ATTR_02.equals(type)) {  // 省内
            sql.append(Constants.TARIFF_ATTR_02, "AND (t1.TARIFF_ATTR=?", false);
            sql.append(Constants.TARIFF_ATTR_03, "OR t1.TARIFF_ATTR=?)", false);
        }
        // 企业, 电信001、移动002、联通003、广电004
//        sql.append("005".equals(param.getString("ent")) ? "004" : param.getString("ent"), "and t1.ENT = ?");
        sql.append(param.getString("ent"), "and t1.ENT = ?");
        // 备案方类型, 1-集团 、2-省企业
        sql.append(param.getString("reporterType"), "and t1.REPORTER_TYPE = ?");
        //资费状态
        if (StringUtils.isNotBlank(status)) {
            sql.append("and ( t1.STATUS in ('-1'");
            for (String code : status.split(",")) {
                sql.append(code, ", ?");
            }
            sql.append("))");
        }


        // 适用省份
        if (StringUtils.isNotBlank(provinceCode)) {
            sql.append("and exists ( ");
            sql.append("select 1 from " + getTableName("XTY_TARIFF_AREQ") + " tt");
            sql.append("where tt.TARIFF_RECORD_ID = t1.ID");
            if (StringUtils.isNotBlank(provinceCode)) {
                provinceCode = provinceCode + ",000";
                sql.appendIn(provinceCode.split(","), "AND tt.PROVINCE_CODE");
            }
            sql.append(") ");
        }

        // 名称
        sql.appendLike(param.getString("name"), "and t1.NAME like ?");

        // 上线日期

        if (StringUtils.isNotBlank(onlineDay) && onlineDay.contains("~")) {
            String[] onlineDayArray = onlineDay.split("~");
            sql.append(onlineDayArray[0].replaceAll("-", ""), "and t1.ONLINE_DAY >= ?");
            sql.append(onlineDayArray[1].replaceAll("-", ""), "and t1.ONLINE_DAY <= ?");
        }
        // 下线日期
        if (StringUtils.isNotBlank(offlineDay) && offlineDay.contains("~")) {
            String[] offlineDayDayArray = offlineDay.split("~");
            sql.append(offlineDayDayArray[0].replaceAll("-", ""), "and t1.OFFLINE_DAY >= ?");
            sql.append(offlineDayDayArray[1].replaceAll("-", ""), "and t1.OFFLINE_DAY <= ?");
        }
        // 资费属性

        if (StringUtils.isNotBlank(tariff_attr)) {
            sql.append("and ( t1.TARIFF_ATTR in ('-1'");
            for (String code : tariff_attr.split(",")) {
                sql.append(code, ", ?");
            }
            sql.append("))");
        }
        sql.append(DictConstants.DICT_SY_YN_N, "and t1.IS_HISTORY=?");
        logger.info("未备案方案关联弹出窗NSQL:{}", sql.getFullSq());
    }

    public String[] getTime(String time) {
        String year = time.substring(0, 4);
        String quert = time.substring(4);
        String quert1 = "";
        String quert2 = "";
        switch (quert) {
            case "01":
                quert1 = "0101";
                quert2 = "0331";
                break;
            case "02":
                quert1 = "0401";
                quert2 = "0630";
                break;
            case "03":
                quert1 = "0701";
                quert2 = "0930";
                break;
            case "04":
                quert1 = "1001";
                quert2 = "1231";
                break;
        }
        String[] timeArray = new String[2];
        timeArray[0] = year + quert1;
        timeArray[1] = year + quert2;
        return timeArray;
    }

    /**
     * 获取资费差异结果
     */
    @WebControl(name="getTariffDiffResult",type=Types.PAGE)
    public JSONObject getTariffDiffResult(){
        EasySQL sql = new EasySQL();
        sql.append("select * from " + getTableName("XTY_TARIFF_DIFF_RESULT") + " where 1=1 ");

        sql.append("order by CREATE_TIME desc");
        return queryForPageList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
    }


    /**
     * 资费记录全行业检查列表
     */
    @InfAuthCheck(resId = "cx-xty-tariff-list")
    @WebControl(name = "tariffRecordFieldsForCheck", type = Types.LIST)
    public JSONObject tariffRecordFieldsForCheck() {
        try {
            JSONObject FieldZhMap = new JSONObject(true);

            // 字段中文映射
            FieldZhMap.put("SEQ_NO", "序列号");
            FieldZhMap.put("REPORTER_NAME", "报送主体");
            FieldZhMap.put("TYPE1", "一级分类");
            FieldZhMap.put("TYPE2", "二级分类");
            FieldZhMap.put("IS_TELECOM", "是否通信类");
            FieldZhMap.put("NAME", "资费名称");
            FieldZhMap.put("FEES", "资费标准");
            FieldZhMap.put("FEES_UNIT", "资费单位");
            FieldZhMap.put("EXTRA_FEES", "超出资费");
            FieldZhMap.put("OTHER_FEES", "其他费用");
            FieldZhMap.put("CALL_NUM", "语音");
            FieldZhMap.put("DATA_NUM", "通用流量");
            FieldZhMap.put("DATA_UNIT", "流量单位");
            FieldZhMap.put("SMS_NUM", "短彩信");
            // FieldZhMap.put("INTERNATIONAL_CALL", "国际语音");
            // FieldZhMap.put("INTERNATIONAL_ROAMING_DATA", "国际漫游流量");
            // FieldZhMap.put("INTERNATIONAL_SMS", "国际短信");
            FieldZhMap.put("ORIENT_TRAFFIC", "定向流量");
            FieldZhMap.put("ORIENT_TRAFFIC_UNIT", "定向流量单位");
            FieldZhMap.put("IPTV", "IPTV");
            FieldZhMap.put("BANDWIDTH", "带宽");
            FieldZhMap.put("RIGHTS", "权益");
            FieldZhMap.put("OTHER_CONTENT", "服务内容");
            FieldZhMap.put("TARIFF_ATTR", "资费属性");
            FieldZhMap.put("APPLICABLE_PEOPLE", "适用范围");
            FieldZhMap.put("AREA_DESC", "适用地区");
            FieldZhMap.put("VALID_PERIOD", "有效期限");
            FieldZhMap.put("CHANNEL", "销售渠道");
            FieldZhMap.put("DURATION", "在网要求");
            FieldZhMap.put("UNSUBSCRIBE", "退订方式");
            FieldZhMap.put("RESPONSIBILITY", "违约责任");
            FieldZhMap.put("ONLINE_DAY", "上线日期");
            FieldZhMap.put("OFFLINE_DAY", "下线日期");
            FieldZhMap.put("OTHERS", "其他说明");
//		FieldZhMap.put("REPORT_OBJ", "备案者");
//		FieldZhMap.put("REASON_NO_PUBLIC", "不公示原因");
            // FieldZhMap.put("RESTRICTIONS", "限制条件");
            FieldZhMap.put("REPORT_NO", "方案编号");
            FieldZhMap.put("STATUS", "资费状态");
            FieldZhMap.put("ENT_NAME", "企业名称");
            FieldZhMap.put("VERSION_NUM", "版本号");
            FieldZhMap.put("TARIFF_ANOTHER_NAME", "别名");// 别名


            // 修改
            // FieldZhMap.put("PLAN", "存量订购用户服务预案");

            // 删除
//		FieldZhMap.put("REASON", "删除原因");
            FieldZhMap.put("DEL_TIME", "删除时间");
            FieldZhMap.put("DEL_ACC", "删除人账号");
            FieldZhMap.put("CREATE_TIME", "创建时间");
            FieldZhMap.put("CREATE_ACC", "创建人账号");

            FieldZhMap.put("IS_PUBLIC", "是否公示");
            FieldZhMap.put("PUBLIC_VERSIONS", "公示版本");

            // FieldZhMap.put("UPDATE_ACC", "修改人账号"); // 用户要求不展示
            // FieldZhMap.put("PROVINCE_NAME", "适用省份"); // 已没用
            // FieldZhMap.put("APPLICABLE_AREA", "适用地区"); // 已没用

            FieldZhMap.put("FIELD_CHECK_RESULT", "字段检查结果");
            FieldZhMap.put("FIELD_CHECK_TIME", "检查时间");

            return EasyResult.ok(FieldZhMap);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail();
        }
    }
    /**
     * 构建FEES字段的查询
     * 用于匹配特定范围内的数值，包括带小数点的情况
     */
    private JSONObject buildFeesRegexQuery(String feesBegin, String feesEnd) {
        try {
            if (StringUtils.isBlank(feesBegin) && StringUtils.isBlank(feesEnd)) {
                return null;
            }

            // 精确匹配场景（范围首尾相同）
            if (StringUtils.isNotBlank(feesBegin) && StringUtils.isNotBlank(feesEnd) && feesBegin.equals(feesEnd)) {
                // 对于精确匹配，我们有两种情况需要处理：
                // 1. 精确数值匹配 (如 "20")
                // 2. 带小数点的匹配 (如 "20.0")

                // 如果输入没有小数点，尝试匹配原始输入和带.0的形式
                if (!feesBegin.contains(".")) {
                    JSONObject exactTermQuery = new JSONObject();
                    exactTermQuery.put("terms", new JSONObject()
                        .fluentPut("FEES.str", new JSONArray()
                            .fluentAdd(feesBegin)
                            .fluentAdd(feesBegin + ".0")));
                    return exactTermQuery;
                } else {
                    // 有小数点的情况，精确匹配
                    JSONObject termQuery = new JSONObject();
                    JSONObject termFields = new JSONObject();
                    termFields.put("FEES.str", feesBegin);
                    termQuery.put("term", termFields);
                    return termQuery;
                }
            }

            // 范围查询，优先使用数值范围查询
            try {
                JSONObject rangeQuery = new JSONObject();
                JSONObject rangeFields = new JSONObject();

                if (StringUtils.isNotBlank(feesBegin)) {
                    rangeFields.put("gte", Double.parseDouble(feesBegin));
                }

                if (StringUtils.isNotBlank(feesEnd)) {
                    rangeFields.put("lte", Double.parseDouble(feesEnd));
                }

                rangeQuery.put("range", new JSONObject().fluentPut("FEES", rangeFields));
                return rangeQuery;
            } catch (NumberFormatException nfe) {
                // 如果数值解析失败，使用字符串范围查询
                JSONObject strRangeQuery = new JSONObject();
                JSONObject strRangeObj = new JSONObject();

                if (StringUtils.isNotBlank(feesBegin)) {
                    strRangeObj.put("gte", feesBegin);
                }

                if (StringUtils.isNotBlank(feesEnd)) {
                    strRangeObj.put("lte", feesEnd);
                }

                strRangeQuery.put("range", new JSONObject().fluentPut("FEES.str", strRangeObj));
                return strRangeQuery;
            }
        } catch (Exception e) {
            logger.error("[buildFeesRegexQuery] 构建FEES查询条件时出错: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 构建正则表达式查询对象
     */
    private JSONObject buildRegexQuery(String regex) {
        JSONObject regexQuery = new JSONObject();
        regexQuery.put("regexp", new JSONObject().fluentPut("FEES_STR", new JSONObject()
            .fluentPut("value", regex)
            .fluentPut("flags", "ALL")
            .fluentPut("case_insensitive", true)));
        return regexQuery;
    }

    /**
     * 获取数字的格式描述
     */
    private String getNumberFormat(String numStr) {
        if (numStr == null) return "null";

        try {
            if (numStr.contains(".")) {
                // 小数点后的位数
                int decimalPlaces = numStr.length() - numStr.indexOf(".") - 1;
                return "decimal_" + decimalPlaces;
            } else {
                // 整数位数
                return "integer_" + numStr.length();
            }
        } catch (Exception e) {
            return "unknown";
        }
    }
}

