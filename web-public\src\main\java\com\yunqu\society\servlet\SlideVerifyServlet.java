package com.yunqu.society.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CacheUtil;
import com.yunqu.society.base.AppBaseServlet;
import com.yunqu.society.base.CommonLogger;
import com.yunqu.society.util.SecurityUtil;
import com.yunqu.society.util.SlideVerifyUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

/**
 * 滑块验证码Servlet
 */
@WebServlet("/interface/slideVerify")
public class SlideVerifyServlet extends AppBaseServlet {

    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("slideVerify").getName());
    
    // 验证容错范围
    private static final int VERIFY_TOLERANCE = 5;
    
    /**
     * 生成验证图片
     */
    public JSONObject actionForGenerate() {
        try {
            HttpServletRequest request = getRequest();
            String clientIp = SecurityUtil.getClientIp(request);
            String userAgent = request.getHeader("User-Agent");
            
            // 安全检查
            if (!SecurityUtil.checkRateLimit(clientIp)) {
                logger.warn("IP访问频率超限: {}", clientIp);
                return EasyResult.error(429, "请求过于频繁，请稍后再试");
            }
            
            if (!SecurityUtil.isValidUserAgent(userAgent)) {
                logger.warn("可疑User-Agent: {}", userAgent);
                return EasyResult.error(403, "请求被拒绝");
            }
            
            // 获取设备类型
            JSONObject param = this.getJSONObject();
            String deviceType = param.getString("deviceType");
            if (StringUtils.isBlank(deviceType)) {
                deviceType = detectDeviceType(userAgent);
            }
            
            // 生成验证图片
            SlideVerifyUtil.VerifyData verifyData = SlideVerifyUtil.generateVerifyImage(deviceType, userAgent);
            
            // 生成安全令牌
            String securityToken = SecurityUtil.generateSecureToken(verifyData.getSessionId(), clientIp);
            
            // 存储验证数据到缓存
            JSONObject serverData = new JSONObject();
            serverData.put("correctX", verifyData.getCorrectX());
            serverData.put("correctY", verifyData.getCorrectY());
            serverData.put("createTime", System.currentTimeMillis());
            serverData.put("clientIp", clientIp);
            serverData.put("userAgent", userAgent);
            serverData.put("securityToken", securityToken);
            serverData.put("used", false);
            
            CacheUtil.put("SLIDE_VERIFY_" + verifyData.getSessionId(), serverData.toJSONString(), 300);