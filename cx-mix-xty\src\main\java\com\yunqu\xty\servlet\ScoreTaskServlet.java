package com.yunqu.xty.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.xty.base.AppBaseServlet;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.*;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.annotation.WebServlet;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@WebServlet("/servlet/scoreTask/*")
public class ScoreTaskServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    private static final Logger logger = CommonLogger.getLogger("scoreTask");

    /**
     * <AUTHOR>
     * @Description 下发任务
     * @Param:
     * @Return:JSONObject
     * @Since create in 2024/6/6 11:52
     * @Company 广州云趣信息科技有限公司
     *
     */
    public JSONObject actionForAddTask() {
        String provinceCode = getJSONObject().getString("provinceCode");
        String taskName = getJSONObject().getString("taskName");
        String year = getJSONObject().getString("year");
        if(StringUtils.isAnyBlank(provinceCode,taskName)){
            logger.info("addTask=="+getJSONObject());
            return EasyResult.fail("参数错误");
        }
        try {
            boolean isExist = getQuery().queryForExist("select count(1) from "+getTableName("XTY_SCORE_TASK")+" where TASK_NAME = ?",new Object[]{taskName});
            if(isExist){
                return EasyResult.fail("任务名称已存在");
            }

            EasyRecord record = new EasyRecord(getTableName("XTY_SCORE_TASK"),"TASK_ID");
            String taskId = RandomKit.uniqueStr();
            record.setPrimaryValues(taskId);
            record.set("TASK_NAME",getJSONObject().getString("taskName"));
            record.set("START_TIME",getJSONObject().getString("startTime"));
            record.set("END_TIME",getJSONObject().getString("endTime"));
            record.set("TASK_PERIOD",getJSONObject().getString("taskPeriod"));
            record.set("YEAR",year);
            record.set("CREATE_TIME", EasyDate.getCurrentDateString());
            record.set("CREATE_USER",getUserPrincipal().getUserName());
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            record.set("UPDATE_USER",getUserPrincipal().getUserName());
            getQuery().save(record);

            List<Object[]> addProvinceList = new ArrayList<>();
            String[] codeArr = provinceCode.split(",");
            for(String code:codeArr){
                addProvinceList.add(new Object[]{RandomKit.uniqueStr(),taskId, code});
            }
            String addSql = "INSERT INTO "+getTableName("XTY_SCORE_TASK_PROVINCE")+"(TASK_PROVINCE_ID,TASK_ID,PROVINCE_CODE) values(?,?,?)";
            //异步执行
            CompletableFuture.runAsync(() -> {
                try {
                    getQuery().executeBatch(addSql, addProvinceList);
                } catch (SQLException e) {
                    logger.error("异步批量执行sql异常-->"+e.getMessage(), e);
                }
            });
            return EasyResult.ok();
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail("系统异常，下发失败");
        }
    }


    /**
     * <AUTHOR>
     * @Description 新增手工填报任务
     * @Param:
     * @Return:JSONObjecte
     * @Since create in 2024/6/6 11:52
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForAddManualTask() {
        JSONObject param = this.getJSONObject();
        try {
            String taskName = param.getString("taskName");
            if (StringUtils.isBlank(taskName)){
                return EasyResult.fail("任务名称不能为空");
            }
            if(this.getQuery().queryForExist("select count(1) from "+getTableName("xty_score_manual")+" where TASK_NAME = ? AND TYPE <> ?", new Object[]{taskName,Constants.XTY_TASK_COMMIT_DEL})){
                return EasyResult.fail("任务名称已存在");
            }

            String taskId = RandomKit.uniqueStr();
            EasyRecord record = new EasyRecord(getTableName("xty_score_manual"),"TASK_ID");
            record.setPrimaryValues(taskId);
            record.set("TASK_NAME",param.getString("taskName"));
            record.set("MANUAL_PERIOD",param.getString("taskPeriod"));
            record.set("YEAR",param.getString("year")); //评分年份
            record.set("CREATE_TIME", EasyDate.getCurrentDateString());
            record.set("CREATE_USER",getUserPrincipal().getUserName());
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            record.set("UPDATE_USER",getUserPrincipal().getUserName());
            getQuery().save(record);
            return EasyResult.ok(taskId);
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail("系统异常，新增填报任务失败");
        }
    }

    /**
     * <AUTHOR>
     * @Description 手工填报评分表分数 暂存/提交
     * @Param:
     * @Return:JSONObject
     * @Since create in 2024/6/6 11:52
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForAddManualScore() {
        JSONObject param = this.getJSONObject();
        logger.info("手工填报评分表分数=="+param);
        String taskId = param.getString("taskId");
        JSONObject addScore = param.getJSONObject("addScore");
        JSONObject totalScore = param.getJSONObject("totalScore");
        String type = param.getString("type");
        String addObj = param.getString("addObj");
        if(StringUtils.isAnyBlank(taskId,type)){
            return EasyResult.fail("参数错误");
        }
        EasyQuery query = getQuery();
        if(StringUtils.isBlank(addObj)){
            addObj = "1";
        }

        try {
            String opType = "add";
            query.begin();
            //1.初始化评分表
            initManualScore(taskId,null,opType,query);
            //2.新增评分关联企业表-
            // addScore:[{entCode1:[{itemId1:分数1},{itemId2:分数2}]},{entCode2:[{itemId1:分数1},{itemId2:分数2}]}]
            //校验是否存在ent数据 （先提交减分表或者暂存都有可能存在ent数据）
            Map<String,String> initMap = getInitMap(taskId,null);
            Map<String,String> entMap = new HashMap<>();
            List<String> delEntList = new ArrayList<>();
            addItem(taskId,addScore,initMap,entMap,delEntList,query,opType,type,addObj);
            //删除编辑后没有传的entCode
//            if(!initMap.isEmpty()){
//                for (String key : initMap.keySet()) {
//                    if (!delEntList.contains(key)) {
//                        query.execute("delete from "+getTableName("xty_score_task_ent") +" where TASK_ID = ? and ENT_CODE = ?",new Object[]{taskId,key});
//                    }
//                }
//            }
            if(Constants.XTY_TASK_COMMIT_COMMITTED.equals(type)){ //提交
                updateManual(taskId,opType,query);
                addScore(totalScore,initMap,entMap,query,opType);
            }
            query.commit();
            return EasyResult.ok();
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                logger.error(ex.getMessage(), ex);
            }
            logger.error(e.getMessage(), e);
            return EasyResult.fail("系统异常，新增填报任务失败");
        }

    }


    /**
     * <AUTHOR>
     * @Description 手工填报减分表暂存/提交
     * @Param: []
     * @Return: com.alibaba.fastjson.JSONObject
     * @Since create in 2024/6/11 20:07
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForSubManualScore() {
        JSONObject param = this.getJSONObject();
        logger.info("手工填报减分表暂存/提交=="+param);
        String taskId = param.getString("taskId");
        String subScoreStr = param.getString("subScore");
        String type = param.getString("type");
        String year = param.getString("year");
        if(StringUtils.isAnyBlank(taskId,subScoreStr,type)){
            return EasyResult.fail("参数错误");
        }
        JSONObject subScore = param.getJSONObject("subScore");
        JSONObject totalScore = param.getJSONObject("totalScore");
        EasyQuery query = getQuery();
        try {
//            if(Constants.XTY_TASK_COMMIT_COMMITTED.equals(type)){
//                if(!checkTotalScore(query,totalScore,year)){
//                    return EasyResult.fail("提交的减分总分超过10分,请刷新重试");
//                }
//            }
            query.begin();
            String opType = "sub";
            //1.初始化减分表
            initManualScore(taskId,null,opType,query);
            //校验是否存在ent数据 （先提交减分表或者暂存都有可能存在ent数据）
            Map<String,String> initMap = getInitMap(taskId,null);
            Map<String,String> entMap = new HashMap<>();
            List<String> delEntList = new ArrayList<>();
            addItem(taskId,subScore,initMap,entMap,delEntList,query,opType,type);
            if(Constants.XTY_TASK_COMMIT_COMMITTED.equals(param.getString("type"))){ //提交统计总分
                updateManual(taskId,opType,query);
                addScore(totalScore,initMap,entMap,query,opType);
            }
            query.commit();
            return EasyResult.ok();
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                logger.error(ex.getMessage(), ex);
            }
            logger.error(e.getMessage(), e);
            return EasyResult.fail("保存异常！");
        }

    }

    private boolean checkTotalScore(EasyQuery query, JSONObject totalScore,String year) throws SQLException {
        boolean flag = true;
        for(String entCode : totalScore.keySet()){
            String score = totalScore.getString(entCode);
            String nowScore = getTotalSubtract(query,entCode,year);
            double scoreInt = StringUtils.isBlank(score)?0:Double.parseDouble(score);
            double nowScoreInt = StringUtils.isBlank(nowScore)?0:Double.parseDouble(nowScore);
            if(scoreInt+nowScoreInt>10){
                flag = false;
                break;
            }
        }
        return flag;
    }

    /**
     * <AUTHOR>
     * @Description 获取当前总分
     * @Param: [query]
     * @Return: com.alibaba.fastjson.JSONObject
     * @Since create in 2024/6/27 10:14
     * @Company 广州云趣信息科技有限公司
     */
    private String getTotalSubtract(EasyQuery query,String entCode,String date) throws SQLException {
        if(StringUtils.isBlank(date)){
            LocalDate currentDate = LocalDate.now();
            date = String.valueOf(currentDate.getYear());
        }

        EasySQL sql = new EasySQL("select ROUND(sum(totalScore), 2) TOTAL_SCORE from ( ");
        //下发任务
        sql.append("select t3.ENT_CODE, ROUND(sum(t4.SCORE), 2) totalScore  from ");
        sql.append(getTableName("xty_score_task t1,")+getTableName("xty_score_task_province t2,"));
        sql.append(getTableName("xty_score_task_ent t3,")+getTableName("xty_score_subtract_record t4"));
        sql.append("where t1.TASK_ID = t2.TASK_ID and t2.TASK_PROVINCE_ID = t3.TASK_PROVINCE_ID");
        sql.append("and t3.TASK_ENT_ID = t4.TASK_ENT_ID");
        //已撤销不统计
        sql.append(Constants.XTY_TASK_TYPE_CANCEL,"and t1.TYPE <> ?");
        sql.append(Constants.XTY_SCORE_TASK_PROVINCE_TYPE_COMMITTED,"AND  t4.SUB_TYPE = ? ");
        //仅限当年的数据
        sql.append(date,"and t1.YEAR = ?");
        sql.append("GROUP BY t3.ENT_CODE");
        sql.append("UNION ALL");
        //工信部填报
        sql.append("select t2.ENT_CODE, ROUND(sum(t3.SCORE), 2) totalScore  from "+getTableName("xty_score_manual t1"));
        sql.append("INNER JOIN "+getTableName("xty_score_task_ent t2 ") +" on t1.TASK_ID = t2.TASK_ID");
        sql.append("INNER JOIN "+getTableName("xty_score_subtract_record t3") +" on t2.TASK_ENT_ID = t3.TASK_ENT_ID ");
        sql.append("where 1=1 ");
        //需要任务整体提交
        sql.append(Constants.XTY_TASK_COMMIT_DEL,"AND  t1.TYPE <> ? ");
        sql.append(Constants.XTY_TASK_COMMIT_COMMITTED,"AND  t3.SUB_TYPE = ? ");
        //仅限当年的数据
        sql.append(date,"and t1.YEAR = ?");
        sql.append("group by t2.ENT_CODE");
        sql.append(") as tmp");
        sql.append(entCode,"where ENT_CODE = ?",false);
        sql.append(" group by ENT_CODE");
        return query.queryForString(sql.getSQL(), sql.getParams());
    }

    /**
     * <AUTHOR>
     * @Description 获取当前加分项总分
     * @Param: [query]
     * @Return: com.alibaba.fastjson.JSONObject
     * @Since create in 2024/6/27 10:14
     * @Company 广州云趣信息科技有限公司
     */
    private String getTotalPlus(EasyQuery query,String entCode,String date) throws SQLException {
        if(StringUtils.isBlank(date)){
            LocalDate currentDate = LocalDate.now();
            date = String.valueOf(currentDate.getYear());
        }

        EasySQL sql = new EasySQL("select ROUND(sum(totalScore), 2) TOTAL_SCORE from ( ");
        //下发任务
        sql.append("select t3.ENT_CODE, ROUND(sum(t4.SCORE), 2) totalScore  from ");
        sql.append(getTableName("xty_score_task t1,")+getTableName("xty_score_task_province t2,"));
        sql.append(getTableName("xty_score_task_ent t3,")+getTableName("xty_score_plus_record t4"));
        sql.append("where t1.TASK_ID = t2.TASK_ID and t2.TASK_PROVINCE_ID = t3.TASK_PROVINCE_ID");
        sql.append("and t3.TASK_ENT_ID = t4.TASK_ENT_ID");
        //已撤销不统计
        sql.append(Constants.XTY_TASK_TYPE_CANCEL,"and t1.TYPE <> ?");
        sql.append(Constants.XTY_SCORE_TASK_PROVINCE_TYPE_COMMITTED,"AND  t4.PLUS_TYPE = ? ");
        //仅限当年的数据
        sql.append(date,"and t1.YEAR = ?");
        sql.append("GROUP BY t3.ENT_CODE");
        sql.append("UNION ALL");
        //工信部填报
        sql.append("select t2.ENT_CODE, ROUND(sum(t3.SCORE), 2) totalScore  from "+getTableName("xty_score_manual t1"));
        sql.append("INNER JOIN "+getTableName("xty_score_task_ent t2 ") +" on t1.TASK_ID = t2.TASK_ID");
        sql.append("INNER JOIN "+getTableName("xty_score_plus_record t3") +" on t2.TASK_ENT_ID = t3.TASK_ENT_ID ");
        sql.append("where 1=1 ");
        //需要任务整体提交
        sql.append(Constants.XTY_TASK_COMMIT_DEL,"AND  t1.TYPE <> ? ");
        sql.append(Constants.XTY_TASK_COMMIT_COMMITTED,"AND  t3.PLUS_TYPE = ? ");
        //仅限当年的数据
        sql.append(date,"and t1.YEAR = ?");
        sql.append("group by t2.ENT_CODE");
        sql.append(") as tmp");
        sql.append(entCode,"where ENT_CODE = ?",false);
        sql.append(" group by ENT_CODE");
        return query.queryForString(sql.getSQL(), sql.getParams());
    }


    private void addItem(String taskId,JSONObject scoreObj,Map<String,String> initMap,Map<String,String> entMap,List<String> delEntList,EasyQuery query,String opType,String type) throws SQLException{
        addItem(taskId,scoreObj,null,null,initMap,entMap,delEntList,query,opType,type,null);
    }
    private void addItem(String taskId,JSONObject scoreObj,Map<String,String> initMap,Map<String,String> entMap,List<String> delEntList,EasyQuery query,String opType,String type,String addObj) throws SQLException{
        addItem(taskId,scoreObj,null,null,initMap,entMap,delEntList,query,opType,type,addObj);
    }

    private void addItem(String taskId,JSONObject scoreObj,String provinceCode,String taskProvinceId,
                          Map<String,String> initMap,Map<String,String> entMap,List<String> delEntList,
                          EasyQuery query,String opType,String type,String addObj) throws SQLException {
        List<Object[]> entList = new ArrayList<>();
        List<Object[]> entItemList = new ArrayList<>();
        String now = EasyDate.getCurrentDateString();
        String user = getUserAcct();
        for(String entCode : scoreObj.keySet()){
            JSONObject itemObj = scoreObj.getJSONObject(entCode);
            delEntList.add(entCode);
            boolean isExist = true;
            String taskEntId = initMap.get(entCode);
            if(StringUtils.isBlank(taskEntId)){
                isExist = false;
                taskEntId = RandomKit.uniqueStr();
            }
            entMap.put(entCode, taskEntId);
            for (String itemId : itemObj.keySet()) {
                if("add".equals(opType)){
                    String score =itemObj.getString(itemId);
                    entItemList.add(new Object[]{RandomKit.uniqueStr(),taskId,taskEntId,itemId,score,now,user,type,addObj});
                }else if("sub".equals(opType) || "plus".equals(opType)){
                    String fileId = "";
                    String score_fileId = itemObj.getString(itemId);
                    String score  =score_fileId.split("\\|")[0];
                    if(score_fileId.split("\\|").length > 1){
                        fileId = score_fileId.split("\\|")[1];
                    }
                    entItemList.add(new Object[]{RandomKit.uniqueStr(),taskId,taskEntId,itemId,score,now,user,type,fileId,addObj});
                }
            }
            if(!isExist){
                entList.add(new Object[]{taskEntId,taskId,provinceCode,taskProvinceId,entCode});
            }
        }
        String entSql = "INSERT INTO "+getTableName("xty_score_task_ent")+"(TASK_ENT_ID,TASK_ID,PROVINCE_CODE,TASK_PROVINCE_ID,ENT_CODE) values(?,?,?,?,?)";
        String entItemSql = "";
        if("sub".equals(opType)){
            entItemSql = "INSERT INTO "+getTableName("xty_score_subtract_record")+"(ID,TASK_ID,TASK_ENT_ID,DESCS,SCORE,CREATE_TIME,CREATE_USER,SUB_TYPE,SUB_FILE_ID,ADD_OBJ) values(?,?,?,?,?,?,?,?,?,?)";
        }else if("plus".equals(opType)){
            entItemSql = "INSERT INTO "+getTableName("xty_score_plus_record")+"(ID,TASK_ID,TASK_ENT_ID,DESCS,SCORE,CREATE_TIME,CREATE_USER,PLUS_TYPE,PLUS_FILE_ID,ADD_OBJ) values(?,?,?,?,?,?,?,?,?,?)";
        }else if("add".equals(opType)){
            entItemSql = "INSERT INTO "+getTableName("xty_score_add_record")+"(ID,TASK_ID,TASK_ENT_ID,ITEM_ID,SCORE,CREATE_TIME,CREATE_USER,ADD_TYPE,ADD_OBJ) values(?,?,?,?,?,?,?,?,?)";
        }
        logger.info("entItemSql:"+entItemSql);
        logger.info("entItemList:"+entItemList);
        query.executeBatch(entItemSql, entItemList);
        query.executeBatch(entSql, entList);
    }

    /**
     * <AUTHOR>
     * @Description 判断ent表有无历史数据
     * @Param: [taskId]
     * @Return: java.util.Map<java.lang.String,java.lang.String>
     * @Since create in 2024/6/11 17:51
     * @Company 广州云趣信息科技有限公司
     */
    private Map<String, String> getInitMap(String taskId,String taskProvinceId) throws SQLException {
        EasySQL sql = new EasySQL("select ENT_CODE,TASK_ENT_ID from "+getTableName("xty_score_task_ent"));
        sql.append("where 1=1");
        sql.append(taskId,"and TASK_ID = ?",false);
        sql.append(taskProvinceId,"and TASK_PROVINCE_ID = ?");
        List<JSONObject> jsonList = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        return  jsonList.stream()
                .collect(Collectors.toMap(
                        json -> json.getString("ENT_CODE"),
                        json -> json.getString("TASK_ENT_ID")
                ));
    }

    private void initAddTaskScore(String taskId,String taskProvinceId,EasyQuery query,String addObj) throws SQLException {
        //初始化下发任务评分表比较特殊：分为两部分提交 一部分是省管局提交数据 一部分是工信部填报数据
        //初始化的时候要考虑提交对象
        EasySQL entListSql = new EasySQL("select TASK_ENT_ID from "+getTableName("XTY_SCORE_TASK_ENT")+" where 1 = 1 ");
        entListSql.append(taskId,"AND TASK_ID = ?");
        entListSql.append(taskProvinceId,"AND TASK_PROVINCE_ID = ?");
        List<String> entList = query.queryForList(entListSql.getSQL(), entListSql.getParams(), new EasyRowMapper<String>() {
            @Override
            public String mapRow(ResultSet resultSet, int i) {
                try {
                    return resultSet.getString("TASK_ENT_ID");
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            }
        });
        //初始化评分总分
        for (String taskEntId : entList){
            //查询各个企业的评分总分
            double entScore = getEntScore(taskEntId, addObj);
            logger.info("企业"+taskEntId+"的评分总分是"+entScore);
            if(entScore > 0.0){
                String entScoresql = "update "+getTableName("xty_score")+" set ADD_SCORE = ADD_SCORE-"+entScore+"  where TASK_ENT_ID = ? ";
                query.executeUpdate(entScoresql, new Object[]{taskEntId});
            }
        }

        EasySQL sql = new EasySQL("delete from "+getTableName("xty_score_add_record"));
        sql.append("where 1=1 ");
        //最后一次提交数据会提交全量 如果加了条件 先提交的会有两条记录
//        sql.append(addObj,"and ADD_OBJ = ?",false);
        sql.append("and TASK_ENT_ID in (");
        appendQuery(taskId,taskProvinceId,sql);
        sql.append(")");
        query.execute(sql.getSQL(),sql.getParams());
    }

    private void initTaskScore(String taskId,String taskProvinceId,String opType,EasyQuery query) throws SQLException {
        String tableName = getScoreTableName(opType);
        if (StringUtils.isBlank(tableName)) {
            logger.error("无效的操作类型: " + opType);
            throw new SQLException("无效的操作类型");
        }
        EasySQL sql = new EasySQL("delete from ");
       sql.append(tableName);
       sql.append("where 1=1 ");
       sql.append("and TASK_ENT_ID in (");
       appendQuery(taskId,taskProvinceId,sql);
       sql.append(")");
       query.execute(sql.getSQL(),sql.getParams());
        EasySQL sql2 = new EasySQL("update "+getTableName("xty_score"));
        if("sub".equals(opType)){
            sql2.append("set SUB_SCORE = 0 ");
        }else if("plus".equals(opType)){
            sql2.append("set PLUS_SCORE = 0 ");
        }
        sql2.append("where TASK_ENT_ID in (");
        appendQuery(taskId,taskProvinceId,sql2);
        sql2.append(")");
        query.execute(sql2.getSQL(),sql2.getParams());
    }


    /**
     * <AUTHOR>
     * @Description 删除评分项并更新总分
     * @Param: [id 记录ID, opType 操作类型(sub/plus), taskEntId 任务企业ID, query 数据库查询对象]
     * @Return: void
     * @Since create in 2024/6/12 9:06
     * @Company 广州云趣信息科技有限公司
     */
    private void delTaskScore(String id, String opType, String taskEntId, EasyQuery query) throws SQLException {
        // 1. 获取表名
        String tableName = getScoreTableName(opType);
        if (StringUtils.isBlank(tableName)) {
            logger.error("无效的操作类型: " + opType);
            throw new SQLException("无效的操作类型");
        }

        // 2. 获取要删除记录的分数
        double scoreToDelete = getScoreToDelete(tableName, taskEntId, query);
        logger.info("要删除的分数: " + scoreToDelete);

        // 3. 删除记录
        deleteScoreRecord(tableName, id, query);
        logger.info("成功删除记录, ID: " + id);

        // 4. 更新总分
        updateTotalScore(taskEntId, opType, scoreToDelete, query);
    }

    /**
     * 根据操作类型获取对应的表名
     */
    private String getScoreTableName(String opType) {
        switch (opType) {
            case "sub":
                return getTableName("xty_score_subtract_record");
            case "plus":
                return getTableName("xty_score_plus_record");
            case "add":
                return getTableName("xty_score_add_record");
            default:
                return null;
        }
    }

    /**
     * 获取要删除记录的分数
     */
    private double getScoreToDelete(String tableName, String taskEntId, EasyQuery query) throws SQLException {
        EasySQL scoreSQL = new EasySQL("select sum(SCORE) SCORE from ");
        scoreSQL.append(tableName + " where 1=1 ");
        scoreSQL.append(taskEntId, "and TASK_ENT_ID=?", false);
        logger.info("查询分数SQL: " + scoreSQL.getSQL() + ", 参数: " + scoreSQL.getParams());
        
        String scoreStr = query.queryForString(scoreSQL.getSQL(), scoreSQL.getParams());
        return StringUtils.isBlank(scoreStr) ? 0.0 : Double.parseDouble(scoreStr);
    }

    /**
     * 删除评分记录
     */
    private void deleteScoreRecord(String tableName, String id, EasyQuery query) throws SQLException {
        EasySQL sql = new EasySQL("delete from ");
        sql.append(tableName);
        sql.append("where 1=1 ");
        sql.append(id, "and ID=?", false);
        query.execute(sql.getSQL(), sql.getParams());
    }

    /**
     * 更新总分
     */
    private void updateTotalScore(String taskEntId, String opType, double scoreToDelete, EasyQuery query) throws SQLException {
        // 获取当前总分记录
        EasyRecord record = new EasyRecord(getTableName("xty_score"), "TASK_ENT_ID");
        record.setPrimaryValues(taskEntId);
        Map<String, String> currentScore = query.findById(record);
        
        // 计算新的总分
        String scoreField = "sub".equals(opType) ? "SUB_SCORE" : "PLUS_SCORE";
        double currentTotal = currentScore.get(scoreField) == null ? 0.0 : Double.parseDouble(currentScore.get(scoreField));
        double newTotal = currentTotal - scoreToDelete;
        
        // 更新总分
        EasySQL updateSQL = new EasySQL("update " + getTableName("xty_score"));
        updateSQL.append(newTotal, "set " + scoreField + " = ? ");
        updateSQL.append(taskEntId, "where TASK_ENT_ID = ?");
        logger.info("更新总分SQL: " + updateSQL.getSQL() + ", 参数: " + updateSQL.getParams());
        
        query.execute(updateSQL.getSQL(), updateSQL.getParams());
    }

    private void initManualScore(String taskId,String taskProvinceId,String opType,EasyQuery query) throws SQLException {
        String tableName = getScoreTableName(opType);
        if (StringUtils.isBlank(tableName)) {
            logger.error("无效的操作类型: " + opType);
            throw new SQLException("无效的操作类型");
        }

        EasySQL sql = new EasySQL("delete from ");
        sql.append(tableName);
        sql.append("where 1=1 ");
        sql.append("and TASK_ENT_ID in (");
        appendQuery(taskId,taskProvinceId,sql);
        sql.append(")");
        query.execute(sql.getSQL(),sql.getParams());

        EasySQL sql2 = new EasySQL("update "+getTableName("xty_score"));
        if("add".equals(opType)){
            sql2.append("set ADD_SCORE = 0 ");
        }else if("sub".equals(opType)){
            sql2.append("set SUB_SCORE = 0 ");
        }else if("plus".equals(opType)){
            sql2.append("set PLUS_SCORE = 0 ");
        }
        sql2.append("where TASK_ENT_ID in (");
        appendQuery(taskId,taskProvinceId,sql2);
        sql2.append(")");
        query.execute(sql2.getSQL(),sql2.getParams());
    }

    private void appendQuery(String taskId,String taskProvinceId,EasySQL sql) {
        sql.append("select TASK_ENT_ID from "+getTableName("xty_score_task_ent")+" where 1 = 1  ");
        sql.append(taskId,"AND TASK_ID = ?",false);
        if(StringUtils.isNotBlank(taskProvinceId)){ //区分手工填报和省管局填报
            sql.append(taskProvinceId,"AND TASK_PROVINCE_ID = ?");
        }
    }

    private Double getEntScore(String taskEntId, String addObj) throws SQLException {
        EasySQL entScore = new EasySQL("select sum(SCORE) SCORE from "+getTableName("xty_score_add_record")+" where 1=1 ");
        entScore.append(taskEntId,"and TASK_ENT_ID = ?");
        entScore.append(addObj,"and ADD_OBJ = ?");
        EasyRow row = getQuery().queryForRow(entScore.getSQL(), entScore.getParams());
        String sumScore = row.getColumnValue("SCORE");
        return StringUtils.isBlank(sumScore) ? 0.0 : Double.parseDouble(sumScore);
    }

    /**
     * <AUTHOR>
     * @Description 更新填报任务表
     * @Param: [taskId, type, query]
     * @Return: void
     * @Since create in 2024/6/20 17:51
     * @Company 广州云趣信息科技有限公司
     */
    private void updateManual(String taskId,String opType,EasyQuery query) throws SQLException {
        //判断加/减分分表有没有提交有的话就更新任务状态为已提交
        EasyRecord mRecord = new EasyRecord(getTableName("xty_score_manual"),"TASK_ID");
        if("sub".equals(opType)){
            mRecord.set("SUB_TYPE",Constants.XTY_TASK_COMMIT_COMMITTED);
        }else if("add".equals(opType)){
            mRecord.set("ADD_TYPE",Constants.XTY_TASK_COMMIT_COMMITTED);
        }
        mRecord.setPrimaryValues(taskId);
        boolean flag = checkCommit(taskId,null,opType,query);
        if(flag){
            mRecord.set("TYPE",Constants.XTY_TASK_COMMIT_COMMITTED);
        }
        mRecord.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        mRecord.set("UPDATE_USER",getUserPrincipal().getUserName());
        query.update(mRecord);
    }

    private boolean checkCommit(String taskId,String taskProvinceId,String opType,EasyQuery query) throws SQLException {
        EasySQL sql = new EasySQL();
        if("add".equals(opType)){
            sql.append("select SUB_TYPE TYPE1,PLUS_TYPE TYPE2 from ");
        }else if("sub".equals(opType)){
            sql.append("select ADD_TYPE TYPE1,PLUS_TYPE TYPE2 from ");
        }else if("plus".equals(opType)){
            sql.append("select ADD_TYPE TYPE1,SUB_TYPE TYPE2 from ");
        }
        if(StringUtils.isBlank(taskProvinceId)){
            sql.append(getTableName("xty_score_manual"));
        }else{
            sql.append(getTableName("xty_score_task_province"));
        }
        sql.append(taskId,"where TASK_ID = ?");
        sql.append(taskProvinceId,"and  TASK_PROVINCE_ID = ?");

        JSONObject object = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        String type1 = object.getString("TYPE1");
        String type2 = object.getString("TYPE2");
        if(StringUtils.isNotBlank(type1) && StringUtils.isNotBlank(type2)
                && Constants.XTY_TASK_COMMIT_COMMITTED.equals(type1) && Constants.XTY_TASK_COMMIT_COMMITTED.equals(type2)){
            return true;
        }
        return false;
    }

    private boolean checkGxbSh(String taskProvinceId,String opType) throws SQLException {
        EasySQL sql = new EasySQL();
        if("sub".equals(opType)){
            sql.append("select GXB_PLUS_TYPE TYPE from ");
        }else if("plus".equals(opType)){
            sql.append("select GXB_SUB_TYPE TYPE from ");
        }
        sql.append(getTableName("xty_score_task_province"));
        sql.append("where 1=1");
        sql.append(taskProvinceId,"and  TASK_PROVINCE_ID = ?");
        logger.info(sql.toFullSql()+",taskProvinceId="+taskProvinceId);
        String type = getQuery().queryForString(sql.getSQL(), sql.getParams());
        if(StringUtils.isNotBlank(type)  && (Constants.XTY_SCORE_TASK_SH_TYPE_COMMITTED.equals(type) || Constants.XTY_SCORE_TASK_SH_TYPE_UNINVOLVED.equals(type))){
            return true;
        }
        return false;
    }

    /**
     * <AUTHOR>
     * @Description 添加总分
     * @Param: [taskId, totalScore, initMap, entMap, query, type]
     * @Return: void
     * @Since create in 2024/6/20 16:08
     * @Company 广州云趣信息科技有限公司
     */
    private void addScore(JSONObject totalScore,Map<String,String> initMap,Map<String,String> entMap,EasyQuery query,String opType) throws SQLException {
        if(totalScore==null){
            logger.info("totalScore为空");
            return ;
        }
        //3.新增add分数表
        //totalScore:[{entCode1:分数},{entCode2:分数},{entCode3:分数},{entCode4:分数}]
        for(String entCode : totalScore.keySet()){ //企业就四个 可放在循环里面提交
            String score = totalScore.getString(entCode);
            String taskEntId = "";
            if(StringUtils.isBlank(initMap.get(entCode))){ //新增
                taskEntId = entMap.get(entCode);
            }else{
                taskEntId = initMap.get(entCode);
            }
            String scoreId = getScoreIdByTaskEntId(taskEntId,query);

            EasyRecord record = new EasyRecord(getTableName("XTY_SCORE"),"ID");
            if(StringUtils.isBlank(scoreId)){
                record.setPrimaryValues(RandomKit.uniqueStr());
                record.set("TASK_ENT_ID",taskEntId);
                if("sub".equals(opType)){
                    record.set("SUB_SCORE",score);
                }else if("add".equals(opType)){
                    record.set("ADD_SCORE",score);
                }else if("plus".equals(opType)){
                    record.set("PLUS_SCORE",score);
                }
                record.set("CREATE_TIME", EasyDate.getCurrentDateString());
                record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
                query.save(record);
            }else{
                record.setPrimaryValues(scoreId);
                if("sub".equals(opType)){
                    record.set("SUB_SCORE",score);
                }else if("add".equals(opType)){
                    record.set("ADD_SCORE",score);
                }else if("plus".equals(opType)){
                    record.set("PLUS_SCORE",score);
                }
                record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
                query.update(record);
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 添加工信部总分
     * @Param: [taskId, totalScore, initMap, entMap, query, type]
     * @Return: void
     * @Since create in 2024/6/20 16:08
     * @Company 广州云趣信息科技有限公司
     */
    private void addGxbScore(JSONObject totalScore,Map<String,String> initMap,Map<String,String> entMap,EasyQuery query) throws SQLException {
        if(totalScore==null){
            logger.info("totalScore为空");
            return ;
        }
        //3.新增add分数表
        //totalScore:[{entCode1:分数},{entCode2:分数},{entCode3:分数},{entCode4:分数}]
        for(String entCode : totalScore.keySet()){ //企业就四个 可放在循环里面提交
            String score = totalScore.getString(entCode);
            String taskEntId = "";
            if(StringUtils.isBlank(initMap.get(entCode))){ //新增
                taskEntId = entMap.get(entCode);
            }else{
                taskEntId = initMap.get(entCode);
            }
            String scoreId = getScoreIdByTaskEntId(taskEntId,query);

            EasyRecord record = new EasyRecord(getTableName("XTY_SCORE"),"ID");
            if(StringUtils.isBlank(scoreId)){
                record.setPrimaryValues(RandomKit.uniqueStr());
                record.set("TASK_ENT_ID",taskEntId);
                record.set("ADD_SCORE",score);
                record.set("CREATE_TIME", EasyDate.getCurrentDateString());
                record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
                query.save(record);
            }else{
                record.setPrimaryValues(scoreId);
                record.set("ADD_SCORE",score);
                record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
                query.update(record);
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 手工填报/省管局填报减分表-删除
     * @Param: []
     * @Return: com.alibaba.fastjson.JSONObject
     * @Since create in 2024/6/11 20:07
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForUpdateSubManualType() {
        JSONObject param = this.getJSONObject();
        logger.info("UpdateSubManualType参数="+param);

        String id = param.getString("id");
        String type = param.getString("type");
        String taskEntId = param.getString("taskEntId");
        if(StringUtils.isAnyBlank(id,type,taskEntId)){
            return EasyResult.fail("参数错误");
        }
        EasyQuery query = getQuery();
        try {
            EasyRecord record = new EasyRecord(getTableName("xty_score_subtract_record"),"ID");
            record.setPrimaryValues(id);
            if(!Constants.XTY_TASK_COMMIT_DEL.equals(type)){ //后续需求变更没有撤销功能
                record.set("SUB_TYPE",param.getString("type"));
                query.update(record);
            }else{
                query.deleteById(record);
            }
            //删除现在只能删除暂存数据 不需要更新xty_score
            return EasyResult.ok();
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail("操作异常！");
        }
    }


    /**
     * <AUTHOR>
     * @Description 工信部撤回/删除历史手工评分
     * @Param: []
     * @Return: com.alibaba.fastjson.JSONObject
     * @Since create in 2024/6/11 20:07
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForCancleManualTask() {
        JSONObject param = this.getJSONObject();
        logger.info("CancleManualTask参数="+param);
        String taskId = param.getString("taskId");
        String type = param.getString("type");
        if(StringUtils.isAnyBlank(taskId,type)){
            return EasyResult.fail("参数错误");
        }
        try {
            EasyRecord record = new EasyRecord(getTableName("xty_score_manual"),"TASK_ID");
            record.setPrimaryValues(taskId);
            if(!Constants.XTY_TASK_COMMIT_DEL.equals(type)){ //撤销任务的时候修改评分提交任务为撤销
                record.set("SUB_TYPE",param.getString("type"));
                record.set("ADD_TYPE",param.getString("type"));
            }
            record.set("TYPE",param.getString("type"));
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            record.set("UPDATE_USER",getUserPrincipal().getUserName());
            getQuery().update(record);
            return EasyResult.ok();
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail("操作异常！");
        }

    }


    /**
     * <AUTHOR>
     * @Description 获取关联的taskEntId
     * @Param:
     * @Return: void
     * @Since create in 2024/6/12 9:06
     * @Company 广州云趣信息科技有限公司
     */
    private String getTaskEntId(JSONObject param,EasyQuery query) throws SQLException {
        EasySQL sql = new EasySQL("select TASK_ENT_ID from "+getTableName("xty_score_task_ent"));
        sql.append("where 1=1");
        sql.append(param.getString("taskId"),"and TASK_ID = ?",false);
        sql.append(param.getString("entCode"),"and ENT_CODE = ?",false);
        sql.append(param.getString("taskProvinceId"),"and TASK_PROVINCE_ID = ?");
        sql.append(param.getString("provinceCode"),"and PROVINCE_CODE = ?");

        String taskEntId = getQuery().queryForString(sql.getSQL(),sql.getParams());
        if(StringUtils.isBlank(taskEntId)){ //这种情况是没有添加评分表 先添加了减分表
            taskEntId = RandomKit.uniqueStr();
            EasyRecord record = new EasyRecord(getTableName("xty_score_task_ent"),"TASK_ENT_ID");
            record.setPrimaryValues(taskEntId);
            record.set("TASK_ID",param.getString("taskId"));
            record.set("ENT_CODE",param.getString("entCode"));
            record.set("TASK_PROVINCE_ID",param.getString("taskProvinceId"));
            record.set("PROVINCE_CODE",param.getString("provinceCode"));
            query.save(record);
        }
        return taskEntId;
    }

    private String getScoreIdByTaskEntId(String taskEntId, EasyQuery query) throws SQLException {
        return query.queryForString("select ID from "+getTableName("xty_score") +" where TASK_ENT_ID =?",new Object[]{taskEntId});
    }


    /**
     * <AUTHOR>
     * @Description 退回省管局提交的评分任务表
     * @Param:
     * @Return:JSONObject
     * @Since create in 2024/6/6 11:52
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForReturnTask() {
        JSONObject param = this.getJSONObject();
        logger.info("ReturnTask参数="+param);
        String provinceCode = param.getString("provinceCode");
        String taskId = param.getString("taskId");
        String returnMsg = param.getString("returnMsg");
        if(StringUtils.isAnyBlank(provinceCode,taskId)){
            return EasyResult.fail("参数错误");
        }
        EasyQuery query = getQuery();
        try {
            query.begin();
            //行方评测+评测考核减分全部退回
            query.execute("UPDATE "+getTableName("xty_score_task_province")+" SET TYPE = ?,ADD_TYPE=?,SUB_TYPE=?,PLUS_TYPE=?,RETURN_MSG=? " +
                    "WHERE TASK_ID = ? AND PROVINCE_CODE = ?",new Object[]{Constants.XTY_SCORE_TASK_PROVINCE_TYPE_RETURN,
                    Constants.XTY_SCORE_TASK_PROVINCE_TYPE_RETURN,Constants.XTY_SCORE_TASK_PROVINCE_TYPE_RETURN,Constants.XTY_SCORE_TASK_PROVINCE_TYPE_RETURN,returnMsg,taskId,provinceCode});
            //评分表
            query.execute("UPDATE "+getTableName("xty_score_add_record")+" SET ADD_TYPE = ? WHERE TASK_ENT_ID IN " +
                    " ( select TASK_ENT_ID from "+ getTableName("xty_score_task_ent")+" where TASK_ID =? and PROVINCE_CODE = ? )",new Object[]{Constants.XTY_SCORE_TASK_PROVINCE_TYPE_RETURN,taskId,provinceCode});
            //减分表
            query.execute("UPDATE "+getTableName("xty_score_subtract_record")+" SET SUB_TYPE = ? WHERE TASK_ENT_ID IN " +
                    " ( select TASK_ENT_ID from "+ getTableName("xty_score_task_ent")+" where TASK_ID =? and PROVINCE_CODE = ? )",new Object[]{Constants.XTY_SCORE_TASK_PROVINCE_TYPE_RETURN,taskId,provinceCode});
            //加分表
            query.execute("UPDATE "+getTableName("xty_score_plus_record")+" SET PLUS_TYPE = ? WHERE TASK_ENT_ID IN " +
                    " ( select TASK_ENT_ID from "+ getTableName("xty_score_task_ent")+" where TASK_ID =? and PROVINCE_CODE = ? )",new Object[]{Constants.XTY_SCORE_TASK_PROVINCE_TYPE_RETURN,taskId,provinceCode});
            query.commit();
            return EasyResult.ok();
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                logger.error(ex.getMessage(), ex);
            }
            logger.error(e.getMessage(), e);
            return EasyResult.fail("系统异常");
        }
    }


    /**
     * <AUTHOR>
     * @Description 修改任务状态(撤销/关闭/延期)操作
     * @Param:
     * @Return:JSONObject
     * @Since create in 2024/6/6 11:52
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForUpdateType() {
        JSONObject param = this.getJSONObject();
        logger.info("UpdateType 参数"+param);
        String taskId = param.getString("taskId");
        String type = param.getString("type");
        if(StringUtils.isAnyBlank(taskId,type)){
            return EasyResult.fail("必传参数不能为空");
        }
        try {
            EasyRecord record = new EasyRecord(getTableName("xty_score_task"),"TASK_ID");
            record.setPrimaryValues(taskId);
            record.set("TYPE",type);
            if(StringUtils.equals(type,Constants.XTY_TASK_TYPE_DEFERRED)){
                record.set("END_TIME",param.getString("entTime"));
                record.set("TASK_COMPLET","0"); //延期后把任务标注为未完成
            }
            getQuery().update(record);
            return EasyResult.ok();
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
            return EasyResult.fail("系统异常，操作失败");
        }
    }



    /**
     * <AUTHOR>
     * @Description 省管局填报评分表分数 暂存/提交
     * @Param:
     * @Return:JSONObject
     * @Since create in 2024/6/6 11:52
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForAddTaskScore() {
        JSONObject param = this.getJSONObject();
        logger.info("AddTaskScore 参数"+param);
        String taskId = param.getString("taskId");
        String provinceCode = param.getString("provinceCode");
        String taskProvinceId = param.getString("taskProvinceId");
        JSONObject addScore = param.getJSONObject("addScore");
        JSONObject totalScore = param.getJSONObject("totalScore");
        String type = param.getString("type");
        String addObj = param.getString("addObj");
        if(StringUtils.isAnyBlank(taskId,provinceCode,taskProvinceId,type)){
            return EasyResult.fail("参数错误");
        }
        EasyQuery query = getQuery();
        try {
            query.begin();
            String opType = "add";
            if(StringUtils.isBlank(addObj)){
                addObj = "0";
            }
            //1.初始化评分表
            initAddTaskScore(taskId,taskProvinceId,query,addObj);
            //2.新增评分关联企业表-
            // addScore:{entCode1:{itemId1:分数1,itemId2:分数2},entCode2:{itemId1:分数1,itemId2:分数2}}
            //校验是否存在ent数据 （先提交减分表或者暂存都有可能存在ent数据）
            Map<String,String> initMap = getInitMap(taskId,taskProvinceId);
            Map<String,String> entMap = new HashMap<>();
            List<String> delEntList = new ArrayList<>();
            addItem(taskId,addScore,provinceCode,taskProvinceId,initMap,entMap,delEntList,query,opType,type,addObj);
            //删除编辑后没有传的entCode
            if(Constants.XTY_TASK_COMMIT_COMMITTED.equals(type)){ //提交
                if("0".equals(addObj)){
                    //省管局填报
                    updateTask(taskId,taskProvinceId,opType,query,addScore);
                }else{
                    updateGxbTask(taskProvinceId,query);
                }
                addGxbScore(totalScore,initMap,entMap,query);
            }
            query.commit();
            return EasyResult.ok();
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                logger.error(ex.getMessage(), ex);
            }
            logger.error(e.getMessage(), e);
            return EasyResult.fail("系统异常，提交失败");
        }

    }


    private void updateTask(String taskId,String taskProvinceId,String opType,EasyQuery query,JSONObject score) throws SQLException {
        //判断加/减分分表有没有提交有的话就更新任务状态为已提交
        EasyRecord taskRecord = new EasyRecord(getTableName("xty_score_task_province"),"TASK_PROVINCE_ID");
        taskRecord.setPrimaryValues(taskProvinceId);
        if("sub".equals(opType)){
            taskRecord.set("SUB_TYPE",Constants.XTY_TASK_COMMIT_COMMITTED);
        }else if("add".equals(opType)){
            taskRecord.set("ADD_TYPE",Constants.XTY_TASK_COMMIT_COMMITTED);
        }else if("plus".equals(opType)){
            taskRecord.set("PLUS_TYPE",Constants.XTY_TASK_COMMIT_COMMITTED);
        }
        boolean flag = checkCommit(taskId,taskProvinceId,opType,query);;
        if(flag){
            taskRecord.set("TYPE",Constants.XTY_TASK_COMMIT_COMMITTED);
        }
        //提交之后 加/减分审核情况变成待审核（默认不涉及）
        if(score!=null && !score.isEmpty() && !"add".equals(opType)){
            taskRecord.set("GXB_SH_TYPE",Constants.XTY_SCORE_TASK_SH_TYPE_UNCOMMITTED);
            if("sub".equals(opType)){
                taskRecord.set("GXB_SUB_TYPE",Constants.XTY_SCORE_TASK_SH_TYPE_UNCOMMITTED);
            }else{
                taskRecord.set("GXB_PLUS_TYPE",Constants.XTY_SCORE_TASK_SH_TYPE_UNCOMMITTED);
            }
        }
        query.update(taskRecord);
    }


    private void updateGxbTask(String taskProvinceId,EasyQuery query) throws SQLException {
        //判断加/减分分表有没有提交有的话就更新任务状态为已提交
        EasyRecord taskRecord = new EasyRecord(getTableName("xty_score_task_province"),"TASK_PROVINCE_ID");
        taskRecord.setPrimaryValues(taskProvinceId);
        taskRecord.set("GXB_COMMIT_TYPE","1");
        taskRecord.set("GXB_COMMIT_USER",getUserPrincipal().getUserName());
        taskRecord.set("GXB_COMMIT_TIME",EasyDate.getCurrentDateString());
        query.update(taskRecord);
    }

    private void updateGxbShTask(String taskProvinceId,String opType,EasyQuery query) {
        //判断加/减分分表有没有提交有的话就更新任务状态为已提交
        EasyRecord taskRecord = new EasyRecord(getTableName("xty_score_task_province"),"TASK_PROVINCE_ID");
        taskRecord.setPrimaryValues(taskProvinceId);
        if("sub".equals(opType)){
            taskRecord.set("GXB_SUB_TYPE",Constants.XTY_TASK_COMMIT_COMMITTED);
        }else if("plus".equals(opType)){
            taskRecord.set("GXB_PLUS_TYPE",Constants.XTY_TASK_COMMIT_COMMITTED);
        }
        taskRecord.set("GXB_SH_USER",getUserPrincipal().getUserName());
        taskRecord.set("GXB_SH_TIME",EasyDate.getCurrentDateString());
        boolean flag = false;
        try {
            flag = checkGxbSh(taskProvinceId,opType);
            if(flag){
                taskRecord.set("GXB_SH_TYPE",Constants.XTY_TASK_COMMIT_COMMITTED);
            }
            query.update(taskRecord);
        } catch (Exception e) {
            logger.error("updateGxbShTask 更新任务状态异常"+e.getMessage(), e);
        }
    }

    /**
     * <AUTHOR>
     * @Description 省管局填报减分表暂存/提交
     * @Param: []
     * @Return: com.alibaba.fastjson.JSONObject
     * @Since create in 2024/6/11 20:07
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForSubTaskScore() {
        JSONObject param = this.getJSONObject();
        String taskId = param.getString("taskId");
        String taskProvinceId = param.getString("taskProvinceId");
        String provinceCode = param.getString("provinceCode");
        String type = param.getString("type");
        String addObj = param.getString("addObj");
        if(StringUtils.isAnyBlank(taskId,taskProvinceId,provinceCode,type)){
            logger.info("参数="+param);
            return EasyResult.fail("参数错误");
        }
        if(StringUtils.isBlank(addObj)){
            addObj = "0";
        }
        EasyQuery query = getQuery();
        query.setTimeout(180);
        JSONObject subScore = param.getJSONObject("subScore");
        JSONObject totalScore = param.getJSONObject("totalScore");
        try {
//            if(Constants.XTY_TASK_COMMIT_COMMITTED.equals(type)){
//                if(!checkTotalScore(query,totalScore,year)){
//                    return EasyResult.fail("提交的减分总分超过10分,请刷新重试");
//                }
//            }
            query.begin();
            String opType = "sub";
            if("1".equals(addObj) && Constants.XTY_TASK_COMMIT_COMMITTED.equals(type)){
                addHisRecord(opType,taskProvinceId,query);
            }
            //1.初始化评分表
            initTaskScore(taskId,taskProvinceId,opType,query);
            //2.新增评分关联企业表-
            // addScore:{entCode1:{itemId1:分数1,itemId2:分数2},entCode2:{itemId1:分数1,itemId2:分数2}}
            //校验是否存在ent数据 （先提交减分表或者暂存都有可能存在ent数据）
            Map<String,String> initMap = getInitMap(taskId,taskProvinceId);
            Map<String,String> entMap = new HashMap<>();
            List<String> delEntList = new ArrayList<>();
            addItem(taskId,subScore,provinceCode,taskProvinceId,initMap,entMap,delEntList,query,opType,type,addObj);
            if(Constants.XTY_TASK_COMMIT_COMMITTED.equals(type)){ //提交
                if("0".equals(addObj)){
                    updateTask(taskId,taskProvinceId,opType,query,subScore);
                }else{
                    updateGxbShTask(taskProvinceId,opType,query);
                    addScore(totalScore,initMap,entMap,query,opType);
                }
            }
            query.commit();
            return EasyResult.ok();
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                logger.error(ex.getMessage(), ex);
            }
            logger.error(e.getMessage(), e);
            return EasyResult.fail("系统异常，保存失败");
        }
    }


    /**
     * <AUTHOR>
     * @Description 省管局填报加分表暂存/提交
     * @Param: []
     * @Return: com.alibaba.fastjson.JSONObject
     * @Since create in 2024/6/11 20:07
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForPlusTaskScore() {
        JSONObject param = this.getJSONObject();
        String taskId = param.getString("taskId");
        String taskProvinceId = param.getString("taskProvinceId");
        String provinceCode = param.getString("provinceCode");
        String type = param.getString("type");
        String addObj = param.getString("addObj");
        logger.info("PlusTaskScore省管局填报加分表暂存/提交=="+param);
        if(StringUtils.isAnyBlank(taskId,taskProvinceId,provinceCode,type)){
            logger.info("参数="+param);
            return EasyResult.fail("参数错误");
        }
        EasyQuery query = getQuery();
        query.setTimeout(180);
        JSONObject subScore = param.getJSONObject("plusScore");
        JSONObject totalScore = param.getJSONObject("totalScore");
        if(StringUtils.isBlank(addObj)){
            addObj = "0";
        }
        try {
            query.begin();
            String opType = "plus";
            if("1".equals(addObj) && Constants.XTY_TASK_COMMIT_COMMITTED.equals(type)){
                addHisRecord(opType,taskProvinceId,query);
            }
            //1.初始化评分表
            initTaskScore(taskId,taskProvinceId,opType,query);

            //2.新增评分关联企业表
            // addScore:{entCode1:{itemId1:分数1,itemId2:分数2},entCode2:{itemId1:分数1,itemId2:分数2}}
            //校验是否存在ent数据 （先提交减分表或者暂存都有可能存在ent数据）
            Map<String,String> initMap = getInitMap(taskId,taskProvinceId);
            Map<String,String> entMap = new HashMap<>();
            List<String> delEntList = new ArrayList<>();
            addItem(taskId,subScore,provinceCode,taskProvinceId,initMap,entMap,delEntList,query,opType,type,addObj);
            if(Constants.XTY_TASK_COMMIT_COMMITTED.equals(type)){ //提交
                if("0".equals(addObj)){
                    updateTask(taskId,taskProvinceId,opType,query,subScore);
                }else{
                    updateGxbShTask(taskProvinceId,opType,query);
                    addScore(totalScore,initMap,entMap,query,opType);
                }
            }
            query.commit();
            return EasyResult.ok();
        } catch (Exception e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                logger.error(ex.getMessage(), ex);
            }
            logger.error(e.getMessage(), e);
            return EasyResult.fail("系统异常，保存失败");
        }
    }


    /**
     * <AUTHOR>
     * @Description 工信部删除加/减分项 增加日志/计算总分
     * @Param: []
     * @Return: com.alibaba.fastjson.JSONObject
     * @Since create in 2024/6/11 20:07
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForDelTaskScore() {
        JSONObject param = this.getJSONObject();
        String id = param.getString("id");
        String taskProvinceId = param.getString("taskProvinceId");
        String taskEntId = param.getString("taskEntId");
        String opType = param.getString("opType");
        logger.info("DelTaskScore工信部删除=="+param);
        if(StringUtils.isAnyBlank(id,taskEntId,opType)){
            logger.info("参数="+param);
            return EasyResult.fail("参数错误");
        }
        if("subtract".equals(opType)){
            opType = "sub";
        }
        EasyQuery query = getQuery();
        query.setTimeout(180);
        try {
            query.begin();
            addHisRecord(opType,taskProvinceId,query);
            //1.初始化评分表
            delTaskScore(id,opType,taskEntId,query);
            query.commit();
        } catch (Exception e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                logger.error(ex.getMessage(), ex);
            }
            logger.error(e.getMessage(), e);
            return EasyResult.fail("系统异常，保存失败");
        }
        return EasyResult.ok();
    }
    /**
     * <AUTHOR>
     * @Description 省管局撤回提交的评分任务表
     * @Param:
     * @Return:JSONObject
     * @Since create in 2024/6/6 11:52
     * @Company 广州云趣信息科技有限公司
     */
    public JSONObject actionForCancelTask() {
        JSONObject param = this.getJSONObject();
        logger.info("cancelTask param="+param);
        String taskId = param.getString("taskId");
        String taskProvinceId = param.getString("taskProvinceId");
        if(StringUtils.isAnyBlank(taskId,taskProvinceId)){
            return EasyResult.fail("必传参数不能为空");
        }
        EasyQuery query = getQuery();
        try {
            query.begin();
            EasyRecord record = new EasyRecord(getTableName("xty_score_task"),"TASK_ID");
            record.setPrimaryValues(taskId);
            Map<String, String> task = getQuery().findById(record);
            if(Constants.XTY_TASK_TYPE_CANCEL.equals(task.get("TYPE")) || Constants.XTY_TASK_TYPE_CLOSE.equals(task.get("TYPE"))){
                return EasyResult.fail("当前任务状态不允许撤回");
            }
            if("1".equals(task.get("TASK_COMPLET"))){
                return EasyResult.fail("当前任务已完成不允许撤回");
            }
            EasyRecord provinceRecord = new EasyRecord(getTableName("xty_score_task_province"),"TASK_PROVINCE_ID");
            provinceRecord.setPrimaryValues(taskProvinceId);
            provinceRecord.set("TYPE",Constants.XTY_SCORE_TASK_PROVINCE_TYPE_CANCEL);
            provinceRecord.set("ADD_TYPE",Constants.XTY_SCORE_TASK_PROVINCE_TYPE_CANCEL);
            provinceRecord.set("SUB_TYPE",Constants.XTY_SCORE_TASK_PROVINCE_TYPE_CANCEL);
            provinceRecord.set("PLUS_TYPE",Constants.XTY_SCORE_TASK_PROVINCE_TYPE_CANCEL);
            query.update(provinceRecord);
            query.execute("UPDATE "+getTableName("xty_score_add_record") +" set ADD_TYPE = ? where TASK_ENT_ID in " +
                    "( select TASK_ENT_ID from "+getTableName("xty_score_task_ent")+" where TASK_PROVINCE_ID=? )",new Object[]{Constants.XTY_SCORE_TASK_PROVINCE_TYPE_CANCEL,taskProvinceId});

            query.execute("UPDATE "+getTableName("xty_score_subtract_record") +" set SUB_TYPE = ? where TASK_ENT_ID in " +
                    "( select TASK_ENT_ID from "+getTableName("xty_score_task_ent")+" where TASK_PROVINCE_ID=? )",new Object[]{Constants.XTY_SCORE_TASK_PROVINCE_TYPE_CANCEL,taskProvinceId});

            query.execute("UPDATE "+getTableName("xty_score_plus_record") +" set PLUS_TYPE = ? where TASK_ENT_ID in " +
                    "( select TASK_ENT_ID from "+getTableName("xty_score_task_ent")+" where TASK_PROVINCE_ID=? )",new Object[]{Constants.XTY_SCORE_TASK_PROVINCE_TYPE_CANCEL,taskProvinceId});
            query.commit();
            return EasyResult.ok();
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                logger.error(ex.getMessage(), ex);
            }
            logger.error(e.getMessage(), e);
            return EasyResult.fail("系统异常，操作失败");
        }
    }

    private void addHisRecord(String opType, String taskProvinceId, EasyQuery query) throws SQLException {
        String tableName = getScoreTableName(opType);
        if (StringUtils.isBlank(tableName)) {
            logger.error("无效的操作类型: " + opType);
            throw new SQLException("无效的操作类型");
        }
        EasySQL sql = new EasySQL("select t1.* from");
        sql.append(tableName + " t1 ");
        // 查询当前减分表的数据
        sql.append("inner join " + getTableName("xty_score_task_ent") + " t2 on t1.TASK_ENT_ID = t2.TASK_ENT_ID ");
        sql.append("where 1=1  ");
        sql.append(taskProvinceId,"and t2.TASK_PROVINCE_ID = ?");
        if("sub".equals(opType)){
            sql.append(Constants.XTY_TASK_COMMIT_COMMITTED,"and t1.SUB_TYPE = ?");
        }else{
            sql.append(Constants.XTY_TASK_COMMIT_COMMITTED,"and t1.PLUS_TYPE = ?");
        }
        List<JSONObject> records = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        String hisId = RandomKit.uniqueStr();
        // 将数据插入到历史记录表
        for (JSONObject record : records) {
            EasyRecord hisRecord = new EasyRecord(getTableName("xty_score_his_record"), "ID");
            hisRecord.setPrimaryValues(RandomKit.uniqueStr());
            hisRecord.set("HIS_ID", hisId);
            hisRecord.set("TASK_ID", record.getString("TASK_ID"));
            hisRecord.set("TASK_ENT_ID", record.getString("TASK_ENT_ID"));
            hisRecord.set("DESCS", record.getString("DESCS"));
            hisRecord.set("SCORE", record.getString("SCORE"));
            if("sub".equals(opType)){
                hisRecord.set("COMMIT_TYPE", record.getString("SUB_TYPE"));
                hisRecord.set("FILE_ID", record.getString("SUB_FILE_ID"));
                hisRecord.set("SCORE_TYPE", "subtract");
            }else{
                hisRecord.set("COMMIT_TYPE", record.getString("PLUS_TYPE"));
                hisRecord.set("FILE_ID", record.getString("PLUS_FILE_ID"));
                hisRecord.set("SCORE_TYPE", "plus");
            }
            hisRecord.set("CREATE_TIME", record.getString("CREATE_TIME"));
            hisRecord.set("CREATE_USER", record.getString("CREATE_USER"));
            hisRecord.set("ADD_OBJ", record.getString("ADD_OBJ"));
            query.save(hisRecord);
        }
    }

}
