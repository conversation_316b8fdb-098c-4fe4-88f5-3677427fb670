package com.yunqu.tariff.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.utils.RuleCheckUtils;
import com.yunqu.tariff.utils.RuleCheckUtilsExt;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import com.yunqu.xty.commonex.util.RedissonUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Pattern;

/**
 * <p>
 * 资费字段检查服务
 * </p>
 *
 * @ClassName TariffCheckService
 * <AUTHOR> Copy This Tag)
 * @Description 资费字段检查服务
 * @Since create in 7/1/24 10:04 AM
 * @Version v1.0
 * @Copyright Copyright (c) 2024
 * @Company 广州云趣信息科技有限公司
 */
public class TariffCheckService {
    private static final Logger joblogger = LoggerFactory.getLogger(CommonLogger.getcheckJobLogger().getName());
    
    // 检查类型：报送库
    public static final int CHECK_TYPE_REPORT = 1;
    // 检查类型：公示库
    public static final int CHECK_TYPE_PUBLIC = 2;
    // 检查类型：顺序检查
    public static final int CHECK_TYPE_SEQUENTIAL = 3;

    // 缓存键：报送库检查任务运行状态
    public static final String REPORT_CHECK_RUNNING_KEY = "TARIFF_REPORT_CHECK_RUNNING";
    // 缓存键：公示库检查任务运行状态
    public static final String PUBLIC_CHECK_RUNNING_KEY = "TARIFF_PUBLIC_CHECK_RUNNING";
    // 缓存键：顺序检查任务运行状态
    public static final String SEQUENTIAL_CHECK_KEY = "TARIFF_SEQUENTIAL_CHECK_RUNNING";
    // 缓存键：检查任务运行状态（兼容旧版本）
    public static final String CHECK_RUNNING_KEY = "TARIFF_CHECK_RUNNING";
    // 缓存键：所有规则缓存
    public static final String ALL_RULES_CACHE_KEY = "ALL_TARIFF_CHECK_RULES";
    
    // 规则列表
    private List<JSONObject> ruleList;

    /**
     * 获取检查类型的名称
     * @param checkType 检查类型
     * @return 检查类型的名称
     */
    public static String getCheckTypeName(int checkType) {
        switch (checkType) {
            case CHECK_TYPE_REPORT:
                return "报送库";
            case CHECK_TYPE_PUBLIC:
                return "公示库";
            case CHECK_TYPE_SEQUENTIAL:
                return "顺序检查";
            default:
                return "未知类型";
        }
    }

    /**
     * 检查是否有任意类型的检查任务正在运行
     * @return true表示有检查任务正在运行，false表示没有
     */
    public static boolean isAnyCheckRunning() {

        return isReportCheckRunning() || isPublicCheckRunning() || isSequentialCheckRunning() || isLegacyCheckRunning();
    }

    /**
     * 检查是否有报送库检查任务正在运行
     * @return true表示有报送库检查任务正在运行，false表示没有
     */
    public static boolean isReportCheckRunning() {
        return RedissonUtil.exist(REPORT_CHECK_RUNNING_KEY);
    }

    /**
     * 检查是否有公示库检查任务正在运行
     * @return true表示有公示库检查任务正在运行，false表示没有
     */
    public static boolean isPublicCheckRunning() {
        return RedissonUtil.exist(PUBLIC_CHECK_RUNNING_KEY);
    }

    /**
     * 检查是否有顺序检查任务正在运行
     * @return true表示有顺序检查任务正在运行，false表示没有
     */
    public static boolean isSequentialCheckRunning() {
        return RedissonUtil.exist(SEQUENTIAL_CHECK_KEY);
    }

    /**
     * 检查是否有使用旧缓存键的检查任务正在运行（兼容旧版本）
     * @return true表示有使用旧缓存键的检查任务正在运行，false表示没有
     */
    public static boolean isLegacyCheckRunning() {
        return RedissonUtil.exist(CHECK_RUNNING_KEY);
    }

    /**
     * 检查是否有检查任务正在运行（兼容旧版本）
     * @return true表示有检查任务正在运行，false表示没有
     */
    public static boolean isCheckRunning() {
        return isAnyCheckRunning();
    }
    
    /**
     * 设置检查任务运行状态
     * @param runningInfo 运行信息
     * @param checkType 检查类型
     */
    public static void setCheckRunning(JSONObject runningInfo, int checkType) {
        String cacheKey = getCacheKeyByType(checkType);
        if (cacheKey != null) {
            RedissonUtil.setEx(cacheKey, runningInfo.toJSONString(), 24 * 60 * 60); // 24小时过期
            joblogger.info("设置{}检查任务运行状态: {}", getCheckTypeName(checkType), runningInfo.toJSONString());
        }
    }
    
    /**
     * 清除检查任务运行状态
     * @param checkType 检查类型
     */
    public static void clearCheckRunning(int checkType) {
        String cacheKey = getCacheKeyByType(checkType);
        if (cacheKey != null) {
            RedissonUtil.del(cacheKey);
            joblogger.info("清除{}检查任务运行状态", getCheckTypeName(checkType));
        }
    }

    /**
     * 清除所有检查任务运行状态
     */
    public static void clearAllCheckRunning() {
        RedissonUtil.del(REPORT_CHECK_RUNNING_KEY);
        RedissonUtil.del(PUBLIC_CHECK_RUNNING_KEY);
        RedissonUtil.del(SEQUENTIAL_CHECK_KEY);
        RedissonUtil.del(CHECK_RUNNING_KEY);
        joblogger.info("清除所有检查任务运行状态");
    }

    /**
     * 根据检查类型获取对应的缓存键
     * @param checkType 检查类型
     * @return 缓存键
     */
    private static String getCacheKeyByType(int checkType) {
        switch (checkType) {
            case CHECK_TYPE_REPORT:
                return REPORT_CHECK_RUNNING_KEY;
            case CHECK_TYPE_PUBLIC:
                return PUBLIC_CHECK_RUNNING_KEY;
            case CHECK_TYPE_SEQUENTIAL:
                return SEQUENTIAL_CHECK_KEY;
            default:
                return CHECK_RUNNING_KEY;
        }
    }

    /**
     * 加载规则配置
     * @param ruleNo 规则编号，如果为null则加载所有规则
     */
    private void loadRuleConfig(String ruleNo) {
        try {
            // 构建SQL
            EasySQL sqlBuilder = new EasySQL();
            sqlBuilder.append("SELECT * FROM ").append(Constants.getBusiSchema()).append(".xty_tariff_check_rule");

            List<Object> params = new ArrayList<>();

            // 如果指定了规则编号，则只加载该规则
            if (StringUtils.isNotBlank(ruleNo)) {
                sqlBuilder.append(" WHERE 1=1");
                sqlBuilder.appendIn(ruleNo.split(","), " and RULE_NO ");
                joblogger.info("加载指定规则配置，规则编号: {}", ruleNo);
            } else {
                joblogger.info("加载所有规则配置");
            }

            sqlBuilder.append(" ORDER BY ID asc");

            // 执行查询
            EasyQuery query = QueryFactory.getWriteQuery();
            ruleList = query.queryForList(sqlBuilder.getSQL(), sqlBuilder.getParams(), new JSONMapperImpl());
            
            if (ruleList == null || ruleList.isEmpty()) {
                joblogger.info("未找到规则配置，规则编号: {}", StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo);
                ruleList = new ArrayList<>();
            } else {
                joblogger.info("已加载{}条规则配置", ruleList.size());
                
                // 预编译所有规则中的正则表达式
                for (JSONObject rule : ruleList) {
                    String configJson = rule.getString("CONFIG_JSON");
                    if (StringUtils.isNotBlank(configJson)) {
                        try {
                            JSONObject config = JSONObject.parseObject(configJson);
                            String ex1 = config.getString("ex1");
                            if (StringUtils.isNotBlank(ex1)) {
                                for (String pattern : ex1.split(",")) {
                                    if (pattern.contains("*") || pattern.contains("(") || pattern.contains("[")) {
                                        // 可能是正则表达式，预编译
                                        try {
                                            RuleCheckUtils.getCompiledPattern(pattern);
                                        } catch (Exception e) {
                                            // 忽略无效的正则表达式
                                        }
                                    }
                                }
            }
        } catch (Exception e) {
                            // 忽略解析错误
                        }
                    }
                }
            }
        } catch (Exception e) {
            joblogger.error("加载规则配置失败", e);
            ruleList = new ArrayList<>();
        }
    }

    /**
     * 加载所有规则配置
     */
    private void loadRuleConfig() {
        loadRuleConfig(null);
    }

    /**
     * 检查资费字段
     * @param startDate 开始日期，格式yyyyMMdd，为空则默认为昨天
     * @param endDate 结束日期，格式yyyyMMdd，为空则默认为昨天
     * @param isAll 是否全量检查，true表示检查所有记录，false表示只检查FIELD_CHECK_TIME为空的记录
     * @param tariffId 资费ID，如果不为空，则只检查指定ID的资费记录
     * @param ruleNo 规则编号，如果不为空，则只检查指定规则编号
     * @param checkType 检查类型，1为报送库，2为公示库
     */
    public void checkTariffFields(String startDate, String endDate, Boolean isAll, String tariffId, String ruleNo, int checkType) {
        try {
            // 检查是否有相应类型的检查任务正在运行
            boolean isTaskRunning = false;
            switch (checkType) {
                case CHECK_TYPE_REPORT:
                    isTaskRunning = isReportCheckRunning();
                    break;
                case CHECK_TYPE_PUBLIC:
                    isTaskRunning = isPublicCheckRunning();
                    break;
                default:
                    // 对于未知类型，检查所有类型的任务
                    isTaskRunning = isAnyCheckRunning();
                    break;
            }

            // 如果存在相应类型的检查任务，则不允许启动新任务
            if (isTaskRunning) {
                joblogger.warn("已有{}检查任务正在运行，无法启动新的检查任务，规则编号: {}",
                        getCheckTypeName(checkType),
                        StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo);
                return;
            }

            // 记录运行信息
            JSONObject runningInfo = new JSONObject();
            runningInfo.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            runningInfo.put("startDate", startDate);
            runningInfo.put("endDate", endDate);
            runningInfo.put("isAll", isAll);
            runningInfo.put("tariffId", tariffId);
            runningInfo.put("ruleNo", ruleNo);
            runningInfo.put("checkType", checkType);

            // 设置检查任务运行状态
            setCheckRunning(runningInfo, checkType);

        try {
            // 加载规则配置
                loadRuleConfig(ruleNo);
            
            // 处理日期参数
            String startDateStr = startDate;
            String endDateStr = endDate;
            
                // 如果指定了资费ID，则直接检查该ID的记录
                if (StringUtils.isNotBlank(tariffId)) {
                    checkTariffById(tariffId, ruleNo, checkType);
                    return;
                }

                // 构建查询SQL - 只查询ID
                EasySQL idSqlBuilder = new EasySQL();
                EasyQuery query = null;
                // 根据检查类型选择不同的表
                if (checkType == CHECK_TYPE_PUBLIC) {
                    idSqlBuilder.append("SELECT id as ID FROM " + Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib WHERE 1=1");
                    query = QueryFactory.getTariffQuery();
                } else {
                    idSqlBuilder.append("SELECT ID FROM " + Constants.getBusiSchema() + ".xty_tariff_record WHERE 1=1");
                    query = QueryFactory.getReadQuery();
                }
            
            // 添加日期范围条件
                if (StringUtils.isBlank(ruleNo)) {
                    if (checkType == CHECK_TYPE_PUBLIC) {
                        idSqlBuilder.append(startDateStr, " AND date_id >= ?");
                        idSqlBuilder.append(endDateStr, " AND date_id <= ?");
                    } else {
                        // 报送库使用DATE_ID字段
                        idSqlBuilder.append(startDateStr, " AND DATE_ID >= ?");
                        idSqlBuilder.append(endDateStr, " AND DATE_ID <= ?");
                    }
                }
            
            // 如果不是全量检查，则只检查FIELD_CHECK_TIME为空的记录
            if (isAll == null || !isAll) {
                    if (checkType == CHECK_TYPE_PUBLIC) {
                        idSqlBuilder.append(" AND (field_check_time IS NULL OR field_check_time = '')");
                    } else {
                        idSqlBuilder.append(" AND (FIELD_CHECK_TIME IS NULL OR FIELD_CHECK_TIME = '')");
                    }
                }

                joblogger.info("开始检查资费字段，查询条件：开始日期={}, 结束日期={}, 是否全量={}, 规则编号={}, 检查类型={}",
                        startDateStr, endDateStr, isAll, StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                        getCheckTypeName(checkType));

                // 获取可用处理器数量，用于优化线程池大小
                int availableProcessors = Runtime.getRuntime().availableProcessors();
                int optimalThreadCount = Math.max(2, Math.min(availableProcessors * 2, 20)); // 最少2个线程，最多20个线程

                joblogger.info("系统可用处理器数: {}, 优化后的线程数: {}", availableProcessors, optimalThreadCount);

                // 创建线程池
                ExecutorService executorService = Executors.newFixedThreadPool(optimalThreadCount);

                // 使用流式处理，边查询边处理
                processRecordsInStreams(idSqlBuilder, query, executorService, optimalThreadCount, ruleNo, checkType);

                // 关闭线程池
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(24, TimeUnit.HOURS)) {
                        joblogger.warn("线程池未在指定时间内完成，强制关闭");
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    joblogger.error("关闭线程池时被中断: {}", e.getMessage(), e);
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }

                joblogger.info("资费字段检查完成，规则编号: {}, 检查类型: {}", 
                        StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                        getCheckTypeName(checkType));
            } finally {
                // 清除检查任务运行状态
                clearCheckRunning(checkType);
            }
        } catch (Exception e) {
            // 发生异常时，确保清除运行状态
            try {
                clearCheckRunning(checkType);
            } catch (Exception ex) {
                joblogger.error("清除检查任务运行状态时发生错误", ex);
            }
            joblogger.error("检查资费字段时发生错误", e);
        }
    }
    
    /**
     * 使用流式处理方式处理记录
     * @param idSqlBuilder ID查询SQL构建器
     * @param query 查询对象
     * @param executorService 线程池
     * @param threadCount 线程数量
     * @param ruleNo 规则编号，如果为null则检查所有规则
     * @param checkType 检查类型，1为报送库，2为公示库
     */
    private void processRecordsInStreams(EasySQL idSqlBuilder, EasyQuery query, ExecutorService executorService, int threadCount, String ruleNo, int checkType) {
        try {
            int pageSize = 1000;
            int pageIndex = 1;
//            任务队列，用于跟踪所有提交的任务
            List<Future<?>> taskList = new ArrayList<>();
            joblogger.info("很重要的待检查sql语句："+idSqlBuilder.toFullSql());
//            //todo
            // 分页查询并处理
            while (true) {
                // 查询当前页的ID列表
                List<Map<String, String>> idList = query.queryForList(
                        idSqlBuilder.getSQL(),
                        idSqlBuilder.getParams(),
                        pageIndex,
                        pageSize,
                        new MapRowMapperImpl()
                );

                if (idList == null || idList.isEmpty()) {
                    break; // 没有更多数据，退出循环
                }

                joblogger.info("查询到第{}页数据，本页{}条记录", pageIndex, idList.size());

                // 提交当前页的处理任务
                Future<?> task = executorService.submit(() -> processPageIds(idList, ruleNo, checkType));
                taskList.add(task);

                // 控制任务提交速度，避免提交太多任务导致内存压力
                if (taskList.size() >= threadCount * 2) {
                    // 等待一些任务完成后再继续提交
                    waitForSomeTasks(taskList);
                }

                pageIndex++;
            }

//            // 查询当前页的ID列表
//            List<Map<String, String>> idList = query.queryForList(
//                    idSqlBuilder.getSQL(),
//                    idSqlBuilder.getParams(),
//                    pageIndex,
//                    pageSize,
//                    new MapRowMapperImpl()
//            );
//
//            if (idList != null && !idList.isEmpty()) {
//                joblogger.info("查询到第{}页数据，本页{}条记录", pageIndex, idList.size());
//
//                // 提交当前页的处理任务
//                Future<?> task = executorService.submit(() -> processPageIds(idList, ruleNo, checkType));
//                taskList.add(task);
//            }

            // 控制任务提交速度，避免提交太多任务导致内存压力
            if (taskList.size() >= threadCount * 2) {
                // 等待一些任务完成后再继续提交
                waitForSomeTasks(taskList);
            }

            // 等待所有任务完成
            for (Future<?> task2 : taskList) {
                try {
                    task2.get();
                } catch (Exception e) {
                    joblogger.error("任务执行异常: {}", e.getMessage(), e);
                }
            }

            joblogger.info("所有页面处理完成");
        } catch (Exception e) {
            joblogger.error("流式处理记录时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 等待部分任务完成
     * @param taskList 任务列表
     */
    private void waitForSomeTasks(List<Future<?>> taskList) {
        List<Future<?>> completedTasks = new ArrayList<>();

        for (Future<?> task : taskList) {
            if (task.isDone()) {
                completedTasks.add(task);
            }
        }

        // 如果没有完成的任务，则等待第一个任务完成
        if (completedTasks.isEmpty() && !taskList.isEmpty()) {
            try {
                taskList.get(0).get();
                completedTasks.add(taskList.get(0));
            } catch (Exception e) {
                joblogger.error("等待任务完成时发生错误: {}", e.getMessage(), e);
            }
        }

        // 从任务列表中移除已完成的任务
        taskList.removeAll(completedTasks);
    }

    /**
     * 处理一页ID列表
     * @param idList ID列表
     * @param ruleNo 规则编号，如果为null则检查所有规则
     * @param checkType 检查类型，1为报送库，2为公示库
     */
    private void processPageIds(List<Map<String, String>> idList, String ruleNo, int checkType) {
        try {
            if (idList == null || idList.isEmpty()) {
                return;
            }

            // 提取ID列表
            List<String> ids = new ArrayList<>();
            for (Map<String, String> idMap : idList) {
                ids.add(idMap.get("ID"));
            }

            List<JSONObject> tariffList;
            
            // 根据检查类型选择不同的查询方式
            if (checkType == CHECK_TYPE_PUBLIC) {
                // 从ES查询公示库记录
                tariffList = queryPublicLibRecordsFromES(ids);
            } else {
                // 从ES查询报送库记录
                tariffList = queryReportRecordsFromES(ids);
            }

            if (tariffList == null || tariffList.isEmpty()) {
                joblogger.debug("没有找到有效的资费记录");
                return;
            }

            joblogger.debug("找到{}条有效资费记录", tariffList.size());

            // 检查每条记录
            for (JSONObject tariff : tariffList) {
                checkSingleTariff(tariff, ruleNo, checkType);
            }

            joblogger.debug("页面处理完成，共处理{}条记录", tariffList.size());
        } catch (Exception e) {
            joblogger.error("处理页面ID时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查单条资费记录
     * @param tariff 资费记录
     * @param ruleNo 规则编号，如果为null则检查所有规则
     * @param checkType 检查类型，1为报送库，2为公示库
     */
    private void checkSingleTariff(JSONObject tariff, String ruleNo, int checkType) {
        try {
            String tariffId = tariff.getString("ID");
            List<String> checkResults = new ArrayList<>();
            List<String> ruleNos = new ArrayList<>();

            // 获取记录的关键信息，用于规则筛选
            String type1 = tariff.getString("TYPE1");
        String type2 = tariff.getString("TYPE2");

            if(checkType == CHECK_TYPE_PUBLIC){
                type1 = tariff.getString("CLASSIC_TYPE_ONE");
                type2 = tariff.getString("CLASSIC_TYPE_TWO");
            }

            // 如果指定了规则编号，只检查该规则
            if (StringUtils.isNotBlank(ruleNo)) {
                // 处理多个规则编号的情况（逗号分隔）
                String[] ruleNoArray = ruleNo.split(",");
                
                for (String singleRuleNo : ruleNoArray) {
                    if (StringUtils.isBlank(singleRuleNo)) {
                        continue;
                    }
                    
                    singleRuleNo = singleRuleNo.trim();
                    
                    // 从ruleList中找到对应的规则
                    JSONObject targetRule = null;
                    for (JSONObject rule : ruleList) {
                        String currentRuleNo = rule.getString("RULE_NO");
                        if (singleRuleNo.equals(currentRuleNo)) {
                            targetRule = rule;
                            break;
                        }
                    }

                    if (targetRule == null) {
                        joblogger.warn("未找到规则编号为 {} 的规则，跳过该规则检查", singleRuleNo);
                        continue;
                    }

                    int ruleNoInt = targetRule.getIntValue("RULE_NO");
                    String ruleDesc = targetRule.getString("RULE_DESC");
                    boolean hasIssue = checkRuleForTariff(tariff, targetRule, ruleNoInt, type1, type2, checkType);

                    if (hasIssue) {
                        checkResults.add("【" + ruleDesc + "】");
                        ruleNos.add(String.valueOf(ruleNoInt));
                    }
                }
            } else {
                // 遍历所有规则进行检查
                for (JSONObject rule : ruleList) {
                    String ruleId = rule.getString("ID");
                    String ruleDesc = rule.getString("RULE_DESC");
                    int ruleNoInt = rule.getIntValue("RULE_NO");
                    boolean hasIssue = checkRuleForTariff(tariff, rule, ruleNoInt, type1, type2, checkType);

                    if (hasIssue) {
                        checkResults.add("【" + ruleDesc + "】");
                        ruleNos.add(String.valueOf(ruleNoInt));
                    }
                }
            }

            // 更新检查结果和检查时间
            String checkResult = String.join("，", checkResults);
            String checkNoStr = String.join(",", ruleNos);
            String checkTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            updateTariffCheckResult(tariff, checkResult, checkTime, checkNoStr, ruleNo, checkType);
        } catch (Exception e) {
            joblogger.error("检查资费记录时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 为单个资费记录检查特定规则
     * @param tariff 资费记录
     * @param rule 规则对象
     * @param ruleNo 规则编号
     * @param type1 资费一级分类
     * @param type2 资费二级分类
     * @param checkType 检查类型，1为报送库，2为公示库
     * @return 是否存在问题
     */
    private boolean checkRuleForTariff(JSONObject tariff, JSONObject rule, int ruleNo, String type1, String type2, int checkType) {
        // 根据检查类型和规则编号进行规则筛选
        if (checkType == CHECK_TYPE_REPORT) {
            // 报送库规则筛选逻辑
            if (ruleNo == 7 && !"1".equals(type2)) {
                // 规则7（免费业务附加在网期限）只适用于套餐（type2=1）
            return false;
        }
        
            if (ruleNo == 14 && !"2".equals(type2)) {
                // 规则14（加装包附加限制性条件）只适用于加装包（type2=2）
            return false;
        }
        
            if (ruleNo == 15 && !"3".equals(type2)) {
                // 规则15（营销活动附加限制性条件）只适用于营销活动（type2=3）
                return false;
            }
            
            if (ruleNo == 16 && !"1".equals(type2)) {
                // 规则16（套餐附加限制性条件）只适用于套餐（type2=1）
                return false;
            }
        } else if (checkType == CHECK_TYPE_PUBLIC) {
            // 公示库规则筛选逻辑 - 根据需求修改
            if (StringUtils.isBlank(type2)) {
                // 根据要求，如果没有二级分类，则对于以下规则返回false
                if (ruleNo == 2 || ruleNo == 14 || ruleNo == 15 || ruleNo == 16) {
                    return false;
                }
            }
        }
        
        String tariffId = checkType == CHECK_TYPE_PUBLIC ? tariff.getString("id") : tariff.getString("ID");
        
        // 从规则配置中获取ex1和ex2参数
        String[] ex1Array = getConfigArray(rule, "ex1");
        String[] ex2Array = getConfigArray(rule, "ex2");

        // 根据规则序号执行不同的检查逻辑，并根据检查类型选择不同的字段
        switch (ruleNo) {
            case 1: // 资费名称重复
                if (checkType == CHECK_TYPE_PUBLIC) {
                    // 公示库的资费名称重复检查逻辑 - 通过省份、企业和名称判断
                    String name = tariff.getString("NAME");
                    String provinceCode = tariff.getString("PROVINCE_CODE");
                    String entCode = tariff.getString("ENT_CODE");
                    return checkNameDuplicateInPublicLib(tariffId, name, provinceCode, entCode);
                } else {
                    // 报送库的资费名称重复检查逻辑
                    String tariffName = tariff.getString("NAME");
                    String reporter = tariff.getString("REPORTER");
                    return checkNameDuplicate(tariffId, tariffName, reporter);
                }

            case 2: // 资费标准缺失
                if (checkType == CHECK_TYPE_PUBLIC) {
                    // 公示库的资费标准缺失检查 - 如果没有二级分类则返回false
                    if (StringUtils.isBlank(type2)) {
                        return false;
                    }
                    String fees = tariff.getString("FEES");
                    return RuleCheckUtils.checkFeesMissingPublic(tariffId, type2, fees);
                } else {
                    // 报送库的资费标准缺失检查 - 检查资费标准、超出资费、其他费用同时为空
                    String fees = tariff.getString("FEES");
                    String exceedFees = tariff.getString("EXTRA_FEES");
                    String otherFees = tariff.getString("OTHER_FEES");
                    return RuleCheckUtils.checkFeesMissingWithAll(tariffId, type2, fees, exceedFees, otherFees);
                }

            case 3: // 服务内容缺失
                if (checkType == CHECK_TYPE_PUBLIC) {
                    // 公示库的服务内容缺失检查
                    String[] contentFields = new String[] {
                            tariff.getString("CALL"),
                            tariff.getString("DATA"),
                            tariff.getString("SMS"),
                            tariff.getString("ORIENT_TRAFFIC"),
                            tariff.getString("IPTV"),
                            tariff.getString("BANDWIDTH"),
                            tariff.getString("RIGHTS"),
                            tariff.getString("OTHER_CONTENT")
                    };
                    return RuleCheckUtils.checkContentMissing(tariffId, contentFields, ex1Array);
                } else {
                    // 报送库的服务内容缺失检查
                    String[] contentFields = new String[] {
                        tariff.getString("CALL_NUM"),
                        tariff.getString("DATA_NUM"),
                        tariff.getString("SMS_NUM"),
                        tariff.getString("ORIENT_TRAFFIC"),
                        tariff.getString("IPTV"),
                        tariff.getString("BANDWIDTH"),
                        tariff.getString("RIGHTS"),
                        tariff.getString("OTHER_CONTENT")
                    };
                    return RuleCheckUtils.checkContentMissing(tariffId, contentFields, ex1Array);
                }

            case 4: // 适用范围填写不规范
                if (checkType == CHECK_TYPE_PUBLIC) {
                    String applicablePeople = tariff.getString("APPLICABLE_PEOPLE");
                    return RuleCheckUtils.checkApplicablePeopleIrregular(tariffId, applicablePeople, ex1Array);
                } else {
                    String applicablePeople = tariff.getString("APPLICABLE_PEOPLE");
                    return RuleCheckUtils.checkApplicablePeopleIrregular(tariffId, applicablePeople, ex1Array);
                }

            case 5: // 有效期限填写不规范
                if (checkType == CHECK_TYPE_PUBLIC) {
                    String validPeriod = tariff.getString("VALID_PERIOD");
                    return RuleCheckUtils.checkValidPeriodIrregular(validPeriod, ex1Array);
                } else {
                    String validPeriod = tariff.getString("VALID_PERIOD");
                    return RuleCheckUtils.checkValidPeriodIrregular(validPeriod, ex1Array);
                }

            case 6: // 销售渠道填写不规范
                if (checkType == CHECK_TYPE_PUBLIC) {
                    String channel = tariff.getString("CHANNEL");
                    return RuleCheckUtils.checkChannelIrregular(channel, ex1Array);
                } else {
                    String channel = tariff.getString("CHANNEL");
                    return RuleCheckUtils.checkChannelIrregular(channel, ex1Array);
                }

            case 7: // 免费业务附加在网期限
                if (checkType == CHECK_TYPE_PUBLIC) {
//                    joblogger.info("tariff:"+tariff.toJSONString());
                    String fees = tariff.getString("FEES");
                    String duration = tariff.getString("DURATION");
                    return RuleCheckUtilsExt.checkFreeTariffWithDuration(type2, fees, duration, null, ex1Array);
                } else {
                    String fees = tariff.getString("FEES");
                    String duration = tariff.getString("DURATION");
                    return RuleCheckUtilsExt.checkFreeTariffWithDuration(type2, fees, duration, null, ex1Array);
                }

            case 8: // 退订方式填写不规范
                if (checkType == CHECK_TYPE_PUBLIC) {
                    String unsubscribe = tariff.getString("UNSUBSCRIBE");
                    return RuleCheckUtils.checkUnsubscribeIrregular(unsubscribe, ex1Array, ex2Array);
                } else {
                    String unsubscribe = tariff.getString("UNSUBSCRIBE");
                    return RuleCheckUtils.checkUnsubscribeIrregular(unsubscribe, ex1Array, ex2Array);
                }

            case 9: // 违约责任填写不规范
                if (checkType == CHECK_TYPE_PUBLIC) {
                    String responsibility = tariff.getString("RESPONSIBILITY");
                    return RuleCheckUtils.checkResponsibilityIrregular(tariffId, responsibility, ex1Array);
                } else {
                    String responsibility = tariff.getString("RESPONSIBILITY");
                    return RuleCheckUtils.checkResponsibilityIrregular(tariffId, responsibility, ex1Array);
                }

            case 10: // 下线时间填写不规范
                if (checkType == CHECK_TYPE_PUBLIC) {
                    String offlineDay = tariff.getString("OFFLINE_DAY");
                    return RuleCheckUtils.checkOfflineDayIrregular(offlineDay, ex1Array);
                } else {
                    String offlineDay = tariff.getString("OFFLINE_DAY");
                    return RuleCheckUtils.checkOfflineDayIrregular(offlineDay, ex1Array);
                }

            case 11: // 资费方案内容冗长
                if (checkType == CHECK_TYPE_PUBLIC) {
                    // 公示库的资费方案内容冗长检查
                    String[] contentLengthFields = new String[] {
                            tariff.getString("NAME"),
                            tariff.getString("FEES"),
                            tariff.getString("FEES_UNIT"),
                            tariff.getString("EXCEED_FEES"),
                            tariff.getString("OTHER_FEES"),
                            tariff.getString("CALL"),
                            tariff.getString("DATA"),
                            tariff.getString("DATA_UNIT"),
                            tariff.getString("SMS"),
                            tariff.getString("ORIENT_TRAFFIC"),
                            tariff.getString("ORIENT_TRAFFIC_UNIT"),
                            tariff.getString("IPTV"),
                            tariff.getString("BANDWIDTH"),
                            tariff.getString("RIGHTS"),
                            tariff.getString("OTHER_CONTENT"),
                            tariff.getString("APPLICABLE_PEOPLE"),
                            tariff.getString("VALID_PERIOD"),
                            tariff.getString("CHANNEL"),
                            tariff.getString("DURATION"),
                            tariff.getString("UNSUBSCRIBE"),
                            tariff.getString("RESPONSIBILITY"),
                            tariff.getString("OTHERS"),
                            tariff.getString("ONLINE_DAY"),
                            tariff.getString("OFFLINE_DAY")
                    };
                    return RuleCheckUtils.checkContentTooLongEnhanced(contentLengthFields);
                } else {
                    // 报送库的资费方案内容冗长检查
                    String[] contentLengthFields = new String[] {
                            tariff.getString("NAME"),
                            tariff.getString("FEES"),
                            tariff.getString("FEES_UNIT"),
                            tariff.getString("EXCEED_FEES"),
                            tariff.getString("OTHER_FEES"),
                        tariff.getString("CALL_NUM"),
                        tariff.getString("DATA_NUM"),
                            tariff.getString("DATA_UNIT"),
                        tariff.getString("SMS_NUM"),
                        tariff.getString("ORIENT_TRAFFIC"),
                            tariff.getString("ORIENT_TRAFFIC_UNIT"),
                        tariff.getString("IPTV"),
                        tariff.getString("BANDWIDTH"),
                        tariff.getString("RIGHTS"),
                        tariff.getString("OTHER_CONTENT"),
                            tariff.getString("APPLICABLE_PEOPLE"),
                            tariff.getString("VALID_PERIOD"),
                            tariff.getString("CHANNEL"),
                            tariff.getString("DURATION"),
                            tariff.getString("UNSUBSCRIBE"),
                            tariff.getString("RESPONSIBILITY"),
                            tariff.getString("OTHERS"),
                        tariff.getString("ONLINE_DAY"),
                        tariff.getString("OFFLINE_DAY")
                    };
                    return RuleCheckUtils.checkContentTooLongEnhanced(contentLengthFields);
                }

            case 12: // 资费方案内容重复或矛盾
                if (checkType == CHECK_TYPE_PUBLIC) {
                    String otherContent = tariff.getString("OTHER_CONTENT");
                    String others = tariff.getString("OTHERS");
                    return RuleCheckUtils.checkContentConflict(tariffId, otherContent, others, ex1Array);
                } else {
                    String otherContent = tariff.getString("OTHER_CONTENT");
                    String others = tariff.getString("OTHERS");
                    return RuleCheckUtils.checkContentConflict(tariffId, otherContent, others, ex1Array);
                }

            case 13: // 流量表述不规范
                if (checkType == CHECK_TYPE_PUBLIC) {
                    // 准备要检查的字段值
                    String[] trafficFields = new String[] {
                            tariff.getString("NAME"),
                            tariff.getString("FEES"),
                            tariff.getString("DATA"),
                            tariff.getString("ORIENT_TRAFFIC"),
                            tariff.getString("OTHER_CONTENT"),
                            tariff.getString("APPLICABLE_PEOPLE"),
                            tariff.getString("APPLICABLE_AREA"),
                            tariff.getString("VALID_PERIOD"),
                            tariff.getString("CHANNEL"),
                            tariff.getString("DURATION"),
                            tariff.getString("UNSUBSCRIBE"),
                            tariff.getString("RESPONSIBILITY"),
                            tariff.getString("OTHERS")
                    };
                    return RuleCheckUtils.checkTrafficIrregular(tariffId, trafficFields, ex1Array);
                } else {
                    String[] trafficFields = new String[] {
                        tariff.getString("NAME"),
                        tariff.getString("FEES"),
                        tariff.getString("DATA_NUM"),
                        tariff.getString("ORIENT_TRAFFIC"),
                        tariff.getString("OTHER_CONTENT"),
                        tariff.getString("APPLICABLE_PEOPLE"),
                        tariff.getString("APPLICABLE_AREA"),
                        tariff.getString("VALID_PERIOD"),
                        tariff.getString("CHANNEL"),
                        tariff.getString("DURATION"),
                        tariff.getString("UNSUBSCRIBE"),
                        tariff.getString("RESPONSIBILITY"),
                        tariff.getString("OTHERS")
                    };
                    return RuleCheckUtils.checkTrafficIrregular(tariffId, trafficFields, ex1Array);
                }

            case 14: // 加装包附加限制性条件
                if (checkType == CHECK_TYPE_PUBLIC) {
                    // 公示库的加装包附加限制性条件检查 - 如果没有二级分类则返回false
                    if (StringUtils.isBlank(type2)) {
            return false;
        }
                    String duration = tariff.getString("DURATION");
                    return RuleCheckUtilsExt.checkAddonWithRestriction(type2, duration, null,ex1Array);
                } else {
                    String duration = tariff.getString("DURATION");
                    return RuleCheckUtilsExt.checkAddonWithRestriction(type2, duration, null,ex1Array);
                }

            case 15: // 营销活动附加限制性条件
                if (checkType == CHECK_TYPE_PUBLIC) {
                    // 公示库的营销活动附加限制性条件检查 - 如果没有二级分类则返回false
                    if (StringUtils.isBlank(type2)) {
                        return false;
                    }
                    String duration = tariff.getString("DURATION");
                    return RuleCheckUtilsExt.checkMarketingWithRestriction(type2, duration, null,ex1Array);
                } else {
                    String duration = tariff.getString("DURATION");
                    return RuleCheckUtilsExt.checkMarketingWithRestriction(type2, duration, null,ex1Array);
                }
                
            case 16: // 套餐附加限制性条件
                if (checkType == CHECK_TYPE_PUBLIC) {
                    // 公示库的套餐附加限制性条件检查 - 如果没有二级分类则返回false
                    if (StringUtils.isBlank(type2)) {
                        return false;
                    }
                    String duration = tariff.getString("DURATION");
                    return RuleCheckUtilsExt.checkPackageWithRestriction(type2, duration, null, ex1Array);
                } else {
                    String duration = tariff.getString("DURATION");
                    return RuleCheckUtilsExt.checkPackageWithRestriction(type2, duration, null, ex1Array);
                }

            default:
                joblogger.warn("未知的规则序号: {}", ruleNo);
        return false;
        }
    }

    /**
     * 更新资费检查结果和检查时间
     * @param tariff 资费记录
     * @param newCheckResult 新的检查结果
     * @param checkTime 检查时间
     * @param newCheckNo 新的规则编号
     * @param targetRuleNo 目标规则编号，如果为null则更新所有规则
     * @param checkType 检查类型，1为报送库，2为公示库
     */
    private void updateTariffCheckResult(JSONObject tariff, String newCheckResult, String checkTime, String newCheckNo, String targetRuleNo, int checkType) {
        // 获取记录ID，根据检查类型处理字段大小写
        String id = checkType == CHECK_TYPE_PUBLIC ? tariff.getString("ID") : tariff.getString("ID");

        try {
            joblogger.debug("开始更新资费检查结果，资费ID: {}, 新检查结果: {}, 目标规则编号: {}, 检查类型: {}",
                    id, newCheckResult, StringUtils.isBlank(targetRuleNo) ? "全部规则" : targetRuleNo,
                    checkType == CHECK_TYPE_PUBLIC ? "公示库" : "报送库");

            // 如果指定了目标规则编号，则需要合并检查结果
            if (StringUtils.isNotBlank(targetRuleNo)) {
                // 获取原有的检查结果和规则编号
                String originalCheckResult;
                String originalCheckNo;

                if (checkType == CHECK_TYPE_PUBLIC) {
                    originalCheckResult = tariff.getString("FIELD_CHECK_RESULT");
                    originalCheckNo = tariff.getString("FIELD_CHECK_NO");
                } else {
                    originalCheckResult = tariff.getString("FIELD_CHECK_RESULT");
                    originalCheckNo = tariff.getString("FIELD_CHECK_NO");
                }

                // 如果原有检查结果为空，直接使用新的检查结果
                if (StringUtils.isBlank(originalCheckResult) || StringUtils.isBlank(originalCheckNo)) {
                    joblogger.debug("原有检查结果为空，直接使用新的检查结果，资费ID: {}", id);
                } else {
                    joblogger.debug("合并检查结果，资费ID: {}, 原检查结果: {}, 原规则编号: {}",
                            id, originalCheckResult, originalCheckNo);

                    // 解析原有规则编号列表
                    Set<String> originalRuleNoSet = new HashSet<>();
                    for (String no : originalCheckNo.split(",")) {
                        if (StringUtils.isNotBlank(no)) {
                            originalRuleNoSet.add(no.trim());
                        }
                    }


                    // 如果新的检查结果为空，表示目标规则没有问题，需要从原有结果中移除
                    if (StringUtils.isBlank(newCheckResult)) {
                        // 从原有规则编号列表中移除目标规则编号（支持逗号分隔多个）
                        for (String ruleNo : targetRuleNo.split(",")) {
                            if (StringUtils.isNotBlank(ruleNo)) {
                                originalRuleNoSet.remove(ruleNo.trim());
                            }
                        }

                        // 如果原有规则编号列表为空，则清空检查结果
                        if (originalRuleNoSet.isEmpty()) {
                            newCheckResult = "";
                            newCheckNo = "";
                        } else {
                            // 重新构建检查结果和规则编号
                            List<String> remainingRuleNos = new ArrayList<>(originalRuleNoSet);
                            newCheckNo = String.join(",", remainingRuleNos);

                            // 查询规则描述
                            String sql = "SELECT RULE_NO, RULE_DESC FROM " + Constants.getBusiSchema() +
                                    ".xty_tariff_check_rule WHERE RULE_NO IN (";
                            for (int i = 0; i < remainingRuleNos.size(); i++) {
                                if (i > 0) sql += ",";
                                sql += "?";
                            }
                            sql += ") ORDER BY ID ASC";

                            EasyQuery query = QueryFactory.getWriteQuery();
                            List<JSONObject> ruleList = query.queryForList(sql, remainingRuleNos.toArray(), new JSONMapperImpl());

                            // 构建新的检查结果
                            List<String> checkResults = new ArrayList<>();
                            for (JSONObject rule : ruleList) {
                                String ruleDesc = rule.getString("RULE_DESC");
                                if (StringUtils.isNotBlank(ruleDesc)) {
                                    checkResults.add("【" + ruleDesc + "】");
                                }
                            }
                            newCheckResult = String.join("，", checkResults);
                        }
                    }

                }
            }

            // 根据检查类型构建更新SQL
            String sql;
            EasyQuery query = null;
            if (checkType == CHECK_TYPE_PUBLIC) {
                sql = "UPDATE " + Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib SET field_check_result = ?, field_check_time = ?, field_check_no = ? WHERE id = ?";
                query = QueryFactory.getTariffQuery();
            } else {
                sql = "UPDATE " + Constants.getBusiSchema() + ".xty_tariff_record SET FIELD_CHECK_RESULT = ?, FIELD_CHECK_TIME = ?, FIELD_CHECK_NO = ? WHERE ID = ?";
                query = QueryFactory.getWriteQuery();
            }
            
            // 执行更新
            query.execute(sql, new Object[]{newCheckResult, checkTime, newCheckNo, id});

            joblogger.info("更新资费检查结果成功，资费ID: {}, 检查结果: {}, 规则编号: {}, 检查类型: {}",
                id, newCheckResult, newCheckNo, checkType == CHECK_TYPE_PUBLIC ? "公示库" : "报送库");

            // 同时更新ES索引
            updateEsCheckResult(id, newCheckResult, checkTime, newCheckNo, checkType);
        } catch (Exception e) {
            joblogger.error("更新资费检查结果失败: {}, 资费ID: {}", e.getMessage(), id, e);
        }
    }

    /**
     * 获取两个日期之间的所有日期
     * @param startDate 开始日期，格式yyyyMMdd
     * @param endDate 结束日期，格式yyyyMMdd
     * @return 日期列表
     */
    private List<String> getDatesBetween(String startDate, String endDate) {
        List<String> result = new ArrayList<>();

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(start);

            while (!calendar.getTime().after(end)) {
                result.add(sdf.format(calendar.getTime()));
                calendar.add(Calendar.DATE, 1);
            }
        } catch (Exception e) {
            joblogger.error("获取日期范围时发生错误: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 从规则配置中获取配置项
     * @param rule 规则对象
     * @param key 配置键
     * @return 配置值数组
     */
    private String[] getConfigArray(JSONObject rule, String key) {
        try {
            String configJson = rule.getString("CONFIG_JSON");
            joblogger.debug("获取规则配置，规则ID: {}, 规则描述: {}, 配置键: {}, 配置JSON: {}",
                    rule.getString("ID"), rule.getString("RULE_DESC"), key, configJson);

            if (StringUtils.isBlank(configJson)) {
                joblogger.debug("规则配置为空，规则ID: {}, 配置键: {}", rule.getString("ID"), key);
                return new String[0];
            }

            JSONObject config = JSONObject.parseObject(configJson);
            String value = config.getString(key);

            if (StringUtils.isBlank(value)) {
                joblogger.debug("规则配置值为空，规则ID: {}, 配置键: {}", rule.getString("ID"), key);
                return new String[0];
            }

            String[] result = value.split(",");
            joblogger.debug("规则配置解析结果，规则ID: {}, 配置键: {}, 配置值: {}, 解析为{}个元素",
                    rule.getString("ID"), key, value, result.length);
            return result;
        } catch (Exception e) {
            joblogger.error("解析规则配置项失败: {}, 规则ID: {}, 配置键: {}", e.getMessage(), rule.getString("ID"), key, e);
            return new String[0];
        }
    }
    
    /**
     * 根据ID检查资费记录
     * @param tariffId 资费ID
     * @param ruleNo 规则编号，如果为null则检查所有规则
     * @param checkType 检查类型，1为报送库，2为公示库
     */
    private void checkTariffById(String tariffId, String ruleNo, int checkType) {
        try {
            joblogger.info("开始检查资费ID: {}, 规则编号: {}, 检查类型: {}", 
                tariffId, StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                checkType == CHECK_TYPE_PUBLIC ? "公示库" : "报送库");

            List<JSONObject> tariffList;
            List<String> idList = new ArrayList<>();
            idList.add(tariffId);
            
            // 根据检查类型选择不同的查询方式
            if (checkType == CHECK_TYPE_PUBLIC) {
                // 从ES查询公示库记录
                tariffList = queryPublicLibRecordsFromES(idList);
            } else {
                // 从ES查询报送库记录
                tariffList = queryReportRecordsFromES(idList);
            }
            
            if (tariffList == null || tariffList.isEmpty()) {
                joblogger.info("没有找到资费ID: {} 的记录，检查类型: {}", tariffId, 
                    checkType == CHECK_TYPE_PUBLIC ? "公示库" : "报送库");
                return;
            }

            // 检查每条记录
            for (JSONObject tariff : tariffList) {
                checkSingleTariff(tariff, ruleNo, checkType);
            }

            joblogger.info("资费ID: {} 的检查完成，检查类型: {}", tariffId, 
                checkType == CHECK_TYPE_PUBLIC ? "公示库" : "报送库");
        } catch (Exception e) {
            joblogger.error("检查资费ID: {} 时发生错误: {}", tariffId, e.getMessage(), e);
        }
    }

    /**
     * 清除规则缓存
     */
    public static void clearRuleCache() {
        // 清除所有规则缓存
        RedissonUtil.del(ALL_RULES_CACHE_KEY);
        joblogger.info("清除规则缓存");
    }

    /**
     * 根据省份和企业编码检查资费字段
     * @param startDate 开始日期，格式yyyyMMdd，为空则默认为昨天
     * @param endDate 结束日期，格式yyyyMMdd，为空则默认为昨天
     * @param isAll 是否全量检查，true表示检查所有记录，false表示只检查FIELD_CHECK_TIME为空的记录
     * @param tariffId 资费ID，如果不为空，则只检查指定ID的资费记录
     * @param ruleNo 规则编号，如果不为空，则只检查指定规则编号
     * @param provinceCodes 省份编码列表，如果不为空，则只检查指定省份的资费记录
     * @param entTypes 企业编码列表，如果不为空，则只检查指定企业的资费记录
     * @param hasJTCode 是否包含JT特殊省份代码
     */
    public void checkTariffFieldsByProvinceAndEnt(String startDate, String endDate, Boolean isAll, String tariffId, 
                                                  String ruleNo, List<String> provinceCodes, List<String> entTypes,
                                                  boolean hasJTCode) {
        try {
            // 检查是否有检查任务正在运行
            boolean isTaskRunning = isCheckRunning();

            if (isTaskRunning) {
                joblogger.warn("已有检查任务正在运行，无法启动新的检查任务，规则编号: {}，省份: {}，企业: {}",
                        StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo, 
                        provinceCodes == null ? "全部" : provinceCodes, 
                        entTypes == null ? "全部" : entTypes);
                return;
            }

            // 记录运行信息
            JSONObject runningInfo = new JSONObject();
            runningInfo.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            runningInfo.put("startDate", startDate);
            runningInfo.put("endDate", endDate);
            runningInfo.put("isAll", isAll);
            runningInfo.put("tariffId", tariffId);
            runningInfo.put("ruleNo", ruleNo);
            runningInfo.put("provinceCodes", provinceCodes);
            runningInfo.put("entTypes", entTypes);
            runningInfo.put("hasJTCode", hasJTCode);
            runningInfo.put("checkType", CHECK_TYPE_REPORT);

            // 设置检查任务运行状态
            setCheckRunning(runningInfo, CHECK_TYPE_REPORT);

            try {
                // 加载规则配置
                loadRuleConfig(ruleNo);

                // 处理日期参数
                String startDateStr = startDate;
                String endDateStr = endDate;

                // 如果开始日期或结束日期为空，则默认为昨天
                if (StringUtils.isBlank(startDateStr) || StringUtils.isBlank(endDateStr)) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                    Date yesterday = calendar.getTime();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    String yesterdayStr = sdf.format(yesterday);

                    if (StringUtils.isBlank(startDateStr)) {
                        startDateStr = yesterdayStr;
                    }
                    if (StringUtils.isBlank(endDateStr)) {
                        endDateStr = yesterdayStr;
                    }
                }

                // 如果指定了资费ID，则直接检查该ID的记录
                if (StringUtils.isNotBlank(tariffId)) {
                    checkTariffById(tariffId, ruleNo, CHECK_TYPE_REPORT);
                    return;
                }

                // 构建查询SQL - 只查询ID
                EasySQL idSqlBuilder = new EasySQL();
                idSqlBuilder.append("SELECT ID FROM " + Constants.getBusiSchema() + ".xty_tariff_record WHERE 1=1");

                // 添加日期范围条件
                if(StringUtils.isBlank(ruleNo)){
                    idSqlBuilder.append(startDateStr, " AND DATE_ID >= ?");
                    idSqlBuilder.append(endDateStr, " AND DATE_ID <= ?");
                }

                // 如果不是全量检查，则只检查FIELD_CHECK_TIME为空的记录
                if (isAll == null || !isAll) {
                    idSqlBuilder.append(" AND (FIELD_CHECK_TIME IS NULL OR FIELD_CHECK_TIME = '')");
                }

                idSqlBuilder.append(" and STATUS in ('1', '4')");

                //todo 测试啊
                //idSqlBuilder.append(" and REPORT_NO = '25CQ101330' ");

                // 处理省份条件
                if (hasJTCode) {
                    // JT特殊代码处理：省份为空或省份不为空字符串
                    joblogger.info("添加JT特殊省份条件：PROVINCE IS NULL OR PROVINCE = ''");
                    idSqlBuilder.append(" AND (PROVINCE IS NULL OR PROVINCE = '')");

                    // 如果还有其他省份代码，使用OR连接
                if (provinceCodes != null && !provinceCodes.isEmpty()) {
                        idSqlBuilder.append(" OR PROVINCE IN (");
                        for (int i = 0; i < provinceCodes.size(); i++) {
                            if (i > 0) {
                                idSqlBuilder.append(",");
                            }
                            idSqlBuilder.append(provinceCodes.get(i));
                        }
                        idSqlBuilder.append(")");
                    }
                } else if (provinceCodes != null && !provinceCodes.isEmpty()) {
                    // 正常省份处理
                    idSqlBuilder.append(" AND PROVINCE IN (");
                    for (int i = 0; i < provinceCodes.size(); i++) {
                        if (i > 0) {
                            idSqlBuilder.append(",");
                        }
                        idSqlBuilder.append(provinceCodes.get(i));
                    }
                    idSqlBuilder.append(")");
                }

                // 添加企业条件
                if (entTypes != null && !entTypes.isEmpty()) {
                    idSqlBuilder.append(" AND ENT IN (");
                    for (int i = 0; i < entTypes.size(); i++) {
                        if (i > 0) {
                            idSqlBuilder.append(",");
                        }
                        idSqlBuilder.append(entTypes.get(i));
                    }
                    idSqlBuilder.append(")");
                }

                joblogger.info("开始检查资费字段，查询条件：开始日期={}, 结束日期={}, 是否全量={}, 规则编号={}, 省份={}, JT特殊省份={}, 企业={}",
                        startDateStr, endDateStr, isAll, StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                        provinceCodes == null ? "全部" : provinceCodes,
                        hasJTCode ? "是" : "否",
                        entTypes == null ? "全部" : entTypes);

                // 记录完整SQL语句，便于调试
                joblogger.info("检查资费字段SQL: {}", idSqlBuilder.toFullSql());

                // 获取可用处理器数量，用于优化线程池大小
                int availableProcessors = Runtime.getRuntime().availableProcessors();
                int optimalThreadCount = Math.max(2, Math.min(availableProcessors * 2, 20)); // 最少2个线程，最多20个线程

                joblogger.info("系统可用处理器数: {}, 优化后的线程数: {}", availableProcessors, optimalThreadCount);

                // 创建线程池
                ExecutorService executorService = Executors.newFixedThreadPool(optimalThreadCount);

                // 使用流式处理，边查询边处理
                processRecordsInStreams(idSqlBuilder, QueryFactory.getReadQuery(), executorService, optimalThreadCount, ruleNo, CHECK_TYPE_REPORT);

                // 关闭线程池
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(24, TimeUnit.HOURS)) {
                        joblogger.warn("线程池未在指定时间内完成，强制关闭");
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    joblogger.error("关闭线程池时被中断: {}", e.getMessage(), e);
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }

                joblogger.info("资费字段检查完成，规则编号: {}, 省份: {}, JT特殊省份: {}, 企业: {}",
                        StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                        provinceCodes == null ? "全部" : provinceCodes,
                        hasJTCode ? "是" : "否",
                        entTypes == null ? "全部" : entTypes);
            } finally {
                // 清除检查任务运行状态
                clearCheckRunning(CHECK_TYPE_REPORT);
            }
        } catch (Exception e) {
            // 发生异常时，确保清除运行状态
            try {
                clearCheckRunning(CHECK_TYPE_REPORT);
            } catch (Exception ex) {
                joblogger.error("清除检查任务运行状态时发生错误", ex);
            }
            joblogger.error("检查资费字段时发生错误", e);
        }
    }

    /**
     * 兼容旧接口的重载方法
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param isAll 是否全量检查
     * @param tariffId 资费ID
     * @param ruleNo 规则编号
     * @param provinceCodes 省份编码列表
     * @param entTypes 企业编码列表
     */
    public void checkTariffFieldsByProvinceAndEnt(String startDate, String endDate, Boolean isAll, String tariffId,
                                                  String ruleNo, List<String> provinceCodes, List<String> entTypes) {
        // 默认不包含JT特殊省份代码
        checkTariffFieldsByProvinceAndEnt(startDate, endDate, isAll, tariffId, ruleNo, provinceCodes, entTypes, false);
    }
    
    /**
     * 公示库资费名称重复检查
     * 通过省份province_code，企业ent_code，资费名称name判断是否重复
     * 
     * @param tariffId 资费ID
     * @param name 资费名称
     * @param provinceCode 省份编码
     * @param entCode 企业编码
     * @return 是否存在重复
     */
    private boolean checkNameDuplicateInPublicLib(String tariffId, String name, String provinceCode, String entCode) {
        joblogger.debug("开始检查公示库资费名称重复，资费ID: {}, 资费名称: {}, 省份编码: {}, 企业编码: {}", 
            tariffId, name, provinceCode, entCode);

        if (StringUtils.isBlank(name) || StringUtils.isBlank(provinceCode) || StringUtils.isBlank(entCode)) {
            joblogger.debug("资费名称、省份编码或企业编码为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }
        
        try {
            // 构建ES查询参数
            JSONObject queryParams = new JSONObject();
            queryParams.put("size", 0);  // 只需要计数，不需要返回具体文档
            queryParams.put("track_total_hits", true);

            // 构建查询条件
            JSONObject boolQuery = new JSONObject();
            JSONArray mustArray = new JSONArray();

            // 添加name条件
            addMatchPhraseQuery(mustArray, "name", name);

            // 添加province_code条件
            addTermQuery(mustArray, "province_code.keyword", provinceCode);

            // 添加ent_code条件
            addTermQuery(mustArray, "ent_code.keyword", entCode);

            // 添加id不等于条件
            JSONArray mustNotArray = new JSONArray();
            JSONObject idTerm = new JSONObject();
            idTerm.put("term", new JSONObject().fluentPut("id.keyword", tariffId));
            mustNotArray.add(idTerm);
            boolQuery.put("must_not", mustNotArray);

            // 设置查询条件
            boolQuery.put("must", mustArray);
            queryParams.put("query", new JSONObject().fluentPut("bool", boolQuery));

            // 执行ES查询
            joblogger.debug("公示库资费名称重复检查查询参数: {}", queryParams.toJSONString());
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, queryParams);

            // 获取匹配的文档数量
            int count = 0;
            if (esResult != null && esResult.containsKey("hits")) {
                JSONObject hits = esResult.getJSONObject("hits");
                if (hits.containsKey("total")) {
                    // ES 7.x 版本返回的是一个对象
                    if (hits.get("total") instanceof JSONObject) {
                        count = hits.getJSONObject("total").getIntValue("value");
                    }
                    // ES 6.x 及更早版本返回的是一个数字
                    else {
                        count = hits.getIntValue("total");
                    }
                }
            }

            joblogger.debug("公示库资费名称重复检查结果，资费ID: {}, 重复数量: {}", tariffId, count);

            // 如果存在相同name、province_code和ent_code的其他记录，则返回true表示存在重复
            return count > 0;
        } catch (Exception e) {
            joblogger.error("检查公示库资费名称重复时发生错误: {}, 资费ID: {}", e.getMessage(), tariffId, e);
            // 发生异常时，保守返回false
            return false;
        }
    }


    /**
     * 报送库资费名称重复检查
     * 通过省份province_code，企业ent_code，资费名称name判断是否重复
     *
     * @param tariffId 资费ID
     * @param name 资费名称
     * @return 是否存在重复
     */
    private boolean checkNameDuplicate(String tariffId, String name, String reporter) {
        joblogger.debug("开始检查报送库资费名称重复，资费ID: {}, 资费名称: {}, 报送主体: {}",
                tariffId, name, reporter);

        if (StringUtils.isBlank(name) || StringUtils.isBlank(reporter) ) {
            joblogger.debug("资费名称、报送主体为空，跳过检查，资费ID: {}", tariffId);
            return false;
        }

        try {
            // 构建ES查询参数
            JSONObject queryParams = new JSONObject();
            queryParams.put("size", 0);  // 只需要计数，不需要返回具体文档
            queryParams.put("track_total_hits", true);

            // 构建查询条件
            JSONObject boolQuery = new JSONObject();
            JSONArray mustArray = new JSONArray();

            // 添加name条件
            addMatchPhraseQuery(mustArray, "NAME", name);

            // 添加province_code条件
            addTermQuery(mustArray, "REPORTER.keyword", reporter);

            // 添加id不等于条件
            JSONArray mustNotArray = new JSONArray();
            JSONObject idTerm = new JSONObject();
            idTerm.put("term", new JSONObject().fluentPut("ID", tariffId));
            mustNotArray.add(idTerm);
            boolQuery.put("must_not", mustNotArray);

            // 设置查询条件
            boolQuery.put("must", mustArray);
            queryParams.put("query", new JSONObject().fluentPut("bool", boolQuery));

            // 执行ES查询
            joblogger.debug("报送库资费名称重复检查查询参数: {}", queryParams.toJSONString());
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_BAK_INFO_INDEX, queryParams);

            // 获取匹配的文档数量
            int count = 0;
            if (esResult != null && esResult.containsKey("hits")) {
                JSONObject hits = esResult.getJSONObject("hits");
                if (hits.containsKey("total")) {
                    // ES 7.x 版本返回的是一个对象
                    if (hits.get("total") instanceof JSONObject) {
                        count = hits.getJSONObject("total").getIntValue("value");
                    }
                    // ES 6.x 及更早版本返回的是一个数字
                    else {
                        count = hits.getIntValue("total");
                    }
                }
            }

            joblogger.debug("报送库资费名称重复检查结果，资费ID: {}, 重复数量: {}", tariffId, count);

            // 如果存在相同name、province_code和ent_code的其他记录，则返回true表示存在重复
            return count > 0;
        } catch (Exception e) {
            joblogger.error("检查报送库资费名称重复时发生错误: {}, 资费ID: {}", e.getMessage(), tariffId, e);
            // 发生异常时，保守返回false
            return false;
        }
    }

    /**
     * 添加精确匹配查询条件
     * @param mustArray 必须匹配的条件数组
     * @param field 字段名
     * @param value 字段值
     */
    private void addTermQuery(JSONArray mustArray, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            JSONObject termQuery = new JSONObject();
            JSONObject termFilter = new JSONObject();
            termFilter.put(field, value);
            termQuery.put("term", termFilter);
            mustArray.add(termQuery);
        }
    }

    /**
     * 添加短语匹配查询条件
     * @param mustArray 必须匹配的条件数组
     * @param field 字段名
     * @param value 字段值
     */
    private void addMatchPhraseQuery(JSONArray mustArray, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            JSONObject matchQuery = new JSONObject();
            JSONObject matchFilter = new JSONObject();
            matchFilter.put(field, value);
            matchQuery.put("match_phrase", matchFilter);
            mustArray.add(matchQuery);
        }
    }

    /**
     * 添加范围查询条件
     * @param mustArray 必须匹配的条件数组
     * @param field 字段名
     * @param gte 大于等于值
     * @param lte 小于等于值
     */
    private void addRangeQuery(JSONArray mustArray, String field, String gte, String lte) {
        if (StringUtils.isNotBlank(gte) || StringUtils.isNotBlank(lte)) {
            JSONObject rangeQuery = new JSONObject();
            JSONObject rangeFilter = new JSONObject();
            JSONObject rangeCondition = new JSONObject();

            if (StringUtils.isNotBlank(gte)) {
                rangeCondition.put("gte", gte);
            }
            if (StringUtils.isNotBlank(lte)) {
                rangeCondition.put("lte", lte);
            }

            rangeFilter.put(field, rangeCondition);
            rangeQuery.put("range", rangeFilter);
            mustArray.add(rangeQuery);
        }
    }

    /**
     * 检查公示库资费字段
     * @param startDate 开始日期，格式yyyyMMdd，为空则默认为昨天
     * @param endDate 结束日期，格式yyyyMMdd，为空则默认为昨天
     * @param isAll 是否全量检查，true表示检查所有记录，false表示只检查FIELD_CHECK_TIME为空的记录
     * @param tariffId 资费ID，如果不为空，则只检查指定ID的资费记录
     * @param ruleNo 规则编号，如果不为空，则只检查指定规则编号
     */
    public void checkPublicLibTariffFields(String startDate, String endDate, Boolean isAll, String tariffId, String ruleNo) {
        checkTariffFields(startDate, endDate, isAll, tariffId, ruleNo, CHECK_TYPE_PUBLIC);
    }

    /**
     * 连续执行报送库和公示库资费字段检查
     * @param startDate 开始日期，格式yyyyMMdd，为空则默认为昨天
     * @param endDate 结束日期，格式yyyyMMdd，为空则默认为昨天
     * @param isAll 是否全量检查，true表示检查所有记录，false表示只检查FIELD_CHECK_TIME为空的记录
     * @param tariffId 资费ID，如果不为空，则只检查指定ID的资费记录
     * @param ruleNo 规则编号，如果不为空，则只检查指定规则编号
     */
    public void checkAllTariffFields(String startDate, String endDate, Boolean isAll, String tariffId, String ruleNo) {
        // 先执行报送库核查
        joblogger.info("开始连续检查 - 报送库核查");
        checkTariffFields(startDate, endDate, isAll, tariffId, ruleNo, CHECK_TYPE_REPORT);

        // 再执行公示库核查
        joblogger.info("连续检查 - 公示库核查");
        checkTariffFields(startDate, endDate, isAll, tariffId, ruleNo, CHECK_TYPE_PUBLIC);

        joblogger.info("连续检查完成");
    }

    /**
     * 根据省份和企业编码检查公示库资费字段
     * @param startDate 开始日期，格式yyyyMMdd，为空则默认为昨天
     * @param endDate 结束日期，格式yyyyMMdd，为空则默认为昨天
     * @param isAll 是否全量检查，true表示检查所有记录，false表示只检查FIELD_CHECK_TIME为空的记录
     * @param tariffId 资费ID，如果不为空，则只检查指定ID的资费记录
     * @param ruleNo 规则编号，如果不为空，则只检查指定规则编号
     * @param provinceCodes 省份编码列表，如果不为空，则只检查指定省份的资费记录
     * @param entTypes 企业编码列表，如果不为空，则只检查指定企业的资费记录
     * @param hasJTCode 是否包含JT特殊省份代码
     */
    public void checkPublicLibTariffFieldsByProvinceAndEnt(String startDate, String endDate, Boolean isAll, String tariffId,
                                                           String ruleNo, List<String> provinceCodes, List<String> entTypes,
                                                           boolean hasJTCode, String versionNo) {
        try {
            // 检查是否有检查任务正在运行
            boolean isTaskRunning = isPublicCheckRunning();

            if (isTaskRunning) {
                joblogger.warn("已有检查任务正在运行，无法启动新的公示库检查任务，规则编号: {}，省份: {}，企业: {}",
                        StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                        provinceCodes == null ? "全部" : provinceCodes,
                        entTypes == null ? "全部" : entTypes);
                return;
            }

            // 记录运行信息
            JSONObject runningInfo = new JSONObject();
            runningInfo.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            runningInfo.put("startDate", startDate);
            runningInfo.put("endDate", endDate);
            runningInfo.put("isAll", isAll);
            runningInfo.put("tariffId", tariffId);
            runningInfo.put("ruleNo", ruleNo);
            runningInfo.put("provinceCodes", provinceCodes);
            runningInfo.put("entTypes", entTypes);
            runningInfo.put("hasJTCode", hasJTCode);
            runningInfo.put("checkType", CHECK_TYPE_PUBLIC);

            // 设置检查任务运行状态
            setCheckRunning(runningInfo, CHECK_TYPE_PUBLIC);

            try {
                // 加载规则配置
                loadRuleConfig(ruleNo);

                // 处理日期参数
                String startDateStr = startDate;
                String endDateStr = endDate;

                // 如果开始日期或结束日期为空，则默认为昨天
                if (StringUtils.isBlank(startDateStr) || StringUtils.isBlank(endDateStr)) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                    Date yesterday = calendar.getTime();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    String yesterdayStr = sdf.format(yesterday);

                    if (StringUtils.isBlank(startDateStr)) {
                        startDateStr = yesterdayStr;
                    }
                    if (StringUtils.isBlank(endDateStr)) {
                        endDateStr = yesterdayStr;
                    }
                }

                // 如果指定了资费ID，则直接检查该ID的记录
                if (StringUtils.isNotBlank(tariffId)) {
                    checkTariffById(tariffId, ruleNo, CHECK_TYPE_PUBLIC);
                    return;
                }

                List<String> publicLibIds = null;
                // 如果指定了版本号，则通过爬虫表查询对应版本的公示库记录ID

                // 构建查询SQL - 只查询ID
                EasySQL idSqlBuilder = new EasySQL();
                if (StringUtils.isNotBlank(versionNo)) {
                    joblogger.info("通过版本号查询公示库ID，版本号: {}", ruleNo);
                    try {

                        // 构建SQL查询
                        idSqlBuilder.append(versionNo, "SELECT DISTINCT PUBLIC_LIB_ID as ID FROM "+Constants.getBusiSchema()+".xty_tariff_crawl_record WHERE VERSION_NO = ?");


                        // 添加省份编码条件
                        if (provinceCodes != null && !provinceCodes.isEmpty()) {
                            idSqlBuilder.append(" AND PROVINCE_CODE IN (");
                            for (int i = 0; i < provinceCodes.size(); i++) {
                                if (i > 0) {
                                    idSqlBuilder.append(",");
                                }
                                idSqlBuilder.append(provinceCodes.get(i));
                            }
                            idSqlBuilder.append(")");
                        }

                        // 添加企业编码条件
                        if (entTypes != null && !entTypes.isEmpty()) {
                            idSqlBuilder.append(" AND ENT_CODE IN (");
                            for (int i = 0; i < entTypes.size(); i++) {
                                if (i > 0) {
                                    idSqlBuilder.append(",");
                                }
                                idSqlBuilder.append(entTypes.get(i));
                            }
                            idSqlBuilder.append(")");
                        }

                        // 排除为空的公示库ID
                        idSqlBuilder.append(" AND PUBLIC_LIB_ID IS NOT NULL AND PUBLIC_LIB_ID <> ''");

                        joblogger.debug("执行版本号查询SQL: {}", idSqlBuilder.toString());

                    } catch (Exception e) {
                        joblogger.error("通过版本号查询公示库ID时发生错误: {}", e.getMessage(), e);
                        // 清除运行状态并返回
                        clearCheckRunning(CHECK_TYPE_PUBLIC);
                        return;
                    }
                }else{
                    idSqlBuilder.append("SELECT id as ID FROM " + Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib WHERE 1=1");

                        // 添加日期范围条件
                        if(!StringUtils.isBlank(ruleNo)){
                            idSqlBuilder.append(startDateStr, " AND date_id >= ?");
                            idSqlBuilder.append(endDateStr, " AND date_id <= ?");
                        }

                        // 如果不是全量检查，则只检查FIELD_CHECK_TIME为空的记录
                        if (isAll == null || !isAll) {
                            idSqlBuilder.append(" AND (field_check_time IS NULL OR field_check_time = '')");
                        }

                        // 处理省份条件 - 公示库使用province_code字段
                        if (hasJTCode) {
                            // JT特殊代码处理：省份为空或省份不为空字符串
                            joblogger.info("添加JT特殊省份条件：province_code IS NULL OR province_code = ''");
                            idSqlBuilder.append(" AND (province_code IS NULL OR province_code = '')");

                            // 如果还有其他省份代码，使用OR连接
                            if (provinceCodes != null && !provinceCodes.isEmpty()) {
                                idSqlBuilder.append(" OR province_code IN (");
                                for (int i = 0; i < provinceCodes.size(); i++) {
                                    if (i > 0) {
                                        idSqlBuilder.append(",");
                                    }
                                    idSqlBuilder.append(provinceCodes.get(i));
                                }
                                idSqlBuilder.append(")");
                            }
                        } else if (provinceCodes != null && !provinceCodes.isEmpty()) {
                            // 正常省份处理
                            idSqlBuilder.append(" AND province_code IN (");
                            for (int i = 0; i < provinceCodes.size(); i++) {
                                if (i > 0) {
                                    idSqlBuilder.append(",");
                                }
                                idSqlBuilder.append(provinceCodes.get(i));
                            }
                            idSqlBuilder.append(")");
                        }

                        // 添加企业条件 - 公示库使用ent_code字段
                        if (entTypes != null && !entTypes.isEmpty()) {
                            idSqlBuilder.append(" AND ent_code IN (");
                            for (int i = 0; i < entTypes.size(); i++) {
                                if (i > 0) {
                                    idSqlBuilder.append(",");
                                }
                                idSqlBuilder.append(entTypes.get(i));
                            }
                            idSqlBuilder.append(")");
                        }

                }

                joblogger.info("开始检查公示库资费字段，查询条件：开始日期={}, 结束日期={}, 是否全量={}, 规则编号={}, 省份={}, JT特殊省份={}, 企业={}",
                        startDateStr, endDateStr, isAll, StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                        provinceCodes == null ? "全部" : provinceCodes,
                        hasJTCode ? "是" : "否",
                        entTypes == null ? "全部" : entTypes);

                // 记录完整SQL语句，便于调试
                joblogger.info("检查公示库资费字段SQL: {}", idSqlBuilder.toFullSql());

                // 获取可用处理器数量，用于优化线程池大小
                int availableProcessors = Runtime.getRuntime().availableProcessors();
                int optimalThreadCount = Math.max(5, Math.min(availableProcessors * 2, 20)); // 最少5个线程，最多20个线程

                joblogger.info("系统可用处理器数: {}, 优化后的线程数: {}", availableProcessors, optimalThreadCount);

                // 创建线程池
                ExecutorService executorService = Executors.newFixedThreadPool(optimalThreadCount);

                // 使用流式处理，边查询边处理
                processRecordsInStreams(idSqlBuilder, QueryFactory.getTariffQuery(), executorService, optimalThreadCount, ruleNo, CHECK_TYPE_PUBLIC);

                // 关闭线程池
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(24, TimeUnit.HOURS)) {
                        joblogger.warn("线程池未在指定时间内完成，强制关闭");
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    joblogger.error("关闭线程池时被中断: {}", e.getMessage(), e);
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }

                joblogger.info("公示库资费字段检查完成，规则编号: {}, 省份: {}, JT特殊省份: {}, 企业: {}",
                        StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                        provinceCodes == null ? "全部" : provinceCodes,
                        hasJTCode ? "是" : "否",
                        entTypes == null ? "全部" : entTypes);
            } finally {
                // 清除检查任务运行状态
                clearCheckRunning(CHECK_TYPE_PUBLIC);
            }
        } catch (Exception e) {
            // 发生异常时，确保清除运行状态
            try {
                clearCheckRunning(CHECK_TYPE_PUBLIC);
            } catch (Exception ex) {
                joblogger.error("清除检查任务运行状态时发生错误", ex);
            }
            joblogger.error("检查公示库资费字段时发生错误", e);
        }
    }


    /**
     * 旧方法，保持向后兼容
     */
    public void checkPublicLibTariffFieldsByProvinceAndEnt(String startDate, String endDate, Boolean isAll, String tariffId,
                                                           String ruleNo, List<String> provinceCodes, List<String> entTypes,
                                                           boolean hasJTCode) {
        // 调用新方法，不指定版本号
        checkPublicLibTariffFieldsByProvinceAndEnt(startDate, endDate, isAll, tariffId, ruleNo, provinceCodes, entTypes, hasJTCode, null);
    }

    /**
     * 从 Elasticsearch 查询公示库记录
     * @param idList 记录ID列表
     * @return 公示库记录列表
     */
    private List<JSONObject> queryPublicLibRecordsFromES(List<String> idList) {
        if (idList == null || idList.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            joblogger.info("从ES查询公示库记录，ID数量: {}", idList.size());

            // 构建ES查询参数
            JSONObject queryParams = new JSONObject();
            queryParams.put("size", idList.size());
            queryParams.put("track_total_hits", true);

            // 构建排序
            JSONArray sortArray = new JSONArray();
            // 按照date_id降序排序
            JSONObject dateIdSort = new JSONObject();
            dateIdSort.put("date_id.keyword", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(dateIdSort);
            // 按照id降序排序
            JSONObject idSort = new JSONObject();
            idSort.put("_id", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(idSort);
            queryParams.put("sort", sortArray);

            // 构建查询条件
            JSONObject boolQuery = new JSONObject();
            JSONArray mustArray = new JSONArray();

            // 添加ID列表条件
            JSONObject termsQuery = new JSONObject();
            JSONObject termsFilter = new JSONObject();
            termsFilter.put("id", idList);
            termsQuery.put("terms", termsFilter);
            mustArray.add(termsQuery);

            // 设置查询条件
            boolQuery.put("must", mustArray);
            queryParams.put("query", new JSONObject().fluentPut("bool", boolQuery));

            // 执行ES查询
            joblogger.debug("公示库查询参数: {}", queryParams.toJSONString());
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_PUBLIC_LIB_INDEX, queryParams);

            // 处理查询结果
            List<JSONObject> resultList = new ArrayList<>();
            if (esResult != null && esResult.containsKey("hits")) {
                JSONObject hits = esResult.getJSONObject("hits");
                if (hits.containsKey("hits")) {
                    JSONArray hitsArray = hits.getJSONArray("hits");
                    for (int i = 0; i < hitsArray.size(); i++) {
                        JSONObject hit = hitsArray.getJSONObject(i);
                        if (hit.containsKey("_source")) {
                            JSONObject source = hit.getJSONObject("_source");

                            // 将ES字段名转换为与数据库字段名一致（小写转大写）
                            JSONObject convertedRecord = new JSONObject();
                            for (Map.Entry<String, Object> entry : source.entrySet()) {
                                String upperCaseKey = entry.getKey().toUpperCase();
                                convertedRecord.put(upperCaseKey, entry.getValue());
                            }

                            resultList.add(convertedRecord);
                        }
                    }
                }
            }

            joblogger.info("从ES查询到{}条公示库记录", resultList.size());
            return resultList;
        } catch (Exception e) {
            joblogger.error("从ES查询公示库记录失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新ES中的检查结果
     * @param id 记录ID
     * @param checkResult 检查结果
     * @param checkTime 检查时间
     * @param checkNo 规则编号
     * @param checkType 检查类型
     * @return 是否更新成功
     */
    private boolean updateEsCheckResult(String id, String checkResult, String checkTime, String checkNo, int checkType) {
        try {
            // 调用ES服务更新索引
            IService service = ServiceContext.getService("XTY_EVT_ES_ORDER_OPERATE");

            // 构建更新数据
            JSONObject updateData = new JSONObject();

            // 转换规则编号字符串为数组
            List<String> checkNosList = new ArrayList<>();
            if (StringUtils.isNotBlank(checkNo)) {
                for (String no : checkNo.split(",")) {
                    if (StringUtils.isNotBlank(no)) {
                        checkNosList.add(no.trim());
                    }
                }
            }

            if (checkType == CHECK_TYPE_PUBLIC) {
                updateData.put("field_check_result", checkResult);
                updateData.put("field_check_no", checkNo);
                updateData.put("field_check_time", checkTime);
                // 添加新字段 field_check_nos，存储规则编号数组
                updateData.put("field_check_nos", checkNosList);

                // 构建ES请求参数
                JSONObject reqParam = new JSONObject();
                reqParam.put("primary", id); // 使用资费ID作为文档ID
                reqParam.put("indexName", Constants.XTY_TARIFF_PUBLIC_LIB_INDEX); // 使用公示库索引
                reqParam.put("command", Constants.ES_OPERATE_UPDATE_DOC); // 更新文档操作
                reqParam.put("data", updateData); // 更新的数据

                // 调用ES服务
                JSONObject result = service.invoke(reqParam);

                if (result != null && "1".equals(result.getString("state"))) {
                    joblogger.debug("成功更新ES中公示库记录的字段检查结果，ID: {}", id);
                    return true;
                } else {
                    String errorMsg = result != null ? result.toJSONString() : "响应为空";
                    joblogger.error("更新ES中公示库记录的字段检查结果失败: {}", errorMsg);
                    return false;
                }
            } else if (checkType == CHECK_TYPE_REPORT) {
                updateData.put("FIELD_CHECK_RESULT", checkResult);
                updateData.put("FIELD_CHECK_NO", checkNo);
                updateData.put("FIELD_CHECK_TIME", checkTime);
                // 添加新字段 FIELD_CHECK_NOS，存储规则编号数组
                updateData.put("FIELD_CHECK_NOS", checkNosList);

                // 构建ES请求参数
                JSONObject reqParam = new JSONObject();
                reqParam.put("primary", id); // 使用资费ID作为文档ID
                reqParam.put("indexName", Constants.XTY_TARIFF_BAK_INFO_INDEX); // 使用备份索引
                reqParam.put("command", Constants.ES_OPERATE_UPDATE_DOC); // 更新文档操作
                reqParam.put("data", updateData); // 更新的数据

                // 调用ES服务
                JSONObject result = service.invoke(reqParam);

                if (result != null && "1".equals(result.getString("state"))) {
                    joblogger.debug("成功更新ES中报送库记录的字段检查结果，ID: {}", id);
                    return true;
                } else {
                    String errorMsg = result != null ? result.toJSONString() : "响应为空";
                    joblogger.error("更新ES中报送库记录的字段检查结果失败: {}", errorMsg);
                    return false;
                }
            }

            return false;
        } catch (Exception e) {
            joblogger.error("更新ES中记录的字段检查结果失败: {}, ID: {}", e.getMessage(), id, e);
            return false;
        }
    }

    /**
     * 从 Elasticsearch 查询报送库记录
     * @param idList 记录ID列表
     * @return 报送库记录列表
     */
    private List<JSONObject> queryReportRecordsFromES(List<String> idList) {
        if (idList == null || idList.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            joblogger.info("从ES查询报送库记录，ID数量: {}", idList.size());

            // 构建ES查询参数
            JSONObject queryParams = new JSONObject();
            queryParams.put("size", idList.size());
            queryParams.put("track_total_hits", true);

            // 构建排序
            JSONArray sortArray = new JSONArray();
            // 按照DATE_ID降序排序
            JSONObject dateIdSort = new JSONObject();
            dateIdSort.put("DATE_ID.keyword", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(dateIdSort);
            // 按照_id降序排序
            JSONObject idSort = new JSONObject();
            idSort.put("_id", new JSONObject().fluentPut("order", "desc"));
            sortArray.add(idSort);
            queryParams.put("sort", sortArray);

            // 构建查询条件
            JSONObject boolQuery = new JSONObject();
            JSONArray mustArray = new JSONArray();

            // 添加ID列表条件
            JSONObject termsQuery = new JSONObject();
            JSONObject termsFilter = new JSONObject();
            termsFilter.put("ID", idList);
            termsQuery.put("terms", termsFilter);
            mustArray.add(termsQuery);

            // 设置查询条件
            boolQuery.put("must", mustArray);
            queryParams.put("query", new JSONObject().fluentPut("bool", boolQuery));

            // 执行ES查询
            joblogger.debug("报送库查询参数: {}", queryParams.toJSONString());
            JSONObject esResult = ElasticsearchKit.search(Constants.XTY_TARIFF_BAK_INFO_INDEX, queryParams);

            // 处理查询结果
            List<JSONObject> resultList = new ArrayList<>();
            if (esResult != null && esResult.containsKey("hits")) {
                JSONObject hits = esResult.getJSONObject("hits");
                if (hits.containsKey("hits")) {
                    JSONArray hitsArray = hits.getJSONArray("hits");
                    for (int i = 0; i < hitsArray.size(); i++) {
                        JSONObject hit = hitsArray.getJSONObject(i);
                        if (hit.containsKey("_source")) {
                            JSONObject source = hit.getJSONObject("_source");
                            resultList.add(source);
                        }
                    }
                }
            }

            joblogger.info("从ES查询到{}条报送库记录", resultList.size());
            return resultList;
        } catch (Exception e) {
            joblogger.error("从ES查询报送库记录失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取当前正在运行的检查任务的缓存信息
     * @return 包含检查任务运行状态的JSON对象，如果没有正在运行的任务，则返回null
     */
    public static JSONObject getRunningCheckInfo() {
        JSONObject runningInfo = new JSONObject();

        // 检查报送库任务
        if (isReportCheckRunning()) {
            String cacheValue = RedissonUtil.get(REPORT_CHECK_RUNNING_KEY);
            if (StringUtils.isNotBlank(cacheValue)) {
                try {
                    JSONObject reportInfo = JSONObject.parseObject(cacheValue);
                    runningInfo.put("reportCheck", reportInfo);
                    runningInfo.put("reportCheckKey", REPORT_CHECK_RUNNING_KEY);
                } catch (Exception e) {
                    runningInfo.put("reportCheck", cacheValue);
                    runningInfo.put("reportCheckKey", REPORT_CHECK_RUNNING_KEY);
                }
            }
        }

        // 检查公示库任务
        if (isPublicCheckRunning()) {
            String cacheValue = RedissonUtil.get(PUBLIC_CHECK_RUNNING_KEY);
            if (StringUtils.isNotBlank(cacheValue)) {
                try {
                    JSONObject publicInfo = JSONObject.parseObject(cacheValue);
                    runningInfo.put("publicCheck", publicInfo);
                    runningInfo.put("publicCheckKey", PUBLIC_CHECK_RUNNING_KEY);
                } catch (Exception e) {
                    runningInfo.put("publicCheck", cacheValue);
                    runningInfo.put("publicCheckKey", PUBLIC_CHECK_RUNNING_KEY);
                }
            }
        }

        // 检查顺序检查任务
        if (isSequentialCheckRunning()) {
            String cacheValue = RedissonUtil.get(SEQUENTIAL_CHECK_KEY);
            if (StringUtils.isNotBlank(cacheValue)) {
                try {
                    JSONObject sequentialInfo = JSONObject.parseObject(cacheValue);
                    runningInfo.put("sequentialCheck", sequentialInfo);
                    runningInfo.put("sequentialCheckKey", SEQUENTIAL_CHECK_KEY);
                } catch (Exception e) {
                    runningInfo.put("sequentialCheck", cacheValue);
                    runningInfo.put("sequentialCheckKey", SEQUENTIAL_CHECK_KEY);
                }
            }
        }

        // 检查旧版本的检查任务
        if (isLegacyCheckRunning()) {
            String cacheValue = RedissonUtil.get(CHECK_RUNNING_KEY);
            if (StringUtils.isNotBlank(cacheValue)) {
                try {
                    JSONObject legacyInfo = JSONObject.parseObject(cacheValue);
                    runningInfo.put("legacyCheck", legacyInfo);
                    runningInfo.put("legacyCheckKey", CHECK_RUNNING_KEY);
                } catch (Exception e) {
                    runningInfo.put("legacyCheck", cacheValue);
                    runningInfo.put("legacyCheckKey", CHECK_RUNNING_KEY);
                }
            }
        }

        // 如果没有任何正在运行的任务，返回null
        if (runningInfo.isEmpty()) {
            return null;
        }

        return runningInfo;
    }

    /**
     * 获取当前正在运行的检查任务的缓存信息（格式化输出）
     * @return 格式化的检查任务运行状态信息
     */
    public static String getRunningCheckInfoString() {
        JSONObject runningInfo = getRunningCheckInfo();
        if (runningInfo == null) {
            return "当前没有正在运行的检查任务";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("当前正在运行的检查任务信息：\n");

        if (runningInfo.containsKey("reportCheck")) {
            sb.append("【报送库检查】缓存键: ").append(runningInfo.getString("reportCheckKey")).append("\n");
            sb.append("详细信息: ").append(runningInfo.get("reportCheck")).append("\n\n");
        }

        if (runningInfo.containsKey("publicCheck")) {
            sb.append("【公示库检查】缓存键: ").append(runningInfo.getString("publicCheckKey")).append("\n");
            sb.append("详细信息: ").append(runningInfo.get("publicCheck")).append("\n\n");
        }

        if (runningInfo.containsKey("sequentialCheck")) {
            sb.append("【顺序检查】缓存键: ").append(runningInfo.getString("sequentialCheckKey")).append("\n");
            sb.append("详细信息: ").append(runningInfo.get("sequentialCheck")).append("\n\n");
        }

        if (runningInfo.containsKey("legacyCheck")) {
            sb.append("【旧版检查】缓存键: ").append(runningInfo.getString("legacyCheckKey")).append("\n");
            sb.append("详细信息: ").append(runningInfo.get("legacyCheck")).append("\n\n");
        }

        return sb.toString();
    }
}
