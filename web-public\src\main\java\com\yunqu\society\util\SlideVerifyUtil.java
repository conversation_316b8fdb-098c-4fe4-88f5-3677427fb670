package com.yunqu.society.util;

import java.awt.*;
import java.awt.geom.Area;
import java.awt.geom.Ellipse2D;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.security.SecureRandom;
import java.util.Base64;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.util.Iterator;

/**
 * 滑块验证码生成工具
 */
public class SlideVerifyUtil {
    
    private static final SecureRandom random = new SecureRandom();
    private static final Color[] BACKGROUND_COLORS = {
        new Color(135, 206, 235), // 天蓝色
        new Color(144, 238, 144), // 浅绿色
        new Color(255, 182, 193), // 浅粉色
        new Color(221, 160, 221), // 梅红色
        new Color(255, 218, 185), // 桃色
        new Color(176, 196, 222)  // 浅钢蓝色
    };
    
    public static class VerifyData {
        private String backgroundImageBase64;
        private String blockImageBase64;
        private int correctX;
        private int correctY;
        private long backgroundSize;
        private long blockSize;
        private String sessionId;
        
        // getters and setters
        public String getBackgroundImageBase64() { return backgroundImageBase64; }
        public void setBackgroundImageBase64(String backgroundImageBase64) { this.backgroundImageBase64 = backgroundImageBase64; }
        public String getBlockImageBase64() { return blockImageBase64; }
        public void setBlockImageBase64(String blockImageBase64) { this.blockImageBase64 = blockImageBase64; }
        public int getCorrectX() { return correctX; }
        public void setCorrectX(int correctX) { this.correctX = correctX; }
        public int getCorrectY() { return correctY; }
        public void setCorrectY(int correctY) { this.correctY = correctY; }
        public long getBackgroundSize() { return backgroundSize; }
        public void setBackgroundSize(long backgroundSize) { this.backgroundSize = backgroundSize; }
        public long getBlockSize() { return blockSize; }
        public void setBlockSize(long blockSize) { this.blockSize = blockSize; }
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public long getTotalSize() { return backgroundSize + blockSize; }
    }
    
    /**
     * 生成验证图片 - 根据设备类型优化
     */
    public static VerifyData generateVerifyImage(String deviceType, String userAgent) {
        try {
            // 根据设备类型选择尺寸
            int[] dimensions = getOptimalDimensions(deviceType, userAgent);
            int width = dimensions[0];
            int height = dimensions[1];
            int blockSize = dimensions[2];
            
            return generateVerifyImage(width, height, blockSize);
            
        } catch (Exception e) {
            throw new RuntimeException("生成验证图片失败", e);
        }
    }
    
    /**
     * 生成验证图片 - 指定尺寸
     */
    public static VerifyData generateVerifyImage(int width, int height, int blockSize) {
        try {
            // 生成背景图
            BufferedImage backgroundImage = generateBackgroundImage(width, height);
            
            // 随机生成滑块位置
            int correctX = random.nextInt(width - blockSize - 50) + 50;
            int correctY = random.nextInt(height - blockSize - 20) + 10;
            
            // 生成滑块形状
            Shape blockShape = generateBlockShape(blockSize);
            
            // 在背景图上挖洞
            BufferedImage backgroundWithHole = createBackgroundWithHole(backgroundImage, blockShape, correctX, correctY);
            
            // 生成滑块图片
            BufferedImage blockImage = createBlockImage(backgroundImage, blockShape, correctX, correctY, blockSize);
            
            // 优化图片格式和大小
            String backgroundBase64 = optimizeBackgroundImage(backgroundWithHole);
            String blockBase64 = optimizeBlockImage(blockImage);
            
            VerifyData verifyData = new VerifyData();
            verifyData.setBackgroundImageBase64(backgroundBase64);
            verifyData.setBlockImageBase64(blockBase64);
            verifyData.setCorrectX(correctX);
            verifyData.setCorrectY(correctY);
            verifyData.setBackgroundSize(calculateBase64Size(backgroundBase64));
            verifyData.setBlockSize(calculateBase64Size(blockBase64));
            verifyData.setSessionId(java.util.UUID.randomUUID().toString());
            
            return verifyData;
            
        } catch (Exception e) {
            throw new RuntimeException("生成验证图片失败", e);
        }
    }
    
    /**
     * 获取最优尺寸
     */
    private static int[] getOptimalDimensions(String deviceType, String userAgent) {
        boolean isMobile = isMobileDevice(deviceType, userAgent);
        
        if (isMobile) {
            return new int[]{240, 120, 32}; // 移动端小尺寸
        } else {
            return new int[]{310, 155, 42}; // PC端标准尺寸
        }
    }
    
    /**
     * 判断是否为移动设备
     */
    private static boolean isMobileDevice(String deviceType, String userAgent) {
        if ("mobile".equals(deviceType)) return true;
        
        if (userAgent != null) {
            String ua = userAgent.toLowerCase();
            return ua.contains("mobile") || ua.contains("android") || 
                   ua.contains("iphone") || ua.contains("ipad");
        }
        
        return false;
    }
    
    /**
     * 生成背景图
     */
    private static BufferedImage generateBackgroundImage(int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 随机选择背景色
        Color bgColor = BACKGROUND_COLORS[random.nextInt(BACKGROUND_COLORS.length)];
        
        // 创建渐变背景
        GradientPaint gradient = new GradientPaint(
            0, 0, bgColor,
            width, height, bgColor.brighter()
        );
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, width, height);
        
        // 添加噪点增加安全性
        addNoise(g2d, width, height);
        
        // 添加网格线
        addGridLines(g2d, width, height);
        
        g2d.dispose();
        return image;
    }
    
    /**
     * 添加噪点
     */
    private static void addNoise(Graphics2D g2d, int width, int height) {
        g2d.setColor(new Color(255, 255, 255, 30));
        for (int i = 0; i < 50; i++) {
            int x = random.nextInt(width);
            int y = random.nextInt(height);
            int size = random.nextInt(3) + 1;
            g2d.fillOval(x, y, size, size);
        }
    }
    
    /**
     * 添加网格线
     */
    private static void addGridLines(Graphics2D g2d, int width, int height) {
        g2d.setColor(new Color(255, 255, 255, 20));
        g2d.setStroke(new BasicStroke(1));
        
        // 垂直线
        for (int x = 20; x < width; x += 20) {
            g2d.drawLine(x, 0, x, height);
        }
        
        // 水平线
        for (int y = 20; y < height; y += 20) {
            g2d.drawLine(0, y, width, y);
        }
    }
    
    /**
     * 生成滑块形状
     */
    private static Shape generateBlockShape(int blockSize) {
        // 创建基础圆角矩形
        RoundRectangle2D baseRect = new RoundRectangle2D.Double(0, 0, blockSize, blockSize, 8, 8);
        Area blockArea = new Area(baseRect);
        
        // 添加凸起和凹陷
        int protrusion = blockSize / 6;
        
        // 右侧凸起
        Ellipse2D rightProtrusion = new Ellipse2D.Double(
            blockSize - protrusion/2, blockSize/2 - protrusion/2, 
            protrusion, protrusion
        );
        blockArea.add(new Area(rightProtrusion));
        
        // 下方凹陷
        Ellipse2D bottomIndentation = new Ellipse2D.Double(
            blockSize/2 - protrusion/2, blockSize - protrusion/2,
            protrusion, protrusion
        );
        blockArea.subtract(new Area(bottomIndentation));
        
        return blockArea;
    }
    
    /**
     * 在背景图上挖洞
     */
    private static BufferedImage createBackgroundWithHole(BufferedImage background, Shape blockShape, int x, int y) {
        BufferedImage result = new BufferedImage(background.getWidth(), background.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = result.createGraphics();
        
        // 绘制原背景
        g2d.drawImage(background, 0, 0, null);
        
        // 设置挖洞区域
        g2d.translate(x, y);
        g2d.setComposite(AlphaComposite.Clear);
        g2d.fill(blockShape);
        
        // 绘制洞的边框
        g2d.setComposite(AlphaComposite.SrcOver);
        g2d.setColor(new Color(0, 0, 0, 100));
        g2d.setStroke(new BasicStroke(2));
        g2d.draw(blockShape);
        
        g2d.dispose();
        return result;
    }
    
    /**
     * 生成滑块图片
     */
    private static BufferedImage createBlockImage(BufferedImage background, Shape blockShape, int x, int y, int blockSize) {
        BufferedImage blockImage = new BufferedImage(blockSize + 10, blockSize + 10, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = blockImage.createGraphics();
        
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 设置裁剪区域
        g2d.translate(5, 5);
        g2d.setClip(blockShape);
        
        // 绘制背景图的对应部分
        g2d.drawImage(background, -x, -y, null);
        
        // 重置裁剪并绘制边框
        g2d.setClip(null);
        g2d.setColor(new Color(255, 255, 255, 200));
        g2d.setStroke(new BasicStroke(2));
        g2d.draw(blockShape);
        
        // 添加阴影效果
        g2d.setColor(new Color(0, 0, 0, 50));
        g2d.translate(2, 2);
        g2d.fill(blockShape);
        
        g2d.dispose();
        return blockImage;
    }
    
    /**
     * 优化背景图 - JPEG压缩
     */
    private static String optimizeBackgroundImage(BufferedImage image) throws Exception {
        // 转换为RGB格式
        BufferedImage rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = rgbImage.createGraphics();
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, rgbImage.getWidth(), rgbImage.getHeight());
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();
        
        // JPEG压缩
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("JPEG");
        ImageWriter writer = writers.next();
        
        ImageOutputStream ios = ImageIO.createImageOutputStream(baos);
        writer.setOutput(ios);
        
        ImageWriteParam param = writer.getDefaultWriteParam();
        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        param.setCompressionQuality(0.8f);
        
        writer.write(null, new javax.imageio.IIOImage(rgbImage, null, null), param);
        writer.dispose();
        ios.close();
        
        byte[] bytes = baos.toByteArray();
        return "data:image/jpeg;base64," + Base64.getEncoder().encodeToString(bytes);
    }
    
    /**
     * 优化滑块图 - PNG压缩
     */
    private static String optimizeBlockImage(BufferedImage image) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        
        byte[] bytes = baos.toByteArray();
        return "data:image/png;base64," + Base64.getEncoder().encodeToString(bytes);
    }
    
    /**
     * 计算Base64大小
     */
    private static long calculateBase64Size(String base64String) {
        String base64Data = base64String.substring(base64String.indexOf(",") + 1);
        return base64Data.length();
    }
    
    /**
     * 验证滑块位置
     */
    public static boolean verifySlidePosition(int userX, int correctX, int tolerance) {
        return Math.abs(userX - correctX) <= tolerance;
    }
    
    /**
     * 验证滑动轨迹（防机器人）
     */
    public static boolean verifySlideTrack(java.util.List<Integer> track, int totalDistance, long totalTime) {
        if (track == null || track.size() < 3) return false;
        
        // 检查时间合理性（0.5-10秒）
        if (totalTime < 500 || totalTime > 10000) return false;
        
        // 检查轨迹连续性
        for (int i = 1; i < track.size(); i++) {
            int diff = Math.abs(track.get(i) - track.get(i-1));
            if (diff > 50) return false; // 单次移动不能超过50px
        }
        
        // 检查是否有回退动作（人类特征）
        boolean hasBacktrack = false;
        for (int i = 2; i < track.size(); i++) {
            if (track.get(i) < track.get(i-1) && track.get(i-1) > track.get(i-2)) {
                hasBacktrack = true;
                break;
            }
        }
        
        return hasBacktrack; // 人类滑动通常会有微调回退
    }
}