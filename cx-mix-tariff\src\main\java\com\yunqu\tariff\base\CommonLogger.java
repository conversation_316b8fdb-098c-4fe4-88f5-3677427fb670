package com.yunqu.tariff.base;

import org.easitline.common.core.log.LogEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CommonLogger {

	public static Logger logger = getLogger();

	public CommonLogger() {

	}

	public static Logger createLogger(String suffix) {
		String loggerName = suffix.isEmpty() ? Constants.APP_NAME
			: Constants.APP_NAME + "-" + suffix;
		return LoggerFactory.getLogger(
			LogEngine.getLogger(Constants.APP_NAME, loggerName).getName()
		);
	}

	public static Logger getLogger() {
		return createLogger("");
	}

	public static Logger getLogger(String suffix) {
		return createLogger(suffix);
	}

	public static Logger getOrderLogger() {
		return createLogger("order");
	}

	public static Logger getRevisitLogger() {
		return createLogger("revisit");
	}

	public static Logger getTaskLogger() {
		return createLogger("task");
	}

	public static Logger getNpsLogger() {
		return createLogger("nps");
	}

	public static Logger getPublicLogger() {
		return createLogger("public");
	}

	public static Logger getJobLogger() {
		return createLogger("job");
	}

	public static Logger getJobBusLogger() {
		return createLogger("job-bus");
	}
	public static Logger getJobErrLogger() {
		return createLogger("job-error");
	}

	public static Logger getCrawLibJobLogger() {
		return createLogger("crawLib");
	}

	public static Logger getcheckJobLogger() {
		return createLogger("check");
	}
}
