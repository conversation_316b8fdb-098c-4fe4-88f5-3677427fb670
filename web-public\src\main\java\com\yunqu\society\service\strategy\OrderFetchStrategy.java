package com.yunqu.society.service.strategy;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.society.base.mapper.HumpMapper;
import com.yunqu.society.base.mapper.ModelMapper;
import com.yunqu.society.base.mapper.NoDecryctMapper;
import com.yunqu.society.model.BoxAppeal;

import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

/**
 * 工单获取策略
 * 获取c_box_appeal表中未同步的数据，分页返回
 */
public class OrderFetchStrategy implements OutInterfaceStrategy {

    @Override
    public JSONObject execute(JSONObject params,HttpServletRequest request,HttpServletResponse response) throws Exception {
        EasyQuery query = getQuery();
        // 获取分页参数
        int pageIndex = params.getIntValue("pageIndex");
        int pageSize = params.getIntValue("pageSize");
        String startTime = params.getString("startTime");
        String endTime = params.getString("endTime");
        // 强制查询
        boolean force = params.getBooleanValue("force");
        // 构建查询SQL
        EasySQL sql = new EasySQL();
        sql.append("SELECT * FROM " + getTableName("c_box_appeal") + " WHERE 1=1");
        
        // 如果有上次同步时间，则只查询之后的数据
        if (StringUtils.notBlank(startTime) && StringUtils.notBlank(endTime)) {
            sql.append(startTime," AND CREATE_TIME >= ? ");
            sql.append(endTime," AND CREATE_TIME <= ? ");
        } else {
            sql.append(DateUtil.addHour(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -6)," and CREATE_TIME >=?");
            sql.append(DateUtil.getCurrentDateStr()," and CREATE_TIME <=?");
        }
        if (!force) {
            // 只查询未同步的数据
            sql.append(" AND (IS_SYSN IS NULL OR IS_SYSN != 'Y')");
        }
        // 按创建时间排序
        sql.append(" ORDER BY CREATE_TIME,CREATE_STAMP ASC");
        sql.append(" LIMIT " + pageIndex * pageSize +", " + pageSize);
        logger.info("查询SQL: {}", sql.getFullSq());
        // 执行查询
        List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new NoDecryctMapper());
        // 返回查询结果
        return createPageResult(true, "查询列表成功", list, CollectionUtils.isEmpty(list) ? -1 : 0, pageIndex, pageSize);
    }
    
    /**
     * 更新同步状态
     * @param dataList 数据列表
     * @param query 查询对象
     * @param servlet Servlet实例
     * @throws Exception 异常
     */
    private void updateSyncStatus(List<JSONObject> dataList) throws Exception {
        // 如果dataList为空，则直接返回
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        // 将dataList中的每个JSONObject转换为一个Object数组，其中只包含JSON对象的"id"字段
        List<Object[]> list = dataList.stream().map(item -> {
            return new Object[]{item.getString("id")};
        }).collect(Collectors.toList());

        // 构建更新SQL语句
        String updateSql = "UPDATE " + getTableName("c_box_appeal") + " SET IS_SYSN = 'Y' WHERE ID = ?";

        // 获取EasyQuery对象
        EasyQuery query = getQuery();

        // 执行批量更新操作
        query.executeBatch(updateSql, list);
    }

    @Override
    public void confirm(JSONObject params, HttpServletRequest request, HttpServletResponse response) throws Exception {
       
    }
}