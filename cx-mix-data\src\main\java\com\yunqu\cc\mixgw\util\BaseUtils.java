package com.yunqu.cc.mixgw.util;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.AttachmentUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.base.QueryFactory;
import com.yunqu.cc.oss.OSSTools;
import com.yunqu.cc.oss.model.AttachmentDbinfo;
import com.yunqu.cc.oss.model.AttachmentFile;
import com.yunqu.cc.oss.model.OSSAttachmentUtil;

public class BaseUtils {
	
	protected Logger logger = CommonLogger.getLogger();
	 
	private static BaseUtils instance = new BaseUtils();
	
	public static BaseUtils getInstance(){
		return instance;
	}
	
	public void processAttachments(String orderId, String appealAttachment, String entId, String busiOrderId,EasyQuery query) {
        try {
            // 通过接口获取附件列表
        	logger.info("开始拉取附件："+orderId);
            JSONObject attachmentResult = getAttachmentListFromInterface(appealAttachment);
            List<JSONObject> fileList =getFileList(appealAttachment, query);
            Map fileMap=null;
            if (CommonUtil.listIsNotNull(fileList)) {
            	 fileMap = fileList.stream().collect(Collectors.toMap(item -> item.getString("NAME"),item -> item));
			}
            if (attachmentResult != null && attachmentResult.getBooleanValue("success")) {
                JSONArray attachments = attachmentResult.getJSONArray("data");
                
                if (attachments != null && !attachments.isEmpty()) {
                    for (int i = 0; i < attachments.size(); i++) {
                        JSONObject attachment = attachments.getJSONObject(i);
                        if (fileMap == null || fileMap.get(attachment.getString("fileName")) == null ) {
                        	try {
                        	    logger.info("文件不存在，文件名称: {}, 附件ID: {}"+attachment.getString("fileName")+"///"+attachment.getString("id"));
                                // 通过getFile接口获取文件并上传到S3
                                downloadAndUploadAttachment(attachment, appealAttachment, entId, busiOrderId,query);
                            } catch (Exception e) {
                                logger.error("上传附件到S3失败，附件ID: {}, 错误: {}"+attachment.getString("id")+"///"+e.getMessage(), e);
                            }
						}                         
                    }
                }else {
                	//临时记录一下附件为空的数据。
                	 logger.info("开始记录附件不存在的工单："+orderId);
                	 EasyRecord record = new EasyRecord(Constants.getSysSchema() +".cx_file_temporary_temp", "ID");
                     record.put("ID", orderId);
                     record.put("BUSI_ID",appealAttachment);
                     query.save(record);
                	
                }
            } else {
                logger.warn("获取附件列表失败或无附件数据，工单ID: {}"+ orderId);
            }
        } catch (Exception e) {
            logger.error("处理附件失败，工单ID: {}, 错误: {}"+orderId+","+e.getMessage(), e);
        }
	 }
	    
	    
	    /**
	     * 下载附件并上传到S3
	     * @param attachment 附件信息
	     * @param busiId 工单ID
	     * @param entId 企业ID
	     * @param busiOrderId 业务工单ID
	     */
	    public void downloadAndUploadAttachment(JSONObject attachment, String busiId, String entId, String busiOrderId,EasyQuery query) {
	        InputStream is = null;
	        try {
	            String fileName = attachment.getString("fileName");
	            String attachmentId = attachment.getString("id");
	            
	            if (StringUtils.isBlank(fileName) || StringUtils.isBlank(attachmentId)) {
	                logger.warn("附件文件名或ID为空，跳过上传，附件ID: {}"+ attachmentId);
	                return;
	            }
	            
	            // 通过getFile接口获取文件流
	            is = getFileStreamFromInterface(attachmentId);
	            if (is == null) {
	                logger.warn("无法获取附件文件流，跳过上传，附件ID: {}"+attachmentId);
	                return;
	            }
	            
	            // 获取文件大小（这里可能需要从attachment信息中获取，或者通过其他方式）
	            long fileSize = attachment.getLongValue("fileSize");
	            if (fileSize <= 0) {
	                // 如果无法获取文件大小，可以尝试读取流来计算，但这里简化处理
	                fileSize = 1024; // 默认值
	            }
	            
	            // 校验附件：检查后缀、文件大小
	            String checkResult = OSSAttachmentUtil.checkSizeAndSuffix(fileName, fileSize);
	            if (StringUtils.isNotBlank(checkResult)) {
	                logger.error("上传附件[{},{}]失败: {}"+fileName+","+fileSize+","+checkResult);
	                return;
	            }
	            
	            AttachmentFile afile = new AttachmentFile(null, is, "appeal", fileName);
	            afile.setFileSize(fileSize);
	            afile.setBusiId(busiId);
	            afile.setDelUploadOldFile(true);
	            
	            // 上传文件到S3
	            OSSTools.getInstance().uploadFile(afile);
	            
	            // 准备入库
	            AttachmentDbinfo attachmentDbinfo = new AttachmentDbinfo(afile,query,Constants.getSysSchema(), entId, busiOrderId);
	            attachmentDbinfo.setUserAcc("system");
	            attachmentDbinfo.setUserName("system");
	            
	            // 数据入库
	            boolean flag = AttachmentUtil.saveAttachment(attachmentDbinfo);
	            if (!flag) {
	                logger.error("上传附件失败，附件入库保存失败: {}"+fileName);
	            } else {
	                logger.info("成功上传附件到S3: {}"+ fileName);
	            }
	            
	        } catch (Exception e) {
	            logger.error("下载并上传附件时异常: {}"+e.getMessage(), e);
	        } finally {
	            if (is != null) {
	                try {
	                    is.close();
	                } catch (IOException e) {
	                    logger.error("关闭文件流时异常: {}"+e.getMessage(), e);
	                }
	            }
	        }
	    }
	    
	    /**
	     * 通过getFile接口获取文件流
	     * @param attachmentId 附件ID
	     * @return 文件输入流
	     */
	    private InputStream getFileStreamFromInterface(String attachmentId) {
	        try {
	            // 构建请求参数
	            JSONObject requestData = new JSONObject();
	            requestData.put("command", "getFile");
	            requestData.put("id", attachmentId);
	            requestData.put("appId",Constants.getAppId());
	            
	            // 添加时间戳
	            String timestamp = String.valueOf(System.currentTimeMillis());
	            requestData.put("timestamp", timestamp);
	            
	            // 计算签名
	            String secretKey = Constants.getSysSecretKey();
	            String signature = calculateSignature(requestData, secretKey);
	            requestData.put("signature", signature);
	            
	            logger.info("发送获取文件请求: {}"+requestData.toJSONString());
	            
	            return OkHttpUtil.getInstance().postSyncForStream(getOutInterfaceUrl(), requestData.toJSONString(), "application/json");
	            
	        } catch (Exception e) {
	            logger.error("获取文件流异常: {}"+ e.getMessage(), e);
	            return null;
	        }
	    }
	    
	    /**
	     * 通过getFile接口获取文件流
	     * @param attachmentId 附件ID
	     * @return 文件输入流
	     */
	    private JSONObject getAttachmentListFromInterface(String busiId) {
	    	
	    	try {
	            // 构建请求参数
	            JSONObject requestData = new JSONObject();
	            requestData.put("command", "getFileList");
	            requestData.put("busiId", busiId);
	            requestData.put("appId",Constants.getAppId());
	            
	            // 添加时间戳
	            String timestamp = String.valueOf(System.currentTimeMillis());
	            requestData.put("timestamp", timestamp);
	            
	            // 计算签名
	            String secretKey = Constants.getSysSecretKey();
	            String signature = calculateSignature(requestData, secretKey);
	            requestData.put("signature", signature);
	            
	            logger.info("发送获取附件列表请求: {}"+ requestData.toJSONString());
	            
	            // 发送HTTP请求
	            String response = OkHttpUtil.getInstance().postSync(getOutInterfaceUrl(), requestData.toJSONString(), "application/json");
	            
	            if (StringUtils.isNotBlank(response)) {
	                JSONObject result = JSONObject.parseObject(response);
	                logger.info("收到附件列表响应: {}"+ result.toJSONString());
	                return result;
	            } else {
	                logger.error("获取附件列表返回空响应");
	                return createErrorResult("获取附件列表返回空响应");
	            }
	            
	        } catch (Exception e) {
	            logger.error("获取附件列表失败: " + e.getMessage(), e);
	            return createErrorResult("获取附件列表失败: " + e.getMessage());
	        }
	    }
	    
	    
	    /**
	     * 计算签名
	     * @param requestData 请求数据
	     * @param secretKey 密钥
	     * @return 签名
	     */
	    protected String calculateSignature(JSONObject requestData, String secretKey) {
	        try {
	            requestData.put("serialId", StringUtils.isNotBlank(requestData.getString("serialId")) ? requestData.getString("serialId") : IDGenerator.getDefaultNUMID());
	            requestData.put("timestamp", StringUtils.isNotBlank(requestData.getString("timestamp")) ? requestData.getString("timestamp") :System.currentTimeMillis());
	            // 使用TreeMap按参数名称降序排列
	            TreeMap<String, Object> sortedParams = new TreeMap<>((a, b) -> b.compareTo(a));
	            
	            // 添加所有参数（除了signature）
	            for (String key : requestData.keySet()) {
	                if (!"signature".equals(key)) {
	                    Object value = requestData.get(key);
	                    if (value != null) {
	                        sortedParams.put(key.toUpperCase(), value.toString());
	                    }
	                }
	            }
	            
	            // 拼接参数名和值
	            StringBuilder signStr = new StringBuilder();
	            for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
	                signStr.append(entry.getKey()).append(entry.getValue());
	            }
	            
	            // 拼接密钥
	            signStr.append(secretKey);
	            
	            //logger.info("签名字符串: {}", signStr.toString());
	            
	            // 计算SHA256
	            return encryptBySHA256(signStr.toString());
	        } catch (Exception e) {
	            logger.error("计算签名失败: {}"+e.getMessage(), e);
	            return "";
	        }
	    }
	    
	    /**
	     * SHA256加密
	     * @param text 待加密文本
	     * @return 加密结果
	     */
	    protected String encryptBySHA256(String text) {
	        try {
	            MessageDigest md = MessageDigest.getInstance("SHA-256");
	            md.update(text.getBytes("UTF-8"));
	            byte[] digest = md.digest();
	            
	            // 转换为16进制字符串
	            StringBuilder hexString = new StringBuilder();
	            for (byte b : digest) {
	                String hex = Integer.toHexString(0xff & b);
	                if (hex.length() == 1) {
	                    hexString.append('0');
	                }
	                hexString.append(hex);
	            }
	            return hexString.toString();
	        } catch (Exception e) {
	            logger.error("SHA256加密失败: " + e.getMessage(), e);
	            return "";
	        }
	    }
	    
	  
	 
	  /**
     * 统一返回结果方法
     * @param success 是否成功
     * @param message 消息
     * @param data 数据
     * @return 统一格式的返回结果
     */
    public JSONObject createResult(boolean success, String message, Object data) {
        JSONObject result = new JSONObject();
        result.put("success", success);
        result.put("message", message);
        if (data != null) {
            result.put("data", data);
        }
        return result;
    }
    
    private List<JSONObject> getFileList (String busiId,EasyQuery query) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append(" select * from " + Constants.getSysSchema() + ".c_cf_attachment");
        sql.append(" where 1=1 ");
        sql.append(busiId," and BUSI_ID =? ",false);
        return query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());

    }

    /**
     * 创建错误结果
     * @param message 错误信息
     * @return 错误结果
     */
    protected JSONObject createErrorResult(String message) {
        JSONObject result = new JSONObject();
        result.put("success", false);
        result.put("message", message);
        return result;
    }

    /**
     * 获取OSS Bucket名称
     * @return Bucket名称
     */
    private String getOssBucket() {
        return Constants.getProperty("oss.bucket.name", "default-bucket");
    }
    
    /**
     * 获取OutInterface服务URL
     * @return URL地址
     */
    protected static String getOutInterfaceUrl() {
        try {
            String baseUrl = Constants.getOutInterface();
            return baseUrl + "/out/interface";
        } catch (Exception e) {
            return "http://localhost:8080/";
        }
    }
}
