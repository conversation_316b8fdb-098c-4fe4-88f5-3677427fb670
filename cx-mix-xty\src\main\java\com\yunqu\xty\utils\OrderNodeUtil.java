package com.yunqu.xty.utils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CacheUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.xty.base.CommonLogger;
import com.yunqu.xty.base.Constants;
import com.yunqu.xty.base.QueryFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class OrderNodeUtil {

	private static Logger logger = CommonLogger.getLogger("node");

	public static OrderNodeUtil getInstance() {
		return new OrderNodeUtil();
	}

	public OrderNodeUtil() {

	}

	private final String XTY_ORDER_NODE_CACHE = "XTY_ORDER_NODE_CACHE";

	private final String XTY_ORDER_NODE_LIST_CACHE = "XTY_ORDER_NODE_LIST_CACHE";

	public void reloadNode(UserModel user, String processId) {
		reloadNode(processId, user.getEpCode(), user.getDeptProvinceCode(), user.getSchemaName());
	}

	/**
	 * 加载节点缓存
	 * @param processId
	 */
	public void reloadNode(String processId, String entId, String provinceCode, String schema) {
		try {
			logger.info("重载工单环节配置 processId:" + processId + ",entId:" + entId + ",provinceCode:" + provinceCode + ",schema:" + schema);

			EasySQL sql = new EasySQL("select ID,CONFIG_JSON,PROVINCE");
			sql.append("from " + schema + ".XTY_PROVINCE_CONFIG t1");
			sql.append("where 1=1");
			sql.append(provinceCode, "and t1.PROVINCE = ?");
			sql.append(processId, "and t1.CONFIG_TYPE = ?");

			List<JSONObject> configDataList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if (configDataList != null) {
				for (JSONObject configData : configDataList) {
					String province = configData.getString("PROVINCE");
					String configId = configData.getString("ID");
					List<JSONObject> nodeList = new ArrayList<JSONObject>();
					if(StringUtils.isNotBlank(configId)) {
						nodeList = configData.getJSONObject("CONFIG_JSON").getJSONArray("orderNodeList").toJavaList(JSONObject.class);
					}

					if(CommonUtil.listIsNotNull(nodeList)) {
						CacheUtil.put(XTY_ORDER_NODE_LIST_CACHE + "_" + province + "_" + processId, nodeList);

						for (JSONObject nodeJson : nodeList) {
							String nodeId = nodeJson.getString("NODE_ID");
							String nodeStatus = nodeJson.getString("STATUS");
							CacheUtil.put(XTY_ORDER_NODE_CACHE + "_" + province + "_" + processId + "_" + nodeId, nodeJson.toJSONString());
							logger.info("设置缓存 cacheKey:" + XTY_ORDER_NODE_CACHE + "_" + province + "_" + processId + "_" + nodeId + ", value:" + nodeJson.toJSONString());
						}
					} else {
						logger.info("未识别到节点配置信息 processId[" + processId + "] provinceCode[" + province + "]");
					}
				}
			}

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	/**
	 * 获取节点信息缓存
	 * @param processId 流程key
	 * @param nodeId 节点ID
	 * @return
	 */
	public JSONObject getNodeInfo(String processId, String nodeId, String entId, String provinceCode, String schema) {
		String key = XTY_ORDER_NODE_CACHE + "_" + provinceCode + "_" + processId + "_" + nodeId;
		logger.info("getNodeInfo key :" + key);
		String nodeStr = CacheUtil.get(key);
		if(StringUtils.isNotBlank(nodeStr)) {
			return JSONObject.parseObject(nodeStr);
		} else {
			reloadNode(processId, entId, provinceCode, schema);
			nodeStr = CacheUtil.get(key);
			if(StringUtils.isNotBlank(nodeStr)) {
				return JSONObject.parseObject(nodeStr);
			}
		}
		return null;
	}


	public static JSONObject getFullNodeInfo(JSONObject node, JSONObject orderInfo, String schema, String nodeId,String flowKey) throws Exception {
		try {
			String deptCode = orderInfo.getString("ENT_DEPT_CODE");
			if (Constants.DUTY_FLOW_KEY.equals(flowKey)) {
				DutyUtil.getFullNodeInfo(node,orderInfo,schema,nodeId);
			} else {
				if (StringUtils.equalsAny(nodeId, new String[]{Constants.COMPLAIN_PREACCEPT,Constants.COMPLAIN_DEAL,
						Constants.MEDIATE_ENT_DEAL,Constants.MEDIATE_ENT_DEAL_CHECK,
						Constants.MEDIATE_ENT_CONFIRM,Constants.MEDIATE_ENT_CONFIRM_CHECK,
						Constants.DUTY_PROCESS_NODE_APPEND,Constants.DUTY_PROCESS_NODE_JUDGEMENT,
						Constants.DUTY_PROCESS_NODE_JUDGEMENT_CHECK,Constants.COMPLAIN_AUDIT_CHECK,
						Constants.COMPLAIN_ACCEPT_CHECK,Constants.COMPLAIN_DEAL_CHECK,
						Constants.COMPLAIN_PREACCEPT_CHECK})) {
					JSONObject provinceEntConfig = getDeptConfig(deptCode, schema);
					if (Objects.nonNull(provinceEntConfig)) {
						JSONObject json = provinceEntConfig.getJSONObject("CONFIG_JSON");
						if (Objects.nonNull(json.getJSONObject(nodeId))) {
							node.putAll(json.getJSONObject(nodeId));
						}
					}
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw new Exception("环节【"+nodeId+"】配置异常！" + e.getMessage());
		}
		return node;
	}

	public static JSONObject getFullNodeInfo2(JSONObject node, String deptCode, String schema, String nodeId) throws Exception {
		try {
			if (StringUtils.equalsAny(nodeId, new String[]{Constants.COMPLAIN_PREACCEPT,Constants.COMPLAIN_DEAL,
					Constants.MEDIATE_ENT_DEAL,Constants.MEDIATE_ENT_DEAL_CHECK,
					Constants.MEDIATE_ENT_CONFIRM,Constants.MEDIATE_ENT_CONFIRM_CHECK,
					Constants.DUTY_PROCESS_NODE_APPEND,Constants.DUTY_PROCESS_NODE_JUDGEMENT,
					Constants.DUTY_PROCESS_NODE_JUDGEMENT_CHECK,Constants.COMPLAIN_AUDIT_CHECK,
					Constants.COMPLAIN_ACCEPT_CHECK,Constants.COMPLAIN_DEAL_CHECK,
					Constants.COMPLAIN_PREACCEPT_CHECK})) {
				JSONObject deptConfig = getDeptConfig(deptCode, schema);
				if (Objects.nonNull(deptConfig)) {
					JSONObject json = deptConfig.getJSONObject("CONFIG_JSON");
					if (Objects.nonNull(json.getJSONObject(nodeId))) {
						node.putAll(json.getJSONObject(nodeId));
					}
				}
			}
			logger.info("获取节点配置：" + node.toJSONString());
		} catch (Exception e) {
			throw new Exception("环节【"+nodeId+"】配置异常！" + e.getMessage());
		}
		return node;
	}


	public static JSONObject getDeptConfig(String deptCode, String schema) {
		String key = "XTY_CONFIG_DEPT_" + deptCode;
		EasyQuery query = QueryFactory.getWriteQuery();
		String config = null;
		try {
			config = CacheUtil.get(key);
			if (StringUtils.isBlank(config)) {
				EasySQL sql = new EasySQL();
				sql.append(" select * from " + schema + ".XTY_DEPT_CONFIG t1 where 1=1 ");
				sql.append(deptCode, " and t1.DEPT_CODE = ?");
				JSONObject configData = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
				logger.info("获取省企业配置：" + sql.getFullSq());
				if (Objects.nonNull(configData)) {
					config = JSONObject.toJSONString(configData);
					CacheUtil.put(key, config, 600);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return StringUtils.isBlank(config) ? null : JSONObject.parseObject(config);
	}

	/**
	 * 获取流程节点列表
	 * @param processId 流程ID
	 * @param entId
	 * @param provinceCode
	 * @param schema
	 * @return
	 */
	public List<JSONObject> getNodeList(String processId, String entId, String provinceCode, String schema) {
		List<JSONObject> nodeList = CacheUtil.get(XTY_ORDER_NODE_LIST_CACHE + "_" + provinceCode + "_" + processId);
		if(nodeList != null) {
			return nodeList;
		} else {
			reloadNode(processId, entId, provinceCode, schema);

			nodeList = CacheUtil.get(XTY_ORDER_NODE_LIST_CACHE + "_" + provinceCode + "_" + processId);
			if(nodeList != null) {
				return nodeList;
			}
		}
		return null;
	}

	public EasyQuery getQuery() {
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
	}

}
