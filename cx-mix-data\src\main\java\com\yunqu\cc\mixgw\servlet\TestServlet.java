package com.yunqu.cc.mixgw.servlet;
import java.sql.SQLException;
import java.util.List;
import javax.servlet.annotation.WebServlet;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.mixgw.base.AppBaseServlet;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.util.BaseUtils;

@WebServlet("/api/testServlet")
public class TestServlet extends AppBaseServlet {
	
    protected Logger logger = CommonLogger.getLogger();
    //提取附件 查询不存在附件表的数据
    public EasyResult actionForExtractAttachments() {
    	String orderNo=getPara("orderNo");
    	String time=getPara("time");
    	
        //查询日期内的附件信息
        EasySQL sql=new EasySQL("select t1.*,T2.EP_CODE,T2.BUSI_ORDER_ID from ycbusi_ekf.c_box_appeal t1");
        sql.append(" left join ycbusi_ekf.c_bo_base_order t2 on t1.id=t2.id");
        sql.append(" where 1=1");
        sql.append("01"," and t1.EXIST_ATTACHMENT=? ");
        sql.append(orderNo," and t2.ORDER_NO=? ");
        if (StringUtils.isNotBlank(time)) {
        	 sql.append(time+" 00:00:00"," and t1.CREATE_TIME>=? ");
             sql.append(time+" 23:59:59"," and t1.CREATE_TIME<=? ");
		}
        sql.append(" and t1.APPEAL_ATTACHMENT is not null  ");
        sql.append( " and not exists ( ");
        sql.append(" select 1 from ycbusi_ekf.c_cf_attachment cca");
        sql.append(" where cca.BUSI_ID = t1.APPEAL_ATTACHMENT)  ");
       
        logger.info("提取工单外部附件失败的sql： " + sql.getFullSql());
        try{
        	EasyQuery query=getQuery();
        	BaseUtils baseUtils= BaseUtils.getInstance();
            List<JSONObject> list=query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
            if (CommonUtil.listIsNull(list))return EasyResult.ok("不存在未正常同步附件的数据");
            for (int i = 0; i < list.size(); i++) {
            	JSONObject obj=list.get(i);
            	String appealAttachment = obj.getString("APPEAL_ATTACHMENT");
            	String existAttachment = obj.getString("EXIST_ATTACHMENT");;
            	String orderId=obj.getString("ID");
            	if (StringUtils.isNotBlank(appealAttachment) && "01".equals(existAttachment)) {
            		 try {
            			 baseUtils.processAttachments(orderId, appealAttachment, 
            					 obj.getString("EP_CODE"), obj.getString("BUSI_ORDER_ID"),query);
                     } catch (Exception e) {
                         logger.error("处理附件失败，工单ID: {}, 错误: {}"+orderId+","+e.getMessage(), e);
                     }
            	}
			}	
          logger.info("【结束】");
          return EasyResult.ok();
        }catch (Exception e) {
            logger.error( "error:" + e.getMessage(), e);
            return EasyResult.error(500,"提取未正常同步附件的数据失败");
        }      
    }

	//提取附件 查询不存在附件表的数据
	public EasyResult actionForFileIdAttachments() {
		String fileId=getPara("fileId");
		if (StringUtils.isBlank(fileId)){
			return  EasyResult.error(500,"附件id不能为空");
		}
		try{

			    BaseUtils baseUtils= BaseUtils.getInstance();
			    EasyQuery query=getQuery();
				try {
					//baseUtils.processAttachments("", fileId,
					//		Constants.getProperty("ENT_ID",""),Constants.getProperty("BUSI_ORDER_ID",""),query);

					JSONObject json=new JSONObject();
					json.put("id",fileId);
					baseUtils.downloadAndUploadAttachment(json,"",Constants.getProperty("ENT_ID",""),Constants.getProperty("BUSI_ORDER_ID",""),query);
				} catch (Exception e) {
					logger.error("处理附件失败，附件ID: {}, 错误: {}"+fileId+","+e.getMessage(), e);
				}


			logger.info("【结束】");
			return EasyResult.ok();
		}catch (Exception e) {
			logger.error( "error:" + e.getMessage(), e);
			return EasyResult.error(500,"提取未正常同步附件的数据失败");
		}
	}

	public EasyResult actionForBusiIdAttachments() {
		String busiId=getPara("busiId");
		if (StringUtils.isBlank(busiId)){
			return  EasyResult.error(500,"busiId不能为空");
		}
		try{

			BaseUtils baseUtils= BaseUtils.getInstance();
			EasyQuery query=getQuery();
			try {
				baseUtils.processAttachments("", busiId,
						Constants.getProperty("ENT_ID",""),Constants.getProperty("BUSI_ORDER_ID",""),query);

			} catch (Exception e) {
				logger.error("处理附件失败，附件ID: {}, 错误: {}"+busiId+","+e.getMessage(), e);
			}


			logger.info("【结束】");
			return EasyResult.ok();
		}catch (Exception e) {
			logger.error( "error:" + e.getMessage(), e);
			return EasyResult.error(500,"提取未正常同步附件的数据失败");
		}
	}


	//根据工单id拉取
    public EasyResult actionForPullId() {
    	String orderNo=getPara("orderNo");
    	String orderId=getPara("orderId");
    	String time=getPara("time");
    	
        //查询日期内的附件信息
        EasySQL sql=new EasySQL("select t1.*,T2.EP_CODE,T2.BUSI_ORDER_ID from ycbusi_ekf.c_box_appeal t1");
        sql.append(" left join ycbusi_ekf.c_bo_base_order t2 on t1.id=t2.id");
        sql.append(" where 1=1");
        sql.append("01"," and t1.EXIST_ATTACHMENT=? ");
        sql.append(orderNo," and t2.ORDER_NO=? ");
        sql.append(orderId," and t1.ID=? ");
        
        if (StringUtils.isNotBlank(time)) {
        	 sql.append(time+" 00:00:00"," and t1.CREATE_TIME>=? ");
             sql.append(time+" 23:59:59"," and t1.CREATE_TIME<=? ");
		}
        /**
        sql.append(" and t1.APPEAL_ATTACHMENT is not null  ");
        sql.append( " and not exists ( ");
        sql.append(" select 1 from ycbusi_ekf.c_cf_attachment cca");
        sql.append(" where cca.BUSI_ID = t1.APPEAL_ATTACHMENT)  ");**/
       
        logger.info("根据工单id提取工单外部附件失败的sql： " + sql.getFullSql());
        try{
        	EasyQuery query=getQuery();
        	BaseUtils baseUtils= BaseUtils.getInstance();
            List<JSONObject> list=query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
            if (CommonUtil.listIsNull(list))return EasyResult.ok("不存在未正常同步附件的数据");
            for (int i = 0; i < list.size(); i++) {
            	JSONObject obj=list.get(i);
            	String appealAttachment = obj.getString("APPEAL_ATTACHMENT");
            	String existAttachment = obj.getString("EXIST_ATTACHMENT");;
            	String id=obj.getString("ID");
            	if (StringUtils.isNotBlank(appealAttachment) && "01".equals(existAttachment)) {
            		 try {
            			 baseUtils.processAttachments(id, appealAttachment, 
            					 obj.getString("EP_CODE"), obj.getString("BUSI_ORDER_ID"),query);
                     } catch (Exception e) {
                         logger.error("处理附件失败，工单ID: {}, 错误: {}"+id+","+e.getMessage(), e);
                     }
            	}
			}	
          logger.info("【结束】");
          return EasyResult.ok();
        }catch (Exception e) {
            logger.error( "error:" + e.getMessage(), e);
            return EasyResult.error(500,"提取未正常同步附件的数据失败");
        }      
    } 
    
    /**修改**/
    public EasyResult actionForUpdata() {
    	String orderId=getPara("orderId");
    	EasySQL sql=new EasySQL("select t1.* from ycbusi_ekf.cx_file_temporary_temp t1");
    	sql.append(" where 1=1");
    	sql.append("N"," and IS_SYS=? ");
    	sql.append(orderId," and ID=? ");
    	EasyQuery query=getQuery();
    	try {
			List<JSONObject> list=query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			 if (CommonUtil.listIsNull(list))return null;
			 for (int i = 0; i < list.size(); i++) {
				 JSONObject obj=list.get(i);
				 String id=obj.getString("ID");
				 if(StringUtils.isNotBlank(id)) {
					 sql=new EasySQL();
					 sql.append(" UPDATE "+Constants.getSysSchema()+".C_BOX_APPEAL_ORDER  SET IS_APPEAL_FILE=? WHERE M_ID=? ");
	     			 query.execute(sql.getSQL(), "02",  id);
	                 
	     			 sql=new EasySQL();
	     			 sql.append(" UPDATE "+Constants.getSysSchema()+".c_box_appeal SET EXIST_ATTACHMENT=? ,APPEAL_ATTACHMENT=? WHERE ID=? ");
	     			 query.execute(sql.getSQL(), "02","",  id);
	     			
	     			 sql=new EasySQL();
	     			 sql.append(" UPDATE "+Constants.getSysSchema()+".cx_file_temporary_temp  SET IS_SYS=? WHERE ID=? ");
	     			 query.execute(sql.getSQL(), "Y",  id);
				 }
                
				 
			 }
			 logger.info("修改完成");
			 
		} catch (SQLException e) {
			logger.error(e.getMessage());
		}
		return null;
    }
}