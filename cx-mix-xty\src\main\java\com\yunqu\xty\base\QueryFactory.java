package com.yunqu.xty.base;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyQuery;

import java.util.Random;

public class QueryFactory {


	/**
	 * 随机获取一个写数据源(当没有request请求时，要使用query对象，可以用该方法)
	 * @return
	 */
	public static EasyQuery getWriteQuery(){
		Random random = new Random();
		int num = random.nextInt(3);
		if(num==2){
			return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_TWO);
		}
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
	}

	public static EasyQuery getReadQuery(){
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
	}

	/**
	 * 根据企业id平均分配，获取写数据源
	 * @param entId
	 * @return
	 */
	public static EasyQuery getQuery(String entId){
		Random random = new Random();
		int num = random.nextInt(3);
		if(num==2){
			return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_TWO);
		}
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
	}

}
