package com.yunqu.handle.service;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.ServiceUtil;
import com.yunqu.handle.base.CommonLogger;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.base.QueryFactory;
import com.yunqu.handle.base.mapper.HumpMapper;
import com.yunqu.handle.model.BoxAppeal;

import com.yunqu.handle.util.OrderNoUtil;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.EasyRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 申诉工单创建服务
 * 负责查询未创建工单的申诉记录并调用OrderSubmitInfService发起工单
 */
public class AppealOrderCreateService {
    
    private static final Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("appeal").getName());
    
    /**
     * 查询未创建工单的申诉记录
     * @param pageIndex 页码（从0开始）
     * @param pageSize 每页记录数
     * @return 查询结果
     */
    public JSONObject queryUnprocessedAppeals(int pageIndex, int pageSize) {
        try {
            EasyQuery query = QueryFactory.getWriteQuery();
            
            // 构建查询SQL
            EasySQL sql = new EasySQL();
            sql.append("SELECT a.* FROM " + getTableName("c_box_appeal") + " a ");
            sql.append("LEFT JOIN " + getTableName("c_bo_base_order") + " o ON a.ID = o.ID ");
            sql.append("WHERE a.IS_SYSN = 'N'");
            sql.append("AND o.ID IS NULL ");
            sql.append("01","AND IS_APPEAL = ?");
            // sql.append("04","AND SERVICE_STATUS != ?");
            sql.append(DateUtil.addDay(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -7)," AND a.CREATE_TIME >= ?");
            sql.append("ORDER BY a.CREATE_TIME ASC ");
            sql.append("LIMIT " + pageIndex * pageSize + ", " + pageSize);
            
            logger.info("查询未处理申诉SQL: {}", sql.getFullSq());
            
            // 执行查询
            List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new HumpMapper());
            
            // 返回查询结果
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", list);
            result.put("total", list.size());
            result.put("pageIndex", pageIndex);
            result.put("pageSize", pageSize);
            
            return result;
        } catch (Exception e) {
            logger.error("查询未处理申诉记录失败: " + e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            return result;
        }
    }


    /**
     * 查询未创建工单的投诉记录
     * @param pageIndex 页码（从0开始）
     * @param pageSize 每页记录数
     * @return 查询结果
     */
    public JSONObject queryUnprocessedComplains(int pageIndex, int pageSize) {
        try {
            EasyQuery query = QueryFactory.getWriteQuery();
            
            // 构建查询SQL
            EasySQL sql = new EasySQL();
            sql.append("SELECT a.* FROM " + getTableName("c_box_appeal") + " a ");
            sql.append("LEFT JOIN " + getTableName("c_bo_complain_order") + " o ON a.ID = o.ID ");
            sql.append("WHERE a.IS_SYSN = 'N' ");
            sql.append("AND o.ID IS NULL ");
            sql.append("02","AND IS_APPEAL = ?");
            sql.append(DateUtil.addDay(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -7)," AND a.CREATE_TIME >= ?");
            sql.append("ORDER BY a.CREATE_TIME ASC ");
            sql.append("LIMIT " + pageIndex * pageSize + ", " + pageSize);
            
            logger.info("查询未处理投诉SQL: {}", sql.getFullSq());
            
            // 执行查询
            List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
            
            // 返回查询结果
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", list);
            result.put("total", list.size());
            result.put("pageIndex", pageIndex);
            result.put("pageSize", pageSize);
            return result;
        } catch (Exception e) {
            logger.error("查询未处理投诉记录失败: " + e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 处理申诉记录，调用OrderSubmitInfService发起工单
     * @param appealList 申诉记录列表
     */
    public void processAppeals(List<JSONObject> appealList) {
        if (appealList == null || appealList.isEmpty()) {
            logger.info("没有需要处理的申诉记录");
            return;
        }
        
        logger.info("开始处理{}条申诉记录", appealList.size());
        
        for (JSONObject appeal : appealList) {
            logger.info("处理申诉记录[{}]", JSONObject.toJSONString(appeal));
            try {
                if ("04".equals(appeal.getString("serviceStatus"))) {
                    logger.info("申诉记录[{}]状态为04，无需处理", appeal.getString("id"));
                    saveRecord(JSONObject.parseObject(appeal.toJSONString(), BoxAppeal.class));
                    updateAppealStatus(appeal.getString("id"));
                    logger.info("申诉记录[{}]工单创建成功", appeal.getString("id"));
                } else {
                    // 调用OrderSubmitInfService发起工单
                    JSONObject result = createOrder(JSONObject.parseObject(appeal.toJSONString(), BoxAppeal.class));
                    
                    // 更新申诉记录状态
                    if (result != null && result.getBooleanValue("success")) {
                        updateAppealStatus(appeal.getString("id"));
                        logger.info("申诉记录[{}]工单创建成功", appeal.getString("id"));
                    } else {
                        String errorMsg = result != null ? result.getString("message") : "调用服务失败";
                        logger.error("申诉记录[{}]工单创建失败: {}", appeal.getString("id"), errorMsg);
                    }
                }
                
            } catch (Exception e) {
                logger.error("处理申诉记录[" + appeal.getString("id") + "]异常: " + e.getMessage(), e);
            }
        }
    }

    private boolean checkExist (String id) {
        synchronized(id.intern()) {
            try {
                EasySQL sql = new EasySQL();
                sql.append(" select count(1) from " + Constants.getSysSchema() + ".c_bo_base_order");
                sql.append(id," where ID =? ",false);
                logger.info("查询工单是否存在SQL: {}", sql.getFullSq());
                return QueryFactory.getQuery().queryForExist(sql.getSQL(),sql.getParams());
            } catch (Exception e) {
                logger.error("查询工单是否存在异常: " + e.getMessage(), e);
                return false;
            }
        }
    }


    private boolean checkComplainExist (String id) {
        synchronized(id.intern()) {
            try {
                EasySQL sql = new EasySQL();
                sql.append(" select count(1) from " + Constants.getSysSchema() + ".c_bo_complain_order");
                sql.append(id," where ID =? ",false);
                logger.info("查询工单是否存在SQL: {}", sql.getFullSq());
                return QueryFactory.getQuery().queryForExist(sql.getSQL(),sql.getParams());
            } catch (Exception e) {
                logger.error("查询工单是否存在异常: " + e.getMessage(), e);
                return false;
            }
        }
    }

    /**
     * 处理申诉记录，调用OrderSubmitInfService发起工单
     * @param complainList 申诉记录列表
     */
    public void processComplains(List<JSONObject> complainList) {
        if (complainList == null || complainList.isEmpty()) {
            logger.info("没有需要处理的投诉记录");
            return;
        }
        
        logger.info("开始处理{}条投诉记录", complainList.size());
        
        // 首先尝试批量插入
        try {
            boolean batchResult = batchInsertComplainOrders(complainList);
            if (batchResult) {
                // 批量插入成功，批量更新状态
                for (JSONObject complain : complainList) {
                    try {
                        updateAppealStatus(complain.getString("ID"));
                        logger.info("投诉记录[{}]批量工单创建成功", complain.getString("ID"));
                    } catch (Exception e) {
                        logger.error("更新投诉记录[{}]状态失败: {}", complain.getString("ID"), e.getMessage(), e);
                    }
                }
                logger.info("批量插入{}条投诉记录成功", complainList.size());
                return;
            }
        } catch (Exception e) {
            logger.warn("批量插入投诉记录失败，降级为单条插入: {}", e.getMessage());
        }
        
        // 批量插入失败，降级为单条插入
        logger.info("开始单条插入{}条投诉记录", complainList.size());
        for (JSONObject complain : complainList) {
            try {
                // 单条写入投诉工单到c_bo_complain_order表
                boolean insertResult = insertComplainOrder(complain);
                
                // 更新原表同步状态
                if (insertResult) {
                    updateAppealStatus(complain.getString("ID"));
                    logger.info("投诉记录[{}]单条工单创建成功", complain.getString("ID"));
                } else {
                    logger.error("投诉记录[{}]单条工单创建失败", complain.getString("ID"));
                }
            } catch (Exception e) {
                logger.error("处理投诉记录[{}]异常: {}", complain.getString("ID"), e.getMessage(), e);
            }
        }
    }

    private void saveRecord( BoxAppeal model) throws SQLException {
        String schema = Constants.getSysSchema();
        String flowKey = "xty_ss";
        JSONObject appealJson = new JSONObject();
        appealJson.put("ID", IDGenerator.getDefaultNUMID());
         // 所属省份
        appealJson.put("PROVINCE_CODE", model.getProvinceCode());
        // 省份名称
        appealJson.put("PROVINCE_NAME", model.getProvinceName());
        // 所属区域
        appealJson.put("CITY_CODE", model.getCityCode());
        // 城市名称
        appealJson.put("CITY_NAME", model.getCityName());
        // 被申诉企业名称
        appealJson.put("ENT_DEPT_NAME", model.getEntDeptName());
        // 创建时间
        appealJson.put("CREATE_TIME", model.getCreateTime());
        // 申诉时间
        appealJson.put("APPEAL_TIME", model.getAppealTime());
        // 申诉涉及号码
        appealJson.put("APPEAL_PHONE", model.getAppealPhone());
        // 联系号码
        appealJson.put("PHONE", model.getPhone());
        // 申诉人姓名
        appealJson.put("APPEAL_NAME",model.getAppealName());
        // 身份证号
        appealJson.put("ID_TYPE", model.getCardType());
        
        appealJson.put("ID_CARD", model.getIdCard());
        // 申诉内容
        appealJson.put("APPEAL_CONTENT", model.getAppealContent());
        // 企业类型编码
        appealJson.put("ENT_TYPE", model.getEntType());
        // 被申诉企业
        appealJson.put("ENT_DEPT_CODE", getDeptCode(model.getEntDeptCode(),model.getProvinceCode()));
        // 附件
        appealJson.put("IS_APPEAL_FILE", model.getExistAttachment());
        appealJson.put("APPEAL_ATTACHMENT", model.getAppealAttachment());
        // 投诉编码
        appealJson.put("APPEAL_CODE", model.getAppealCode());
        // 电子邮箱
        appealJson.put("EMAIL", model.getEmail());
        // 部流水号
        appealJson.put("SECTION_NO", model.getSerialId());
        // 通讯地址
        appealJson.put("ADDRESS",model.getAddress());
        // 申诉来源
        appealJson.put("APPEAL_SOURCE", model.getAppealSource());
        // 邮政编码
        appealJson.put("POST_CODE", model.getPostCode());
        appealJson.put("IS_COMPLAINED", model.getIsComplained());
        appealJson.put("IS_BACK_RESULT", model.getIsBackResult());
        appealJson.put("IS_SATISFY_RESULT", model.getIsSatisfyResult());
        // appealJson.put("IS_APPEAL_FILE", );
        appealJson.put("COMPLAIN_TIME", model.getComplainTime());
        appealJson.put("IP_ADDRESS", StringUtils.isNotBlank(model.getIpAddress()) ? model.getIpAddress().split(",")[0] : "");
        appealJson.put("APPEAL_ORG", model.getOrgName());
        appealJson.put("ORG_CODE", model.getOrgCode());
        appealJson.put("APPEAL_CODE", model.getAppealCode());
        appealJson.put("DEPT_TYPE", model.getDeptType());
        appealJson.put("CONCACT_NAME", model.getConcactName());
        appealJson.put("CONCACT_CARD_TYPE", model.getConcactCardType());
        appealJson.put("CONCACT_ID_CARD", model.getConcactIdCard());
        appealJson.put("M_ID", model.getId());
        appealJson.put("SERVICE_STATUS", "06");
        appealJson.put("APPEAL_RESULT", "04");
        JSONObject baseJson = new JSONObject();
        baseJson.put("ID", model.getId());
        baseJson.put("ORDER_NO", OrderNoUtil.generateHisDeptSerialNo(schema, flowKey, model.getAppealTime()) );
        baseJson.put("PROC_KEY", flowKey);
        baseJson.put("CREATE_ACC", "system");
        baseJson.put("CREATE_NAME", "系统");
        baseJson.put("CREATE_DEPT_CODE", "system");
        baseJson.put("CREATE_TIME", DateUtil.getCurrentDateStr());
        baseJson.put("EP_CODE", Constants.getEntId());
        baseJson.put("BUSI_ORDER_ID", Constants.getBusiOrderId());
        baseJson.put("STATUS", "2");
        baseJson.put("PROVINCE", appealJson.getString("PROVINCE_CODE"));
        baseJson.put("CITY", appealJson.getString("CITY_CODE"));
        EasyRecord baseRecord = new EasyRecord(schema + ".C_BO_BASE_ORDER", "ID");
        baseRecord.putAll(baseJson);
        logger.info("保存基本信息：" + JSONObject.toJSONString(baseJson));
        EasyQuery query = QueryFactory.getQuery();
        query.save(baseRecord);

        EasyRecord busiRecord = new EasyRecord(schema + ".C_BOX_APPEAL_ORDER", "ID");
        busiRecord.putAll(appealJson);
        query.save(busiRecord);
        logger.info("保存申诉信息：" + JSONObject.toJSONString(appealJson));
        try {
            JSONObject param = new JSONObject() {{put("orderId",model.getId());put("schema",Constants.getSysSchema());put("entId",Constants.getEntId());put("busiOrderId",Constants.getBusiOrderId());}};
            logger.info("调用大模型服务：" + JSONObject.toJSONString(param));
            JSONObject invoke2 = ServiceUtil.invoke2("XTY_INVOKE_AI_SERVICE",param);
            logger.info("大模型服务返回：" + JSONObject.toJSONString(invoke2));
            addFlow(Constants.getSysSchema(), model.getId(), "6", "用户撤诉", "用户自行撤诉，工单关闭。");
        } catch (Exception e) {
            logger.error("调用大模型服务异常: {}", e.getMessage(), e);
        }
        
       
    }
    
    /**
     * 调用OrderSubmitInfService发起工单
     * @param model 申诉记录
     * @return 调用结果
     */
    private JSONObject createOrder(BoxAppeal model) {
        try {
            if (checkExist(model.getId())) {
                updateAppealStatus(model.getId());
                JSONObject result = new JSONObject();
                result.put("success", false);
                result.put("message", "调用服务异常: 工单已存在" );
                return result;
            }
            // 构建调用参数
            JSONObject data = new JSONObject();
            // 设置业务数据
            JSONObject appealJson = new JSONObject();
             // 所属省份
            appealJson.put("PROVINCE_CODE", model.getProvinceCode());
            // 省份名称
            appealJson.put("PROVINCE_NAME", model.getProvinceName());
            // 所属区域
            appealJson.put("CITY_CODE", model.getCityCode());
            // 城市名称
            appealJson.put("CITY_NAME", model.getCityName());
            // 被申诉企业名称
            appealJson.put("ENT_DEPT_NAME", model.getEntDeptName());
            // 申诉时间
            appealJson.put("APPEAL_TIME", model.getAppealTime());
            // 申诉涉及号码
            appealJson.put("APPEAL_PHONE", model.getAppealPhone());
            // 联系号码
            appealJson.put("PHONE", model.getPhone());
            // 申诉人姓名
            appealJson.put("APPEAL_NAME",model.getAppealName());
            // 身份证号
            appealJson.put("ID_TYPE", model.getCardType());
            
            appealJson.put("ID_CARD", model.getIdCard());
            // 申诉内容
            appealJson.put("APPEAL_CONTENT", model.getAppealContent());
            // 企业类型编码
            appealJson.put("ENT_TYPE", model.getEntType());
            // 被申诉企业
            appealJson.put("ENT_DEPT_CODE", getDeptCode(model.getEntDeptCode(),model.getProvinceCode()));
            // 附件
            appealJson.put("IS_APPEAL_FILE", model.getExistAttachment());
            appealJson.put("APPEAL_ATTACHMENT", model.getAppealAttachment());
            // 投诉编码
            appealJson.put("APPEAL_CODE", model.getAppealCode());
            // 电子邮箱
            appealJson.put("EMAIL", model.getEmail());
            // 部流水号
            appealJson.put("SECTION_NO", model.getSerialId());
            // 通讯地址
            appealJson.put("ADDRESS",model.getAddress());
            // 申诉来源
            appealJson.put("APPEAL_SOURCE", model.getAppealSource());
            // 邮政编码
            appealJson.put("POST_CODE", model.getPostCode());
            appealJson.put("IS_COMPLAINED", model.getIsComplained());
            appealJson.put("IS_BACK_RESULT", model.getIsBackResult());
            appealJson.put("IS_SATISFY_RESULT", model.getIsSatisfyResult());
           // appealJson.put("IS_APPEAL_FILE", );
            appealJson.put("COMPLAIN_TIME", model.getComplainTime());
            appealJson.put("IP_ADDRESS", StringUtils.isNotBlank(model.getIpAddress()) ? model.getIpAddress().split(",")[0] : "");
            appealJson.put("APPEAL_ORG", model.getOrgName());
            appealJson.put("ORG_CODE", model.getOrgCode());
            appealJson.put("APPEAL_CODE", model.getAppealCode());
            appealJson.put("DEPT_TYPE", model.getDeptType());
            appealJson.put("CONTACT_NAME", model.getConcactName());
            appealJson.put("CONTACT_CARD_TYPE", model.getConcactCardType());
            appealJson.put("CONTACT_ID_CARD", model.getConcactIdCard());
            data.put("c_box_appeal_order", appealJson);
            JSONObject param = new JSONObject();
            param.put("operType", "add");
            param.put("orderId", model.getId());
            param.put("entId", Constants.getEntId());
            param.put("schema", Constants.getSysSchema());
            param.put("busiOrderId", Constants.getBusiOrderId());
            param.put("userAcc", "system");
            param.put("userName", "系统");
            param.put("deptCode", "000000".equals(model.getOrgCode()) ? "500501" : "system");
            param.put("deptName", "000000".equals(model.getOrgCode()) ? "部中心" :"系统");
            param.put("flowKey", "xty_ss");
            param.put("cityCode", model.getCityCode());
            param.put("provinceCode", model.getProvinceCode());
            param.put("data", data);
            JSONObject urlParam = new JSONObject();
            param.put("urlParam", urlParam);

            logger.info("-->[创建申诉工单请求] param:" + param.toJSONString());
            IService service = ServiceContext.getService("XTY_ORDER_IMPORT_INF_SERVICE");
            JSONObject result = service.invoke(param);
            logger.info("-->[创建申诉工单响应] result:" + result.toJSONString());
            if (result != null && "1".equals(result.getString("state"))) {
                return new JSONObject(){{
                    put("success", true);
                    put("message", "申诉记录工单创建成功");
                }};
            } else {
                logger.error("申诉记录[{}]工单创建失败: {}", model.getId(), result.toJSONString());
                throw new Exception("申诉记录工单创建失败: " + result.getString("msg"));
            }
           
        } catch (Exception e) {
            logger.error("调用OrderSubmitInfService服务异常: " + e.getMessage(), e);
            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("message", "调用服务异常: " + e.getMessage());
            return result;
        }
    }

    private String getDeptCode (String deptCode,String provinceCode) throws SQLException {
        if (!Arrays.asList("1","2","3","4").contains(deptCode)) {
            logger.info("非基础运营商直接返回部门编码：{}",deptCode);
            return deptCode;
        }
        return  QueryFactory.getWriteQuery().queryForString("select SKILL_GROUP_CODE from " + Constants.getSysSchema() + ".CC_SKILL_GROUP where GROUP_TYPE = ? and PROVINCE_CODE = ?", new Object[]{"9902030" + deptCode, provinceCode});
    }
    
    /**
     * 更新申诉记录状态为已同步
     * @param appealId 申诉ID
     */
    private void updateAppealStatus(String appealId) {
        try {
            EasyQuery query = QueryFactory.getWriteQuery();
            
            // 构建更新SQL
            EasySQL sql = new EasySQL();
            sql.append("UPDATE " + getTableName("c_box_appeal") + " SET IS_SYSN = 'Y' ");
            sql.append(appealId," WHERE ID = ?",false);
            // 执行更新
            query.execute(sql.getSQL(), sql.getParams());
            
            logger.info("记录[{}]状态已更新为已同步", appealId);
        } catch (Exception e) {
            logger.error("更新记录[" + appealId + "]状态失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 批量插入投诉工单到c_bo_complain_order表
     * @param complainList 投诉记录列表
     * @return 批量插入是否成功
     */
    private boolean batchInsertComplainOrders(List<JSONObject> complainList) {
        if (complainList == null || complainList.isEmpty()) {
            return true;
        }
        
        try {
            EasyQuery query = QueryFactory.getWriteQuery();
            
            // 构建批量插入SQL
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("INSERT INTO ").append(getTableName("c_bo_complain_order")).append(" (");
            sqlBuilder.append("ID, M_ID, ENT_ID, BUSI_ORDER_ID, CREATE_TIME, UPDATE_TIME, ");
            sqlBuilder.append("ENT_TYPE, PROVINCE_CODE, PROVINCE_NAME, CITY_CODE, CITY_NAME, ");
            sqlBuilder.append("APPEAL_NAME, PHONE, COMPLAIN_CONTENT, ADDRESS, ORDER_NO, ");
            sqlBuilder.append("ENT_DEPT_CODE, ENT_DEPT_NAME, APPEAL_ATTACHMENT,APPEAL_SOURCE");
            sqlBuilder.append(") VALUES ");
            sqlBuilder.append("(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)");
            
            // 构建参数列表
            String currentTime = DateUtil.getCurrentDateStr();
            List<Object[]> list = new ArrayList<Object[]>();
            for (JSONObject complain : complainList) {
                Object[] params = new Object[20];
                params[0] = complain.getString("ID");
                params[1] = "";
                params[2] = Constants.getEntId();
                params[3] = Constants.getBusiOrderId();
                params[4] = complain.getString("CREATE_TIME");
                params[5] = currentTime;
                params[6] = "990104".equals(complain.getString("ENT_TYPE")) ? complain.getString("ENT_DEPT_CODE") : complain.getString("ENT_TYPE");
                params[7] = complain.getString("PROVINCE_CODE");
                params[8] = complain.getString("PROVINCE_NAME");
                params[9] = complain.getString("CITY_CODE");
                params[10] = complain.getString("CITY_NAME");
                params[11] = complain.getString("APPEAL_NAME");
                params[12] = complain.getString("PHONE");
                params[13] = complain.getString("APPEAL_CONTENT");
                params[14] = complain.getString("ADDRESS");
                params[15] = complain.getString("SERIAL_ID");
                params[16] = getDeptCode(complain.getString("ENT_DEPT_CODE"),complain.getString("PROVINCE_CODE"));
                params[17] = complain.getString("ENT_DEPT_NAME");
                params[18] = complain.getString("APPEAL_ATTACHMENT");
                params[19] = getAppealSource(complain.getString("APPEAL_SOURCE"));
                list.add(params);
            }
            
            logger.info("批量插入投诉工单SQL: {}", sqlBuilder.toString());
            logger.info("批量插入投诉工单数量: {}", complainList.size());
            
            // 执行批量插入
            query.executeBatch(sqlBuilder.toString(), list);
            return true;
        } catch (Exception e) {
            logger.error("批量插入投诉工单失败: {}", e.getMessage(), e);
            return false;
        }
    }

    public String getAppealSource(String appealSource) {
        switch(appealSource) {
            case "01":
                appealSource = "00";
                break;
            case "02":
                appealSource = "01";
                break;
            case "03":
                appealSource = "02";
                break;
        }
        return appealSource;
    }
    
    /**
     * 插入投诉工单到c_bo_complain_order表（单条）
     * @param complain 投诉记录
     * @return 插入是否成功
     */
    private boolean insertComplainOrder(JSONObject complain) {
        try {
            String id = complain.getString("ID");
            EasyQuery query = QueryFactory.getWriteQuery();
            if (checkComplainExist(id)) {
                updateAppealStatus(id);
                return false;
            }
            // 使用EasyRecord创建记录
            EasyRecord record = new EasyRecord(getTableName("c_bo_complain_order"),"ID");
            
            // 设置字段值
            String currentTime = DateUtil.getCurrentDateStr();
            record.set("ID", complain.getString("ID"));
            record.set("M_ID","");
            record.set("ENT_ID", Constants.getEntId());
            record.set("BUSI_ORDER_ID", Constants.getBusiOrderId());
            record.set("CREATE_TIME", complain.getString("CREATE_TIME"));
            record.set("UPDATE_TIME", currentTime);
            record.set("ENT_TYPE", "990104".equals(complain.getString("ENT_TYPE")) ? complain.getString("ENT_DEPT_CODE") : complain.getString("ENT_TYPE"));
            record.set("PROVINCE_CODE", complain.getString("PROVINCE_CODE"));
            record.set("PROVINCE_NAME", complain.getString("PROVINCE_NAME"));
            record.set("CITY_CODE", complain.getString("CITY_CODE"));
            record.set("CITY_NAME", complain.getString("CITY_NAME"));
            record.set("APPEAL_NAME", complain.getString("APPEAL_NAME"));
            record.set("PHONE", complain.getString("PHONE"));
            record.set("COMPLAIN_CONTENT", complain.getString("APPEAL_CONTENT"));
            record.set("ADDRESS", complain.getString("ADDRESS"));
            record.set("ORDER_NO", complain.getString("SERIAL_ID"));
            record.set("ENT_DEPT_CODE", getDeptCode(complain.getString("ENT_DEPT_CODE"),complain.getString("PROVINCE_CODE")));
            record.set("ENT_DEPT_NAME", complain.getString("ENT_DEPT_NAME"));
            record.set("APPEAL_ATTACHMENT", complain.getString("APPEAL_ATTACHMENT"));
            
            logger.info("单条插入投诉工单，ID: {}", complain.getString("ID"));
            
            // 执行插入
            query.save(record);
            return true;
        } catch (Exception e) {
            logger.error("插入投诉工单[{}]失败: {}", complain.getString("ID"), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取表名（带schema）
     * @param tableName 表名
     * @return 带schema的表名
     */
    private String getTableName(String tableName) {
        return Constants.getSysSchema() + "." + tableName;
    }

    
     /**
     * 添加跟进记录
     *
     * @param schema
     * @param orderId
     * @param type    跟进类型 0-创建工单 1-办理工单 2-转派工单 3-撤回工单 4-退回工单 5-签收工单 6-其他 7-暂存 8-取消签收工单 9-挂起 10-取消挂起 11-异常暂存 12-到达 13-分派 14-自动回访 15-工单归档 16-用户催办 17-用户补充信息 18-用户撤单 19-用户评价 20-自动分派 21-自动回收
     * @param content
     * @throws ServiceException
     */
    private void addFlow(String schema, String orderId, String type, String taskName, String content) {
        try {
            JSONObject param = new JSONObject();
            param.put("command", "addOrderFollow");
            param.put("entId", Constants.getEntId());
            param.put("busiOrderId", Constants.getBusiOrderId());
            param.put("schema", schema);
            param.put("taskName", taskName);
            param.put("userAcc", "system");
            param.put("userName", "系统");
            param.put("type", type);
            param.put("content", content);
            param.put("orderId", orderId);
            param.put("deptCode",  "system");
            logger.info("-->[生成跟进记录请求] param:" + param.toJSONString());
            ServiceUtil.invoke2("CC_EORDER_OPERATION_HANDLE", param);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

}