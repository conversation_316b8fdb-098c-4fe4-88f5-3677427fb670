package com.yunqu.tariff.servlet;

import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.yunqu.tariff.base.Constants;
import com.yunqu.tariff.base.QueryFactory;
import com.yunqu.tariff.service.TariffCrawlCheckService;
import com.yunqu.xty.commonex.kit.ElasticsearchKit;
import com.yunqu.xty.commonex.util.RedissonUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.slf4j.Logger;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.Yqlogger;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.tariff.base.AppBaseServlet;
import com.yunqu.tariff.base.CommonLogger;
import com.yunqu.tariff.service.TariffCheckService;
import com.yunqu.tariff.utils.LogUtil;

import cn.hutool.core.util.IdUtil;

/**
 * <p>
 * 资费规则检查Servlet
 * </p>
 *
 * @ClassName TariffRuleServlet
 * <AUTHOR> Copy This Tag)
 * @Description 资费规则检查Servlet
 * @Since create in 7/1/24 2:30 PM
 * @Version v1.0
 * @Copyright Copyright (c) 2024
 * @Company 广州云趣信息科技有限公司
 * 
 * 接口地址说明：
 * 1. 报送库核查接口：
 *    - 同步核查：/servlet/tariffRule/checkFields
 *    - 异步核查：/servlet/tariffRule/asyncCheckFields
 *    - 根据省份和企业核查：/servlet/tariffRule/recheckTariffByProvinceAndEnt
 * 
 * 2. 公示库核查接口：
 *    - 同步核查：/servlet/tariffRule/checkPublicLibFields
 *    - 根据省份和企业核查：/servlet/tariffRule/recheckPublicLibTariffByProvinceAndEnt
 * 
 * 3. 同时核查报送库和公示库：
 *    - 同步核查：/servlet/tariffRule/checkAllFields
 *    - 异步核查：/servlet/tariffRule/asyncCheckAllFields
 * 
 * 4. 规则管理接口：
 *    - 获取规则列表：/servlet/tariffRule/getRuleList
 *    - 更新规则配置：/servlet/tariffRule/updateRule
 * 
 * 5. 检查结果管理接口：
 *    - 更新报送库检查结果：/servlet/tariffRule/updateTariffCheckResult
 *    - 更新公示库检查结果：/servlet/tariffRule/updatePublicLibCheckResult
 * 
 * 6. 任务状态管理接口：
 *    - 获取检查任务状态：/servlet/tariffRule/getCheckStatus
 *    - 清除检查任务状态：/servlet/tariffRule/clearCheckStatus
 *    - 获取同步任务状态：/servlet/tariffRule/getSyncStatus
 * 
 * 7. 同步爬虫数据接口：
 *    - 同步爬虫数据到公示库：/servlet/tariffRule/syncCrawlDataToPublic
 */
@WebServlet("/servlet/tariffRule/*")
public class TariffRuleServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    private Logger logger = CommonLogger.logger;
    private Yqlogger yqlogger = new Yqlogger();

    private static final ExecutorService checkTaskExecutor = Executors.newSingleThreadExecutor();

    /**
     * 执行报送库资费字段检查
     * 
     * 请求参数：
     * startDate: 开始日期，格式yyyyMMdd，可选，默认为昨天
     * endDate: 结束日期，格式yyyyMMdd，可选，默认为昨天
     * isAll: 是否全量检查，可选，默认为false
     * ruleNo: 规则编号，可选，如果不为空，则只检查指定规则
     * tariffId: 资费ID，可选，如果不为空，则只检查指定ID的资费记录
     * 
     * 请求示例：
     * /servlet/tariffRule/checkFields?startDate=20240701&endDate=20240701&isAll=true&ruleNo=1
     */
    public JSONObject actionForCheckFields() {
        try {
            UserModel user = UserUtil.getUser(getRequest());

            // 获取请求参数
            String startDate = getPara("startDate");
            String endDate = getPara("endDate");
            String isAllStr = getPara("isAll");
            String ruleNo = getPara("ruleNo");
            String tariffId = getPara("tariffId");

            // 处理isAll参数
            Boolean isAll = null;
            if (StringUtils.isNotBlank(isAllStr)) {
                isAll = Boolean.parseBoolean(isAllStr);
            }

            // 检查是否有任务正在运行
            boolean isRunning = TariffCheckService.isReportCheckRunning();
            if (isRunning) {
                logger.warn("已有报送库检查任务正在运行，无法启动新的检查任务");
                return EasyResult.fail("已有报送库检查任务正在运行，请稍后再试");
            }

            // 记录操作日志
            String logMessage = String.format("执行报送库资费字段检查，开始日期：%s，结束日期：%s，是否全量：%s，规则编号：%s，资费ID：%s",
                    StringUtils.isBlank(startDate) ? "默认(昨天)" : startDate,
                    StringUtils.isBlank(endDate) ? "默认(昨天)" : endDate,
                    isAll == null ? "默认(false)" : isAll,
                    StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                    StringUtils.isBlank(tariffId) ? "全部资费" : tariffId);
            logger.info(logMessage);

            // 创建任务记录ID
            String taskId = IdUtil.getSnowflakeNextIdStr();

            // 创建运行信息
            JSONObject runningInfo = new JSONObject();
            runningInfo.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            runningInfo.put("startDate", startDate);
            runningInfo.put("endDate", endDate);
            runningInfo.put("isAll", isAll);
            runningInfo.put("tariffId", tariffId);
            runningInfo.put("ruleNo", ruleNo);
            runningInfo.put("taskId", taskId);
            runningInfo.put("operator", user.getUserName());
            runningInfo.put("checkType", TariffCheckService.CHECK_TYPE_REPORT);
            runningInfo.put("checkTypeName", TariffCheckService.getCheckTypeName(TariffCheckService.CHECK_TYPE_REPORT));

            // 设置检查任务运行状态
//            TariffCheckService.setCheckRunning(runningInfo, TariffCheckService.CHECK_TYPE_REPORT);

            // 获取当前请求参数的副本，用于异步任务
            final String finalStartDate = startDate;
            final String finalEndDate = endDate;
            final Boolean finalIsAll = isAll;
            final String finalRuleNo = ruleNo;
            final String finalTariffId = tariffId;
            
            // 在线程池中异步执行报送库资费字段检查
            checkTaskExecutor.submit(() -> {
                try {
                    logger.info("开始异步执行报送库资费字段检查，任务ID: {}", taskId);
            TariffCheckService checkService = new TariffCheckService();
                    checkService.checkTariffFields(finalStartDate, finalEndDate, finalIsAll, finalTariffId, finalRuleNo, TariffCheckService.CHECK_TYPE_REPORT);
                    logger.info("异步报送库资费字段检查任务完成，任务ID: {}", taskId);
                } catch (Exception e) {
                    logger.error("异步执行报送库资费字段检查时发生错误，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
                } finally {
                    // 清除检查任务运行状态
                    TariffCheckService.clearCheckRunning(TariffCheckService.CHECK_TYPE_REPORT);
                }
            });

            // 返回成功结果
            JSONObject result = new JSONObject();
            result.put("taskId", taskId);
            result.put("message", "报送库资费字段检查任务已提交");
            result.put("startDate", StringUtils.isBlank(startDate) ? "默认(昨天)" : startDate);
            result.put("endDate", StringUtils.isBlank(endDate) ? "默认(昨天)" : endDate);
            result.put("isAll", isAll == null ? false : isAll);
            result.put("ruleNo", StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo);
            result.put("tariffId", StringUtils.isBlank(tariffId) ? "全部资费" : tariffId);
            result.put("checkType", TariffCheckService.CHECK_TYPE_REPORT);
            result.put("checkTypeName", TariffCheckService.getCheckTypeName(TariffCheckService.CHECK_TYPE_REPORT));

            return EasyResult.ok(result, "报送库资费字段检查任务已提交，正在后台异步执行");
        } catch (Exception e) {
            // 发生异常时，尝试清除运行状态
            try {
                TariffCheckService.clearCheckRunning(TariffCheckService.CHECK_TYPE_REPORT);
            } catch (Exception ex) {
                logger.error("清除检查任务运行状态时发生错误", ex);
            }
            
            logger.error("执行报送库资费字段检查时发生错误", e);
            return EasyResult.fail("执行报送库资费字段检查失败：" + e.getMessage());
        }
    }

    /**
     * 执行公示库资费字段检查
     * 
     * 请求参数：
     * startDate: 开始日期，格式yyyyMMdd，可选，默认为昨天
     * endDate: 结束日期，格式yyyyMMdd，可选，默认为昨天
     * isAll: 是否全量检查，可选，默认为false
     * ruleNo: 规则编号，可选，如果不为空，则只检查指定规则
     * tariffId: 资费ID，可选，如果不为空，则只检查指定ID的资费记录
     * 
     * 请求示例：
     * /servlet/tariffRule/checkPublicLibFields?startDate=20240701&endDate=20240701&isAll=true&ruleNo=1
     */
    public JSONObject actionForCheckPublicLibFields() {
        try {
            UserModel user = UserUtil.getUser(getRequest());

            // 获取请求参数
            String startDate = getPara("startDate");
            String endDate = getPara("endDate");
            String isAllStr = getPara("isAll");
            String ruleNo = getPara("ruleNo");
            String tariffId = getPara("tariffId");

            // 处理isAll参数
            Boolean isAll = null;
            if (StringUtils.isNotBlank(isAllStr)) {
                isAll = Boolean.parseBoolean(isAllStr);
            }

            // 检查是否有公示库任务正在运行
            boolean isRunning = TariffCheckService.isPublicCheckRunning();
            if (isRunning) {
                logger.warn("已有公示库检查任务正在运行，无法启动新的公示库检查任务");
                return EasyResult.fail("已有公示库检查任务正在运行，请稍后再试");
            }

            // 记录操作日志
            String logMessage = String.format("执行公示库资费字段检查，开始日期：%s，结束日期：%s，是否全量：%s，规则编号：%s，资费ID：%s",
                    StringUtils.isBlank(startDate) ? "默认(昨天)" : startDate,
                    StringUtils.isBlank(endDate) ? "默认(昨天)" : endDate,
                    isAll == null ? "默认(false)" : isAll,
                    StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                    StringUtils.isBlank(tariffId) ? "全部资费" : tariffId);
            logger.info(logMessage);

            // 创建任务记录ID
            String taskId = IdUtil.getSnowflakeNextIdStr();

            // 创建运行信息
            JSONObject runningInfo = new JSONObject();
            runningInfo.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            runningInfo.put("startDate", startDate);
            runningInfo.put("endDate", endDate);
            runningInfo.put("isAll", isAll);
            runningInfo.put("tariffId", tariffId);
            runningInfo.put("ruleNo", ruleNo);
            runningInfo.put("taskId", taskId);
            runningInfo.put("operator", user.getUserName());
            runningInfo.put("checkType", TariffCheckService.CHECK_TYPE_PUBLIC);
            runningInfo.put("checkTypeName", TariffCheckService.getCheckTypeName(TariffCheckService.CHECK_TYPE_PUBLIC));

//            // 设置检查任务运行状态
//            TariffCheckService.setCheckRunning(runningInfo, TariffCheckService.CHECK_TYPE_PUBLIC);

            // 获取当前请求参数的副本，用于异步任务
            final String finalStartDate = startDate;
            final String finalEndDate = endDate;
            final Boolean finalIsAll = isAll;
            final String finalRuleNo = ruleNo;
            final String finalTariffId = tariffId;
            
            // 在线程池中异步执行公示库资费字段检查
            checkTaskExecutor.submit(() -> {
                try {
                    logger.info("开始异步执行公示库资费字段检查，任务ID: {}", taskId);
                    TariffCheckService checkService = new TariffCheckService();
                    checkService.checkTariffFields(finalStartDate, finalEndDate, finalIsAll, finalTariffId, finalRuleNo, TariffCheckService.CHECK_TYPE_PUBLIC);
                    logger.info("异步公示库资费字段检查任务完成，任务ID: {}", taskId);
                } catch (Exception e) {
                    logger.error("异步执行公示库资费字段检查时发生错误，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
                } finally {
                    // 清除检查任务运行状态
                    TariffCheckService.clearCheckRunning(TariffCheckService.CHECK_TYPE_PUBLIC);
                }
            });

            // 返回成功结果
            JSONObject result = new JSONObject();
            result.put("taskId", taskId);
            result.put("message", "公示库资费字段检查任务已提交");
            result.put("startDate", StringUtils.isBlank(startDate) ? "默认(昨天)" : startDate);
            result.put("endDate", StringUtils.isBlank(endDate) ? "默认(昨天)" : endDate);
            result.put("isAll", isAll == null ? false : isAll);
            result.put("ruleNo", StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo);
            result.put("tariffId", StringUtils.isBlank(tariffId) ? "全部资费" : tariffId);
            result.put("checkType", TariffCheckService.CHECK_TYPE_PUBLIC);
            result.put("checkTypeName", TariffCheckService.getCheckTypeName(TariffCheckService.CHECK_TYPE_PUBLIC));

            return EasyResult.ok(result, "公示库资费字段检查任务已提交，正在后台异步执行");
        } catch (Exception e) {
            // 发生异常时，尝试清除运行状态
            try {
                TariffCheckService.clearCheckRunning(TariffCheckService.CHECK_TYPE_PUBLIC);
            } catch (Exception ex) {
                logger.error("清除检查任务运行状态时发生错误", ex);
            }
            
            logger.error("执行公示库资费字段检查时发生错误", e);
            return EasyResult.fail("执行公示库资费字段检查失败：" + e.getMessage());
        }
    }

    /**
     * 获取规则列表
     * 
     * 请求示例：
     * /servlet/tariffRule/getRuleList
     * 
     * 响应示例：
     * {
     *   "code": 0,
     *   "msg": "获取规则列表成功",
     *   "data": [
     *     {
     *       "ID": "1",
     *       "RULE_NO": "1",
     *       "RULE_DESC": "资费名称重复",
     *       "CHECK_FIELD": "资费名称",
     *       "CHECK_POINT": "同一适用地区内资费进行比较，如果资费名字相同，则为有问题",
     *       "DISPLAY_CHECK_POINT": "同一适用地区内资费进行比较，如果资费名字相同，则为有问题",
     *       "CONFIG_JSON": "{\"ex1\":\"关键词1,关键词2\"}",
     *       "CONFIG_OBJ": {
     *         "ex1": "关键词1,关键词2"
     *       },
     *       "ex1": "关键词1,关键词2",
     *       "EX1_IS_VARIABLE": false,
     *       "EX2_IS_VARIABLE": false,
     *       "editable": true,
     *       "DISPLAY_RULE_DESC": "资费名称重复",
     *       "LAST_UPDATE_TIME": "2024-07-01 10:00:00",
     *       "REMARK": "备注信息"
     *     }
     *   ]
     * }
     */
    public JSONObject actionForGetRuleList() {
        try {
            UserModel user = UserUtil.getUser(getRequest());
            
            logger.info("开始获取规则列表");
            
            // 查询规则配置，按RULE_NO整数排序
            String sql = "SELECT * FROM " + getTableName("XTY_TARIFF_CHECK_RULE") + " ORDER BY ID ASC";
            EasyQuery query = getQuery();
            List<JSONObject> ruleList = query.queryForList(sql, new Object[]{}, new JSONMapperImpl());
            
            if (ruleList == null || ruleList.isEmpty()) {
                logger.warn("未找到规则配置");
                return EasyResult.fail("未找到规则配置");
            }
            
            logger.info("查询到{}条规则配置", ruleList.size());
            
            // 处理配置字段，将JSON字符串转换为对象，并处理规则描述
            for (JSONObject rule : ruleList) {
                String configJson = rule.getString("CONFIG_JSON");
                boolean hasEx1 = false;
                boolean hasEx2 = false;
                
                // 确保EX1_IS_VARIABLE和EX2_IS_VARIABLE字段存在，如果数据库中没有这些字段，则默认为false
                if (!rule.containsKey("EX1_IS_VARIABLE")) {
                    rule.put("EX1_IS_VARIABLE", false);
                }
                if (!rule.containsKey("EX2_IS_VARIABLE")) {
                    rule.put("EX2_IS_VARIABLE", false);
                }

                
                if (StringUtils.isNotBlank(configJson)) {
                    try {
                        JSONObject config = JSONObject.parseObject(configJson);
                        rule.put("CONFIG_OBJ", config);
                        
                        // 直接添加ex1到规则对象中，方便前端使用
                        if (config.containsKey("ex1")) {
                            String ex1Value = config.getString("ex1");
                            rule.put("ex1", ex1Value);
                            hasEx1 = StringUtils.isNotBlank(ex1Value);
                        }
                        
                        // 只有当ex2存在且不为空时才添加到规则对象中
                        if (config.containsKey("ex2")) {
                            String ex2Value = config.getString("ex2");
                            if (StringUtils.isNotBlank(ex2Value)) {
                                rule.put("ex2", ex2Value);
                                hasEx2 = true;
                            }
                        }
                        
                        // 处理规则描述，替换ex1和ex2的占位符
                        processRuleDesc(rule, config);
                        
                        // 处理检查点描述，替换ex1和ex2的占位符
                        processCheckPoint(rule, config);

                    } catch (Exception e) {
                        logger.error("解析规则配置JSON失败: {}", e.getMessage(), e);
                        rule.put("CONFIG_OBJ", new JSONObject());
                    }
                } else {
                    rule.put("CONFIG_OBJ", new JSONObject());
                    rule.put("DISPLAY_CHECK_POINT", rule.getString("CHECK_POINT"));
                }
                
                // 检查规则是否可编辑
                // 规则可编辑的条件：CHECK_POINT中包含${ex1}或${ex2}占位符
                String checkPoint = rule.getString("CHECK_POINT");
                boolean containsPlaceholder = false;
                if (StringUtils.isNotBlank(checkPoint)) {
                    containsPlaceholder = checkPoint.contains("${ex1}") || checkPoint.contains("${ex2}");
                }
                
                // 添加可编辑标志
                boolean editable = containsPlaceholder || hasEx1 || hasEx2;
                rule.put("editable", editable);
                
                // 如果没有ex1，但是规则是可编辑的，则添加空值
                if (editable && !rule.containsKey("ex1")) {
                    rule.put("ex1", "");
                }
                
                // 注意：我们不再为空的ex2添加默认值，只有当ex2存在时才返回给前端
            }
            
            logger.info("获取规则列表成功，返回{}条规则", ruleList.size());
            return EasyResult.ok(ruleList, "获取规则列表成功");
        } catch (Exception e) {
            logger.error("获取规则列表时发生错误", e);
            return EasyResult.fail("获取规则列表失败：" + e.getMessage());
        }
    }

    private static final String CACHE_KEY_PREFIX = "TARIFF_CHECK_RUNNING:";


    /**
     * 更新规则配置
     * 
     * 请求路径：
     * /servlet/tariffRule/updateRule
     * 
     * 请求方式：
     * POST
     * 
     * 请求体示例：
     * {
     *   "id": "1",
     *   "ex1": "2099,4099",
     *   "ex2": "其他可选参数",
     *   "ex1IsVariable": false,
     *   "ex2IsVariable": false
     * }
     * 
     * 响应示例：
     * {
     *   "code": 0,
     *   "msg": "更新规则配置成功，已启动规则检查任务",
     *   "data": {
     *     "id": "1",
     *     "ruleNo": "1",
     *     "ex1": "2099,4099",
     *     "ex2": "其他参数",
     *     "updateTime": "2024-07-10 15:30:45",
     *     "checkStarted": true
     *   }
     * }
     * 
     * 测试命令示例：
     * curl -X POST -H "Content-Type: application/json" -d '{"id":"1","ex1":"2099,4099","ex2":"其他参数","ex1IsVariable":false,"ex2IsVariable":false}' http://localhost:8080/servlet/tariffRule/updateRule
     */
    public JSONObject actionForUpdateRule() {
        try {
            // 获取JSON请求参数
            JSONObject param = getJSONObject();
            String id = param.getString("id");
            
            logger.info("收到更新规则配置请求，规则ID: {}, 请求参数: {}", id, param.toJSONString());
            
            if (StringUtils.isBlank(id)) {
                logger.warn("规则ID为空，更新失败");
                return EasyResult.fail("规则ID不能为空");
            }

            if("8".equals(id)){
                if(!param.containsKey("ex2")){
                    logger.warn("规则8的配置项2没传");
                    return EasyResult.fail("规则8的配置项2没传");
                }
            }

            // 获取规则编号
            String ruleNo = null;
            try {
                // 查询规则编号
                String queryRuleNoSql = "SELECT RULE_NO FROM " + getTableName("XTY_TARIFF_CHECK_RULE") + " WHERE ID = ?";
                EasyQuery query = getQuery();
                List<JSONObject> ruleList = query.queryForList(queryRuleNoSql, new Object[]{id}, new JSONMapperImpl());
                
                if (ruleList != null && !ruleList.isEmpty()) {
                    ruleNo = ruleList.get(0).getString("RULE_NO");
                }
            } catch (Exception e) {
                logger.error("查询规则编号失败，规则ID: {}, 错误: {}", id, e.getMessage(), e);
            }
            
            // 获取ex1和ex2参数
            String ex1 = param.getString("ex1");
            String ex2 = param.getString("ex2");

            logger.info("规则参数解析，规则ID: {}, ex1: {}, ex2: {}",
                    id, ex1, ex2);
            
            // 构建CONFIG_JSON
            JSONObject configJson = new JSONObject();
            if (StringUtils.isNotBlank(ex1)) {
                configJson.put("ex1", ex1);
            }
            if (StringUtils.isNotBlank(ex2)) {
                configJson.put("ex2", ex2);
            }
            
            // 获取当前时间
            String updateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            
            logger.info("构建配置JSON，规则ID: {}, CONFIG_JSON: {}, 更新时间: {}", id, configJson.toJSONString(), updateTime);
            
            // 构建更新SQL
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("UPDATE ").append(getTableName("XTY_TARIFF_CHECK_RULE")).append(" SET ");
            
            // 根据参数构建SQL
            List<Object> params = new ArrayList<>();
            params.add(configJson.toJSONString());
            params.add(updateTime);
            
            sqlBuilder.append("CONFIG_JSON = ?, LAST_UPDATE_TIME = ?");

            
            sqlBuilder.append(" WHERE ID = ?");
            params.add(id);
            
            logger.info("执行SQL更新，SQL: {}", sqlBuilder.toString());
            
            // 执行更新
            EasyQuery query = getQuery();
            query.execute(sqlBuilder.toString(), params.toArray());
            
            logger.info("规则配置更新成功，规则ID: {}", id);

        } catch (Exception e) {
            logger.error("更新规则配置时发生错误: {}", e.getMessage(), e);
            return EasyResult.fail("更新规则配置失败：" + e.getMessage());
        }
        return EasyResult.ok();
    }
    
    /**
     * 处理规则描述，替换ex1和ex2的占位符
     * @param rule 规则对象
     * @param config 配置对象
     */
    private void processRuleDesc(JSONObject rule, JSONObject config) {
        String ruleDesc = rule.getString("RULE_DESC");
        String displayRuleDesc = "";
        
        if (StringUtils.isNotBlank(ruleDesc)) {
            displayRuleDesc = ruleDesc;
            
            // 替换ex1占位符
            if (ruleDesc.contains("${ex1}") && config.containsKey("ex1")) {
                String ex1Value = config.getString("ex1");
                displayRuleDesc = displayRuleDesc.replace("${ex1}", ex1Value);
            }
            
            // 替换ex2占位符
            if (ruleDesc.contains("${ex2}") && config.containsKey("ex2")) {
                String ex2Value = config.getString("ex2");
                displayRuleDesc = displayRuleDesc.replace("${ex2}", ex2Value);
            }
        }
        
        // 无论原始RULE_DESC是否为空，都添加DISPLAY_RULE_DESC字段
        rule.put("DISPLAY_RULE_DESC", displayRuleDesc);
        logger.info("处理规则描述，规则ID: {}, 原始RULE_DESC: {}, DISPLAY_RULE_DESC: {}", 
                rule.getString("ID"), ruleDesc, displayRuleDesc);
    }

    /**
     * 处理检查点描述，替换ex1和ex2的占位符，并将变量名替换为中文名称
     * @param rule 规则对象
     * @param config 配置对象
     */
    private void processCheckPoint(JSONObject rule, JSONObject config) {
        String checkPoint = rule.getString("CHECK_POINT");
        String displayCheckPoint = "";
        
        if (StringUtils.isNotBlank(checkPoint)) {
            displayCheckPoint = checkPoint;
            
            // 替换ex1占位符
            if (checkPoint.contains("${ex1}") && config.containsKey("ex1")) {
                String ex1Value = config.getString("ex1");
                
                // 检查ex1是否为变量
                boolean ex1IsVariable = rule.getBooleanValue("EX1_IS_VARIABLE");
                if (ex1IsVariable && StringUtils.isNotBlank(ex1Value)) {
                    // 将变量名替换为中文名称
                    String[] variables = ex1Value.split(",");
                    List<String> zhNames = new ArrayList<>();
                    
                    for (String variable : variables) {
                        String trimVar = variable.trim();
                        String zhName = getFieldZhName(trimVar);
                        zhNames.add(zhName);
                    }
                    
                    String zhValue = String.join("、", zhNames);
                    displayCheckPoint = displayCheckPoint.replace("${ex1}", zhValue);
                    
                    // 添加变量标识和原始变量名，便于前端识别和使用
                    rule.put("ex1_is_variable", true);
                } else {
                    displayCheckPoint = displayCheckPoint.replace("${ex1}", ex1Value);
                    rule.put("ex1_is_variable", false);
                }
            }
            
            // 替换ex2占位符
            if (checkPoint.contains("${ex2}") && config.containsKey("ex2")) {
                String ex2Value = config.getString("ex2");
                
                // 检查ex2是否为变量
                boolean ex2IsVariable = rule.getBooleanValue("EX2_IS_VARIABLE");
                if (ex2IsVariable && StringUtils.isNotBlank(ex2Value)) {
                    // 将变量名替换为中文名称
                    String[] variables = ex2Value.split(",");
                    List<String> zhNames = new ArrayList<>();
                    
                    for (String variable : variables) {
                        String trimVar = variable.trim();
                        String zhName = getFieldZhName(trimVar);
                        zhNames.add(zhName);
                    }
                    
                    String zhValue = String.join("、", zhNames);
                    displayCheckPoint = displayCheckPoint.replace("${ex2}", zhValue);
                    
                    // 添加变量标识和原始变量名，便于前端识别和使用
                    rule.put("ex2_is_variable", true);
                } else {
                    displayCheckPoint = displayCheckPoint.replace("${ex2}", ex2Value);
                    rule.put("ex2_is_variable", false);
                }
            }
        }
        
        // 无论原始CHECK_POINT是否为空，都添加DISPLAY_CHECK_POINT字段
        rule.put("DISPLAY_CHECK_POINT", displayCheckPoint);
        logger.info("处理检查点描述，规则ID: {}, 原始CHECK_POINT: {}, DISPLAY_CHECK_POINT: {}, ex1是否变量: {}, ex2是否变量: {}", 
                rule.getString("ID"), checkPoint, displayCheckPoint, 
                rule.getBooleanValue("EX1_IS_VARIABLE"), rule.getBooleanValue("EX2_IS_VARIABLE"));
    }

    /**
     * 获取字段的中文名称
     * @param fieldName 字段名称
     * @return 中文名称
     */
    private String getFieldZhName(String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            return "";
        }
        
        String zhName = com.yunqu.tariff.utils.BusiUtil.FieldZhMap.getString(fieldName);
        if (StringUtils.isBlank(zhName)) {
            return fieldName; // 如果没有找到中文名称，则返回原字段名
        }
        
        return zhName;
    }

    /**
     * 重新核查指定ID的资费记录
     *
     * 请求参数：
     * tariffId: 资费ID，必填，指定要重新核查的资费记录ID
     * ruleNo: 规则编号，可选，如果不为空，则只检查指定规则
     *
     * 请求示例：
     * /servlet/tariffRule/recheckTariff?tariffId=12345&ruleNo=1
     *
     * 响应示例：
     * {
     *   "code": 0,
     *   "msg": "资费重新核查成功",
     *   "data": {
     *     "tariffId": "12345",
     *     "ruleNo": "1",
     *     "checkTime": "2024-07-10 15:30:45"
     *   }
     * }
     */
    public JSONObject actionForRecheckTariff() {
        try {
            UserModel user = UserUtil.getUser(getRequest());

            // 获取资费ID参数和规则编号参数
            String tariffId = getPara("tariffId");
            String ruleNo = getPara("ruleNo");
            String checkType = getPara("checkType");

            if(StringUtils.isBlank(checkType)){
                checkType="1";
            }

            if (StringUtils.isBlank(tariffId)) {
                logger.warn("资费ID为空，无法进行重新核查");
                return EasyResult.fail("资费ID不能为空");
            }

            // 检查是否有任务正在运行
            boolean isRunning = TariffCheckService.isCheckRunning();
            if (isRunning) {
                logger.warn("已有检查任务正在运行，无法启动新的检查任务");
                return EasyResult.fail("已有检查任务正在运行，请稍后再试");
            }

            logger.info("开始重新核查资费，资费ID: {}, 规则编号: {}, 操作人: {}",
                    tariffId, StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo, user.getUserName());

            // 调用检查服务进行重新核查
            TariffCheckService checkService = new TariffCheckService();
            checkService.checkTariffFields(null, null, true, tariffId, ruleNo,Integer.parseInt(checkType));

            // 获取当前时间作为检查时间
            String checkTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            // 返回成功结果
            JSONObject result = new JSONObject();
            result.put("tariffId", tariffId);
            result.put("ruleNo", StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo);
            result.put("checkTime", checkTime);

            logger.info("资费重新核查完成，资费ID: {}, 规则编号: {}, 检查时间: {}",
                    tariffId, StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo, checkTime);
            return EasyResult.ok(result, "资费重新核查成功");
        } catch (Exception e) {
            logger.error("重新核查资费时发生错误", e);
            return EasyResult.fail("重新核查资费失败：" + e.getMessage());
        }
    }

    /**
     * 更新资费检查结果
     * 
     * 请求参数：
     * tariffId: 资费ID，必填
     * ruleNos: 规则编号，多个编号用逗号分隔，例如"8,10"，可以为空字符串，表示清除检查结果
     * 
     * 请求示例：
     * /servlet/tariffRule/updateTariffCheckResult?tariffId=12345&ruleNos=8,10
     * /servlet/tariffRule/updateTariffCheckResult?tariffId=12345&ruleNos=
     * 
     * 响应示例：
     * {
     *   "code": 0,
     *   "msg": "更新资费检查结果成功",
     *   "data": {
     *     "tariffId": "12345",
     *     "ruleNos": "8,10",
     *     "checkResult": "【退订方式填写不规范】，【下线时间填写不规范】",
     *     "checkTime": "2024-07-10 15:30:45"
     *   }
     * }
     */
    public JSONObject actionForUpdateTariffCheckResult() {
        try {
            UserModel user = UserUtil.getUser(getRequest());
            JSONObject jsonparam = this.getJSONObject();

            // 获取参数
            String tariffId = jsonparam.getString("tariffId");
            String ruleNos = jsonparam.getString("ruleNos");

            if (StringUtils.isBlank(tariffId)) {
                logger.warn("资费ID为空，无法更新检查结果");
                return EasyResult.fail("资费ID不能为空");
            }
            
            logger.info("开始更新资费检查结果，资费ID: {}, 规则编号: {}, 操作人: {}", 
                    tariffId, StringUtils.isBlank(ruleNos) ? "空" : ruleNos, user.getUserName());
            
            // 获取当前时间
            String checkTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            
            String checkResult = "";
            
            // 解析规则编号字符串为列表，用于存储到 FIELD_CHECK_NOS 字段
            List<String> ruleNosList = new ArrayList<>();
            
            // 如果ruleNos不为空，则查询对应的规则描述
            if (StringUtils.isNotBlank(ruleNos)) {
                // 查询规则描述
                String[] ruleNoArray = ruleNos.split(",");
                List<String> ruleDescList = new ArrayList<>();
                
                // 将规则编号添加到列表
                for (String ruleNo : ruleNoArray) {
                    if (StringUtils.isNotBlank(ruleNo)) {
                        ruleNosList.add(ruleNo.trim());
                    }
                }
                
                // 构建SQL查询条件
                StringBuilder sqlBuilder = new StringBuilder();
                sqlBuilder.append("SELECT RULE_NO, RULE_DESC FROM ").append(getTableName("XTY_TARIFF_CHECK_RULE"));
                sqlBuilder.append(" WHERE RULE_NO IN (");
                
                for (int i = 0; i < ruleNoArray.length; i++) {
                    if (i > 0) {
                        sqlBuilder.append(",");
                    }
                    sqlBuilder.append("?");
                }
                sqlBuilder.append(") ORDER BY ID ASC");

                // 准备参数
                Object[] params = new Object[ruleNoArray.length];
                for (int i = 0; i < ruleNoArray.length; i++) {
                    params[i] = ruleNoArray[i].trim();
                }
                
                logger.info("查询规则描述，SQL: {}, 参数: {}", sqlBuilder.toString(), Arrays.toString(params));
                
                // 执行查询
                EasyQuery query = getQuery();
                List<JSONObject> ruleList = query.queryForList(sqlBuilder.toString(), params, new JSONMapperImpl());
                
                // 构建检查结果
                for (JSONObject rule : ruleList) {
                    String ruleDesc = rule.getString("RULE_DESC");
                    if (StringUtils.isNotBlank(ruleDesc)) {
                        ruleDescList.add("【" + ruleDesc + "】");
                    }
                }
                
                checkResult = String.join("，", ruleDescList);
                logger.info("构建检查结果，资费ID: {}, 检查结果: {}", tariffId, checkResult);
            } else {
                // 如果ruleNos为空，则将检查结果和规则编号设置为空字符串
                ruleNos = "";
                checkResult = "";
                logger.info("规则编号为空，将清除资费ID: {} 的检查结果", tariffId);
            }
            
            // 1. 更新数据库中的资费检查结果
            String updateSql = "UPDATE " + getTableName("xty_tariff_record") +
                               " SET FIELD_CHECK_RESULT = ?, FIELD_CHECK_NO = ?, FIELD_CHECK_TIME = ? WHERE ID = ?";
            
            EasyQuery query = getQuery();
            query.execute(updateSql, new Object[]{checkResult, ruleNos, checkTime, tariffId});

            // 调用ES服务更新索引
            IService service =
                    ServiceContext.getService("XTY_EVT_ES_ORDER_OPERATE");

            // 构建更新数据
            JSONObject updateData = new JSONObject();
            updateData.put("FIELD_CHECK_RESULT", checkResult);
            updateData.put("FIELD_CHECK_NO", ruleNos);
            updateData.put("FIELD_CHECK_TIME", checkTime);
            // 添加新字段 FIELD_CHECK_NOS，存储规则编号数组
            updateData.put("FIELD_CHECK_NOS", ruleNosList);

            // 构建ES请求参数
            JSONObject reqParam = new JSONObject();
            reqParam.put("primary", tariffId); // 使用资费ID作为文档ID
            reqParam.put("indexName", Constants.XTY_TARIFF_BAK_INFO_INDEX); // 使用备份索引
            reqParam.put("command", Constants.ES_OPERATE_UPDATE_DOC); // 更新文档操作
            reqParam.put("data", updateData); // 更新的数据

            // 调用ES服务
            service.invoke(reqParam);
            
            logger.info("更新资费检查结果成功，资费ID: {}, 规则编号: {}, 检查结果: {}, 检查时间: {}", 
                    tariffId, ruleNos, checkResult, checkTime);
            
            // 返回成功结果
            JSONObject resultObj = new JSONObject();
            resultObj.put("tariffId", tariffId);
            resultObj.put("ruleNos", ruleNos);
            resultObj.put("checkResult", checkResult);
            resultObj.put("checkTime", checkTime);
            resultObj.put("dbUpdateSuccess", true);
            resultObj.put("esUpdateSuccess", true);

            return EasyResult.ok(resultObj, "更新资费检查结果成功");

            } catch(Exception e){
            logger.error("更新资费检查结果时发生错误", e);
            return EasyResult.fail("更新资费检查结果失败：" + e.getMessage());
        }
    }

    /**
     * 查询检查任务运行状态
     * 
     * 请求示例：
     * /servlet/tariffRule/getCheckStatus
     * 
     * 响应示例：
     * {
     *   "code": 0,
     *   "msg": "获取检查任务状态成功",
     *   "data": {
     *     "isRunning": true,
     *     "runningInfo": {
     *       "startTime": "2024-07-10 15:30:45",
     *       "startDate": "20240701",
     *       "endDate": "20240701",
     *       "isAll": true,
     *       "tariffId": "12345",
     *       "ruleNo": "1"
     *     }
     *   }
     * }
     */
    public JSONObject actionForGetCheckStatus() {
        try {
            UserModel user = UserUtil.getUser(getRequest());
            
            // 获取请求参数
            String checkType = getPara("checkType");
            int checkTypeInt = TariffCheckService.CHECK_TYPE_REPORT; // 默认为报送库
            
            if (StringUtils.isNotBlank(checkType)) {
                try {
                    checkTypeInt = Integer.parseInt(checkType);
                } catch (NumberFormatException e) {
                    logger.warn("无效的检查类型参数: {}, 使用默认值: {}", checkType, checkTypeInt);
                }
            }
            
            logger.info("查询检查任务运行状态，检查类型: {}, 操作人: {}", 
                    TariffCheckService.getCheckTypeName(checkTypeInt), user.getUserName());
            
            // 根据检查类型检查是否有任务正在运行
            boolean isRunning = false;
            String cacheKey = null;
            
            switch (checkTypeInt) {
                case TariffCheckService.CHECK_TYPE_REPORT:
                    isRunning = TariffCheckService.isReportCheckRunning();
                    cacheKey = TariffCheckService.REPORT_CHECK_RUNNING_KEY;
                    break;
                case TariffCheckService.CHECK_TYPE_PUBLIC:
                    isRunning = TariffCheckService.isPublicCheckRunning();
                    cacheKey = TariffCheckService.PUBLIC_CHECK_RUNNING_KEY;
                    break;
                case TariffCheckService.CHECK_TYPE_SEQUENTIAL:
                    isRunning = TariffCheckService.isSequentialCheckRunning();
                    cacheKey = TariffCheckService.SEQUENTIAL_CHECK_KEY;
                    break;
                default:
                    // 检查所有类型
                    isRunning = TariffCheckService.isAnyCheckRunning();
                    cacheKey = null;
            }
            
            // 返回结果
            JSONObject result = new JSONObject();
            result.put("isRunning", isRunning);
            result.put("checkType", checkTypeInt);
            result.put("checkTypeName", TariffCheckService.getCheckTypeName(checkTypeInt));
            
            if (isRunning && cacheKey != null) {
                // 如果有任务正在运行，尝试获取运行信息
                String runningInfoStr = RedissonUtil.get(cacheKey);
                if (StringUtils.isNotBlank(runningInfoStr)) {
                    try {
                        JSONObject runningInfo = JSONObject.parseObject(runningInfoStr);
                        result.put("runningInfo", runningInfo);
                    } catch (Exception e) {
                        logger.error("解析运行信息失败: {}", e.getMessage(), e);
                    }
                }
            } else if (isRunning && cacheKey == null) {
                // 查询所有类型的运行状态
                JSONObject allRunningInfo = new JSONObject();
                
                // 检查报送库任务
                if (TariffCheckService.isReportCheckRunning()) {
                    String reportInfoStr = RedissonUtil.get(TariffCheckService.REPORT_CHECK_RUNNING_KEY);
                    if (StringUtils.isNotBlank(reportInfoStr)) {
                        try {
                            allRunningInfo.put("reportCheck", JSONObject.parseObject(reportInfoStr));
                        } catch (Exception e) {
                            logger.error("解析报送库任务运行信息失败: {}", e.getMessage(), e);
                        }
                    }
                }
                
                // 检查公示库任务
                if (TariffCheckService.isPublicCheckRunning()) {
                    String publicInfoStr = RedissonUtil.get(TariffCheckService.PUBLIC_CHECK_RUNNING_KEY);
                    if (StringUtils.isNotBlank(publicInfoStr)) {
                        try {
                            allRunningInfo.put("publicCheck", JSONObject.parseObject(publicInfoStr));
                        } catch (Exception e) {
                            logger.error("解析公示库任务运行信息失败: {}", e.getMessage(), e);
                        }
                    }
                }
                
                // 检查顺序检查任务
                if (TariffCheckService.isSequentialCheckRunning()) {
                    String seqInfoStr = RedissonUtil.get(TariffCheckService.SEQUENTIAL_CHECK_KEY);
                    if (StringUtils.isNotBlank(seqInfoStr)) {
                        try {
                            allRunningInfo.put("sequentialCheck", JSONObject.parseObject(seqInfoStr));
                        } catch (Exception e) {
                            logger.error("解析顺序检查任务运行信息失败: {}", e.getMessage(), e);
                        }
                    }
                }
                
                // 检查旧的缓存键
                if (TariffCheckService.isLegacyCheckRunning()) {
                    String legacyInfoStr = RedissonUtil.get(TariffCheckService.CHECK_RUNNING_KEY);
                    if (StringUtils.isNotBlank(legacyInfoStr)) {
                        try {
                            allRunningInfo.put("legacyCheck", JSONObject.parseObject(legacyInfoStr));
                        } catch (Exception e) {
                            logger.error("解析旧缓存任务运行信息失败: {}", e.getMessage(), e);
                        }
                    }
                }
                
                result.put("allRunningInfo", allRunningInfo);
            }
            
            logger.info("查询检查任务运行状态完成，检查类型: {}, 状态: {}", 
                    TariffCheckService.getCheckTypeName(checkTypeInt), 
                    isRunning ? "运行中" : "未运行");
            return EasyResult.ok(result, "获取检查任务状态成功");
        } catch (Exception e) {
            logger.error("查询检查任务运行状态时发生错误", e);
            return EasyResult.fail("查询检查任务运行状态失败：" + e.getMessage());
        }
    }

    /**
     * 强制清除检查任务运行状态
     * 
     * 请求示例：
     * /servlet/tariffRule/clearCheckStatus?checkType=1
     * 
     * 响应示例：
     * {
     *   "code": 0,
     *   "msg": "清除检查任务运行状态成功",
     *   "data": {
     *     "clearedAt": "2024-07-10 15:30:45",
     *     "checkType": 1,
     *     "checkTypeName": "报送库"
     *   }
     * }
     */
    public JSONObject actionForClearCheckStatus() {
        try {
            UserModel user = UserUtil.getUser(getRequest());
            
            // 获取请求参数
            String checkType = getPara("checkType");
            int checkTypeInt = 0; // 默认清除所有类型
            
            if (StringUtils.isNotBlank(checkType)) {
                try {
                    checkTypeInt = Integer.parseInt(checkType);
                } catch (NumberFormatException e) {
                    logger.warn("无效的检查类型参数: {}, 将清除所有类型的检查状态", checkType);
                    checkTypeInt = 0;
                }
            }
            
            logger.info("开始强制清除检查任务运行状态，检查类型: {}, 操作人: {}", 
                    checkTypeInt == 0 ? "所有类型" : TariffCheckService.getCheckTypeName(checkTypeInt), 
                    user.getUserName());
            
            // 根据检查类型清除运行状态
            if (checkTypeInt == 0) {
                // 清除所有类型的运行状态
                TariffCheckService.clearAllCheckRunning();
            // 清除所有规则缓存
            TariffCheckService.clearRuleCache();
            } else {
                // 清除指定类型的运行状态
                TariffCheckService.clearCheckRunning(checkTypeInt);
            }
            
            // 获取当前时间
            String clearedAt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            
            // 返回结果
            JSONObject result = new JSONObject();
            result.put("clearedAt", clearedAt);
            result.put("checkType", checkTypeInt);
            result.put("checkTypeName", checkTypeInt == 0 ? "所有类型" : TariffCheckService.getCheckTypeName(checkTypeInt));
            
            logger.info("强制清除检查任务运行状态完成，检查类型: {}, 操作人: {}, 时间: {}", 
                    checkTypeInt == 0 ? "所有类型" : TariffCheckService.getCheckTypeName(checkTypeInt), 
                    user.getUserName(), clearedAt);
            return EasyResult.ok(result, "清除检查任务运行状态成功");
        } catch (Exception e) {
            logger.error("强制清除检查任务运行状态时发生错误", e);
            return EasyResult.fail("清除检查任务运行状态失败：" + e.getMessage());
        }
    }

    /**
     * 根据省份和企业编码重新核查资费字段
     * 
     * 请求参数：
     * provinceCode: 省份编码数组，可包含特殊代码JT表示集团
     * entType: 企业编码数组
     * ruleNo: 规则编号，可选，如果不为空，则只检查指定规则
     * isAll: 是否全量检查，可选，默认为true
     * checkType: 检查对象，1-报送库，2-公示库，默认为1
     * 
     * 请求示例：
     * /servlet/tariffRule/recheckTariffByProvinceAndEnt
     * {
     *   "provinceCode": ["11", "12", "JT"],
     *   "entType": ["1", "2"],
     *   "ruleNo": "1,2,3",
     *   "isAll": true,
     *   "checkType": 1
     * }
     */
    public JSONObject actionForRecheckTariffByProvinceAndEnt() {
        try {
            UserModel user = UserUtil.getUser(getRequest());
            JSONObject jsonParam = getJSONObject();
            
            // 获取省份编码数组
            List<String> provinceCodes = null;
            boolean hasJTCode = false;
            if (jsonParam.containsKey("provinceCode")) {
                provinceCodes = jsonParam.getJSONArray("provinceCode").toJavaList(String.class);
                
                // 检查是否包含JT特殊代码
                if (provinceCodes != null && !provinceCodes.isEmpty()) {
                    Iterator<String> iterator = provinceCodes.iterator();
                    while (iterator.hasNext()) {
                        String code = iterator.next();
                        if ("JT".equals(code)) {
                            hasJTCode = true;
                            iterator.remove();
                        }
                    }
                }
                
                // 如果只有JT代码，清空省份代码列表
                if (hasJTCode && (provinceCodes == null || provinceCodes.isEmpty())) {
                    provinceCodes = null;
                    logger.info("仅有JT代码，清空省份代码列表，将在服务层特殊处理");
                }
            }
            
            // 获取企业编码数组
            List<String> entTypes = null;
            if (jsonParam.containsKey("entType")) {
                entTypes = jsonParam.getJSONArray("entType").toJavaList(String.class);
            }
            // 新增：如果entTypes为空，自动赋值为["1","2","3","5"]
            if (entTypes == null || entTypes.isEmpty()) {
                entTypes = Arrays.asList("1", "2", "3", "5");
            }
            
            // 获取规则编号和是否全量检查参数
            String ruleNo = jsonParam.getString("ruleNo");
            Boolean isAll = jsonParam.getBoolean("isAll");
            if (isAll == null) {
                isAll = true; // 默认为全量检查
            }
            // 新增：如果ruleNo为空，自动赋值为指定字符串
//            if (StringUtils.isBlank(ruleNo)) {
                ruleNo = "1,2,3,5,4,6,7,8,9,10,11,12,13,15,14,16";
            //}
            
            // 获取检查对象参数
            Integer checkType = jsonParam.getInteger("checkType");
            if (checkType == null) {
                checkType = 1; // 默认为报送库
            }
            
            // 检查是否有必要的参数
            if ((provinceCodes == null || provinceCodes.isEmpty()) && !hasJTCode && 
                (entTypes == null || entTypes.isEmpty())) {
                logger.warn("省份编码和企业编码均为空，无法进行重新核查");
                return EasyResult.fail("请至少提供省份编码或企业编码");
            }

            // 根据检查对象类型检查是否有任务正在运行
            boolean isRunning = false;
            if (checkType == 1) {
                // 检查报送库任务是否运行
                isRunning = TariffCheckService.isReportCheckRunning();
                if (isRunning) {
                    logger.warn("已有报送库检查任务正在运行，无法启动新的检查任务");
                    return EasyResult.fail("已有报送库检查任务正在运行，请稍后再试");
                }
            } else if (checkType == 2) {
                // 检查公示库任务是否运行
                isRunning = TariffCheckService.isPublicCheckRunning();
                if (isRunning) {
                    logger.warn("已有公示库检查任务正在运行，无法启动新的检查任务");
                    return EasyResult.fail("已有公示库检查任务正在运行，请稍后再试");
                }
            } else {
                // 检查所有任务是否运行
                isRunning = TariffCheckService.isAnyCheckRunning();
            if (isRunning) {
                logger.warn("已有检查任务正在运行，无法启动新的检查任务");
                return EasyResult.fail("已有检查任务正在运行，请稍后再试");
                }
            }
            
            String checkTypeName = checkType == 1 ? "报送库" : (checkType == 2 ? "公示库" : "未知");
            
            logger.info("开始重新核查{}资费，省份编码: {}, 是否包含JT代码: {}, 企业编码: {}, 规则编号: {}, 是否全量: {}, 操作人: {}", 
                    checkTypeName, provinceCodes, hasJTCode, entTypes, 
                    StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo, 
                    isAll, user.getUserName());
            
            // 创建任务记录ID
            String taskId = IdUtil.getSnowflakeNextIdStr();

            // 创建一个新线程来执行检查任务，避免阻塞当前请求
            List<String> finalProvinceCodes = provinceCodes;
            List<String> finalEntTypes = entTypes;
            Boolean finalIsAll = isAll;
            String finalRuleNo = ruleNo;
            boolean finalHasJTCode = hasJTCode;
            Integer finalCheckType = checkType;
            checkTaskExecutor.submit(() -> {
                try {
                    // 创建检查服务实例
                    TariffCheckService checkService = new TariffCheckService();
                    
                    if (finalCheckType == 1) {
                        // 执行报送库资费核查
                        logger.info("开始执行报送库资费核查，任务ID: {}", taskId);
                        checkService.checkTariffFieldsByProvinceAndEnt(null, null, finalIsAll, null, finalRuleNo, finalProvinceCodes, finalEntTypes, finalHasJTCode);
                    } else if (finalCheckType == 2) {
                        // 执行公示库资费核查
                        logger.info("开始执行公示库资费核查，任务ID: {}", taskId);
                        checkService.checkPublicLibTariffFieldsByProvinceAndEnt(null, null, finalIsAll, null, finalRuleNo, finalProvinceCodes, finalEntTypes
                                , finalHasJTCode, jsonParam.getString("versionNo"));
                    }
                    
                    // 检查完成后清除运行状态
                    TariffCheckService.clearCheckRunning(finalCheckType == 1 ? TariffCheckService.CHECK_TYPE_REPORT : TariffCheckService.CHECK_TYPE_PUBLIC);
                    logger.info("{}资费重新核查任务已完成，任务ID: {}", finalCheckType == 1 ? "报送库" : "公示库", taskId);
                } catch (Exception e) {
                    // 发生异常时，清除所有运行状态
                    TariffCheckService.clearCheckRunning(finalCheckType == 1 ? TariffCheckService.CHECK_TYPE_REPORT : TariffCheckService.CHECK_TYPE_PUBLIC);
                    logger.error("执行资费重新核查任务时发生错误: {}", e.getMessage(), e);
                }
            });
            
            // 返回结果
            JSONObject result = new JSONObject();
            result.put("taskId", taskId);
            result.put("message", checkTypeName + "资费重新核查任务已提交，正在后台执行");
            result.put("provinceCodes", provinceCodes);
            result.put("hasJTCode", hasJTCode);
            result.put("entTypes", entTypes);
            result.put("ruleNo", StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo);
            result.put("isAll", isAll);
            result.put("checkType", checkType);
            result.put("checkTypeName", checkTypeName);
            result.put("operator", user.getUserName());
            
            return EasyResult.ok(result, checkTypeName + "资费重新核查任务已提交，正在后台执行");
        } catch (Exception e) {
            logger.error("提交资费重新核查任务时发生错误", e);
            return EasyResult.fail("提交资费重新核查任务失败：" + e.getMessage());
        }
    }

    /**
     * 同步爬虫数据到公示库
     * <p>
     * /servlet/tariffRule/syncCrawlDataToPublic
     * </p>
     *
     * @return 处理结果
     */
    public JSONObject actionForSyncCrawlDataToPublic() {
        try {
            TariffCrawlCheckService service2 = new TariffCrawlCheckService();

            service2.clearCheckRunning();
            // 创建一个新线程来执行同步任务
            checkTaskExecutor.submit(() -> {
                try {
                    TariffCrawlCheckService service = new TariffCrawlCheckService();
                    service.syncCrawlDataToPublic();
                } catch (Exception e) {
                    logger.error("同步爬虫数据到公示库失败: {}", e.getMessage(), e);
                }
            });

            logger.info("成功提交同步爬虫数据到公示库的任务");
            return EasyResult.ok("成功提交同步任务，系统正在后台处理");
        } catch (Exception e) {
            logger.error("提交同步爬虫数据到公示库任务时发生异常: {}", e.getMessage(), e);
            return EasyResult.error(500, "提交同步任务失败: " + e.getMessage());
        }
    }

    /**
     * 异步执行报送库资费字段检查
     * 
     * 请求参数：
     * startDate: 开始日期，格式yyyyMMdd，可选，默认为昨天
     * endDate: 结束日期，格式yyyyMMdd，可选，默认为昨天
     * isAll: 是否全量检查，可选，默认为false
     * ruleNo: 规则编号，可选，如果不为空，则只检查指定规则
     * tariffId: 资费ID，可选，如果不为空，则只检查指定ID的资费记录
     * 
     * 请求示例：
     * /servlet/tariffRule/asyncCheckFields?startDate=20240701&endDate=20240701&isAll=true&ruleNo=1
     */
    public JSONObject actionForAsyncCheckFields() {
        try {
            UserModel user = UserUtil.getUser(getRequest());

            // 获取请求参数
            String startDate = getPara("startDate");
            String endDate = getPara("endDate");
            String isAllStr = getPara("isAll");
            String ruleNo = getPara("ruleNo");
            String tariffId = getPara("tariffId");

            // 处理isAll参数
            Boolean isAll = null;
            if (StringUtils.isNotBlank(isAllStr)) {
                isAll = Boolean.parseBoolean(isAllStr);
            }

            // 检查是否有任务正在运行
            boolean isRunning = TariffCheckService.isCheckRunning();
            if (isRunning) {
                logger.warn("已有检查任务正在运行，无法启动新的检查任务");
                return EasyResult.fail("已有检查任务正在运行，请稍后再试");
            }

            // 记录操作日志
            String logMessage = String.format("异步执行报送库资费字段检查，开始日期：%s，结束日期：%s，是否全量：%s，规则编号：%s，资费ID：%s",
                    StringUtils.isBlank(startDate) ? "默认(昨天)" : startDate,
                    StringUtils.isBlank(endDate) ? "默认(昨天)" : endDate,
                    isAll == null ? "默认(false)" : isAll,
                    StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                    StringUtils.isBlank(tariffId) ? "全部资费" : tariffId);
            logger.info(logMessage);

            // 创建任务记录ID
            String taskId = IdUtil.getSnowflakeNextIdStr();
            
            // 获取当前请求参数的副本，用于异步任务
            final String finalStartDate = startDate;
            final String finalEndDate = endDate;
            final Boolean finalIsAll = isAll;
            final String finalRuleNo = ruleNo;
            final String finalTariffId = tariffId;
            
            // 在线程池中异步执行报送库资费字段检查
            checkTaskExecutor.submit(() -> {
                try {
                    logger.info("开始异步执行报送库资费字段检查，任务ID: {}", taskId);
                    TariffCheckService checkService = new TariffCheckService();
                    checkService.checkTariffFields(finalStartDate, finalEndDate, finalIsAll, finalTariffId, finalRuleNo, TariffCheckService.CHECK_TYPE_REPORT);
                    logger.info("异步报送库资费字段检查任务完成，任务ID: {}", taskId);
                } catch (Exception e) {
                    logger.error("异步执行报送库资费字段检查时发生错误，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
                }
            });

            // 返回成功结果
            JSONObject result = new JSONObject();
            result.put("taskId", taskId);
            result.put("message", "异步报送库资费字段检查任务已提交");
            result.put("startDate", StringUtils.isBlank(startDate) ? "默认(昨天)" : startDate);
            result.put("endDate", StringUtils.isBlank(endDate) ? "默认(昨天)" : endDate);
            result.put("isAll", isAll == null ? false : isAll);
            result.put("ruleNo", StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo);
            result.put("tariffId", StringUtils.isBlank(tariffId) ? "全部资费" : tariffId);
            result.put("checkType", "报送库");
            result.put("asyncMode", true);

            return EasyResult.ok(result, "异步报送库资费字段检查任务已提交，系统正在后台处理");
                } catch (Exception e) {
            logger.error("提交异步报送库资费字段检查任务时发生错误", e);
            return EasyResult.fail("提交异步报送库资费字段检查任务失败：" + e.getMessage());
        }
    }

    /**
     * 依次执行报送库和公示库资费字段检查
     * 
     * 请求参数：
     * startDate: 开始日期，格式yyyyMMdd，可选，默认为昨天
     * endDate: 结束日期，格式yyyyMMdd，可选，默认为昨天
     * isAll: 是否全量检查，可选，默认为false
     * ruleNo: 规则编号，可选，如果不为空，则只检查指定规则
     * tariffId: 资费ID，可选，如果不为空，则只检查指定ID的资费记录
     * 
     * 请求示例：
     * /servlet/tariffRule/checkAllFields?startDate=20240701&endDate=20240701&isAll=true&ruleNo=1
     */
    public JSONObject actionForCheckAllFields() {
        try {
            UserModel user = UserUtil.getUser(getRequest());

            // 获取请求参数
            String startDate = getPara("startDate");
            String endDate = getPara("endDate");
            String isAllStr = getPara("isAll");
            String ruleNo = getPara("ruleNo");
            String tariffId = getPara("tariffId");

            // 处理isAll参数
            Boolean isAll = null;
            if (StringUtils.isNotBlank(isAllStr)) {
                isAll = Boolean.parseBoolean(isAllStr);
            }

            // 检查是否有任务正在运行
            boolean isRunning = TariffCheckService.isCheckRunning();
            boolean isSequentialRunning = TariffCheckService.isSequentialCheckRunning();
            
            if (isRunning && !isSequentialRunning) {
                logger.warn("已有检查任务正在运行，无法启动新的检查任务");
                return EasyResult.fail("已有检查任务正在运行，请稍后再试");
            }

            // 记录操作日志
            String logMessage = String.format("执行报送库和公示库资费字段检查，开始日期：%s，结束日期：%s，是否全量：%s，规则编号：%s，资费ID：%s",
                    StringUtils.isBlank(startDate) ? "默认(昨天)" : startDate,
                    StringUtils.isBlank(endDate) ? "默认(昨天)" : endDate,
                    isAll == null ? "默认(false)" : isAll,
                    StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                    StringUtils.isBlank(tariffId) ? "全部资费" : tariffId);
            logger.info(logMessage);

            // 创建任务记录ID
            String taskId = IdUtil.getSnowflakeNextIdStr();

            // 执行资费字段检查（依次执行报送库和公示库）
            TariffCheckService checkService = new TariffCheckService();
            checkService.checkAllTariffFields(startDate, endDate, isAll, tariffId, ruleNo);

            // 返回成功结果
            JSONObject result = new JSONObject();
            result.put("taskId", taskId);
            result.put("message", "报送库和公示库资费字段检查任务已提交");
            result.put("startDate", StringUtils.isBlank(startDate) ? "默认(昨天)" : startDate);
            result.put("endDate", StringUtils.isBlank(endDate) ? "默认(昨天)" : endDate);
            result.put("isAll", isAll == null ? false : isAll);
            result.put("ruleNo", StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo);
            result.put("tariffId", StringUtils.isBlank(tariffId) ? "全部资费" : tariffId);
            result.put("checkType", "报送库和公示库");

            return EasyResult.ok(result, "报送库和公示库资费字段检查任务已提交");
        } catch (Exception e) {
            logger.error("执行报送库和公示库资费字段检查时发生错误", e);
            return EasyResult.fail("执行报送库和公示库资费字段检查失败：" + e.getMessage());
        }
    }

    /**
     * 异步依次执行报送库和公示库资费字段检查
     * 
     * 请求参数：
     * startDate: 开始日期，格式yyyyMMdd，可选，默认为昨天
     * endDate: 结束日期，格式yyyyMMdd，可选，默认为昨天
     * isAll: 是否全量检查，可选，默认为false
     * ruleNo: 规则编号，可选，如果不为空，则只检查指定规则
     * tariffId: 资费ID，可选，如果不为空，则只检查指定ID的资费记录
     * 
     * 请求示例：
     * /servlet/tariffRule/asyncCheckAllFields?startDate=20240701&endDate=20240701&isAll=true&ruleNo=1
     */
    public JSONObject actionForAsyncCheckAllFields() {
        try {
            UserModel user = UserUtil.getUser(getRequest());

            // 获取请求参数
            String startDate = getPara("startDate");
            String endDate = getPara("endDate");
            String isAllStr = getPara("isAll");
            String ruleNo = getPara("ruleNo");
            String tariffId = getPara("tariffId");

            // 处理isAll参数
            Boolean isAll = null;
            if (StringUtils.isNotBlank(isAllStr)) {
                isAll = Boolean.parseBoolean(isAllStr);
            }

            // 检查是否有任务正在运行
            boolean isRunning = TariffCheckService.isCheckRunning();
            boolean isSequentialRunning = TariffCheckService.isSequentialCheckRunning();
            
            if (isRunning && !isSequentialRunning) {
                logger.warn("已有检查任务正在运行，无法启动新的检查任务");
                return EasyResult.fail("已有检查任务正在运行，请稍后再试");
            }

            // 记录操作日志
            String logMessage = String.format("异步执行报送库和公示库资费字段检查，开始日期：%s，结束日期：%s，是否全量：%s，规则编号：%s，资费ID：%s",
                    StringUtils.isBlank(startDate) ? "默认(昨天)" : startDate,
                    StringUtils.isBlank(endDate) ? "默认(昨天)" : endDate,
                    isAll == null ? "默认(false)" : isAll,
                    StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo,
                    StringUtils.isBlank(tariffId) ? "全部资费" : tariffId);
            logger.info(logMessage);

            // 创建任务记录ID
            String taskId = IdUtil.getSnowflakeNextIdStr();
            
            // 获取当前请求参数的副本，用于异步任务
            final String finalStartDate = startDate;
            final String finalEndDate = endDate;
            final Boolean finalIsAll = isAll;
            final String finalRuleNo = ruleNo;
            final String finalTariffId = tariffId;
            
            // 在线程池中异步执行资费字段检查（依次执行报送库和公示库）
            checkTaskExecutor.submit(() -> {
                try {
                    logger.info("开始异步执行报送库和公示库资费字段检查，任务ID: {}", taskId);
                    TariffCheckService checkService = new TariffCheckService();
                    checkService.checkAllTariffFields(finalStartDate, finalEndDate, finalIsAll, finalTariffId, finalRuleNo);
                    logger.info("异步报送库和公示库资费字段检查任务完成，任务ID: {}", taskId);
                } catch (Exception e) {
                    logger.error("异步执行报送库和公示库资费字段检查时发生错误，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
                }
            });

            // 返回成功结果
            JSONObject result = new JSONObject();
            result.put("taskId", taskId);
            result.put("message", "异步报送库和公示库资费字段检查任务已提交");
            result.put("startDate", StringUtils.isBlank(startDate) ? "默认(昨天)" : startDate);
            result.put("endDate", StringUtils.isBlank(endDate) ? "默认(昨天)" : endDate);
            result.put("isAll", isAll == null ? false : isAll);
            result.put("ruleNo", StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo);
            result.put("tariffId", StringUtils.isBlank(tariffId) ? "全部资费" : tariffId);
            result.put("checkType", "报送库和公示库");
            result.put("asyncMode", true);

            return EasyResult.ok(result, "异步报送库和公示库资费字段检查任务已提交，系统正在后台处理");
                } catch (Exception e) {
            logger.error("提交异步报送库和公示库资费字段检查任务时发生错误", e);
            return EasyResult.fail("提交异步报送库和公示库资费字段检查任务失败：" + e.getMessage());
        }
    }

    /**
     * 更新公示库资费检查结果
     * 
     * 请求参数：
     * tariffId: 公示库资费ID，必填
     * ruleNos: 规则编号，多个编号用逗号分隔，例如"8,10"，可以为空字符串，表示清除检查结果
     * 
     * 请求示例：
     * /servlet/tariffRule/updatePublicLibCheckResult
     * 请求体：
     * {
     *   "tariffId": "12345",
     *   "ruleNos": "8,10"
     * }
     * 
     * 响应示例：
     * {
     *   "code": 0,
     *   "msg": "更新公示库资费检查结果成功",
     *   "data": {
     *     "tariffId": "12345",
     *     "ruleNos": "8,10",
     *     "checkResult": "【退订方式填写不规范】，【下线时间填写不规范】",
     *     "checkTime": "2024-07-10 15:30:45"
     *   }
     * }
     */
    public JSONObject actionForUpdatePublicLibCheckResult() {
        try {
            UserModel user = UserUtil.getUser(getRequest());
            JSONObject jsonparam = this.getJSONObject();

            // 获取参数
            String tariffId = jsonparam.getString("id");
            String ruleNos = jsonparam.getString("ruleNos");

            if (StringUtils.isBlank(tariffId)) {
                logger.warn("公示库资费ID为空，无法更新检查结果");
                return EasyResult.fail("公示库资费ID不能为空");
            }
            
            logger.info("开始更新公示库资费检查结果，资费ID: {}, 规则编号: {}, 操作人: {}", 
                    tariffId, StringUtils.isBlank(ruleNos) ? "空" : ruleNos, user.getUserName());
            
            // 获取当前时间
            String checkTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            
            String checkResult = "";
            
            // 解析规则编号字符串为列表，用于存储到 field_check_nos 字段
            List<String> ruleNosList = new ArrayList<>();
            
            // 如果ruleNos不为空，则查询对应的规则描述
            if (StringUtils.isNotBlank(ruleNos)) {
                // 查询规则描述
                String[] ruleNoArray = ruleNos.split(",");
                List<String> ruleDescList = new ArrayList<>();
                
                // 将规则编号添加到列表
                for (String ruleNo : ruleNoArray) {
                    if (StringUtils.isNotBlank(ruleNo)) {
                        ruleNosList.add(ruleNo.trim());
                    }
                }
                
                // 构建SQL查询条件
                StringBuilder sqlBuilder = new StringBuilder();
                sqlBuilder.append("SELECT RULE_NO, RULE_DESC FROM ").append(getTableName("XTY_TARIFF_CHECK_RULE"));
                sqlBuilder.append(" WHERE RULE_NO IN (");
                
                for (int i = 0; i < ruleNoArray.length; i++) {
                    if (i > 0) {
                        sqlBuilder.append(",");
                    }
                    sqlBuilder.append("?");
                }
                sqlBuilder.append(") ORDER BY ID ASC");

                // 准备参数
                Object[] params = new Object[ruleNoArray.length];
                for (int i = 0; i < ruleNoArray.length; i++) {
                    params[i] = ruleNoArray[i].trim();
                }

                logger.info("查询规则描述，SQL: {}, 参数: {}", sqlBuilder.toString(), Arrays.toString(params));

                // 执行查询
                EasyQuery query = getQuery();
                List<JSONObject> ruleList = query.queryForList(sqlBuilder.toString(), params, new JSONMapperImpl());
                
                // 构建检查结果
                for (JSONObject rule : ruleList) {
                    String ruleDesc = rule.getString("RULE_DESC");
                    if (StringUtils.isNotBlank(ruleDesc)) {
                        ruleDescList.add("【" + ruleDesc + "】");
                    }
                }
                
                checkResult = String.join("，", ruleDescList);
                logger.info("构建检查结果，公示库资费ID: {}, 检查结果: {}", tariffId, checkResult);
            } else {
                // 如果ruleNos为空，则将检查结果和规则编号设置为空字符串
                ruleNos = "";
                checkResult = "";
                logger.info("规则编号为空，将清除公示库资费ID: {} 的检查结果", tariffId);
            }
            
            // 1. 更新数据库中的公示库资费检查结果
            String updateSql = "UPDATE " + Constants.getBusiSchema() + ".xty_tariff_crawl_record_lib" +
                               " SET FIELD_CHECK_RESULT = ?, FIELD_CHECK_NO = ?, FIELD_CHECK_TIME = ? WHERE ID = ?";
            
            int rows = QueryFactory.getTariffQuery().executeUpdate(updateSql, new Object[]{checkResult, ruleNos, checkTime, tariffId});
            
            if (rows == 0) {
                logger.warn("未找到公示库资费记录，ID: {}", tariffId);
                return EasyResult.fail("未找到公示库资费记录，ID: " + tariffId);
            }
            
            // 2. 更新ES索引中的检查结果
            try {
                // 构建需要更新的ES文档
                JSONObject updateDoc = new JSONObject();
                updateDoc.put("id", tariffId);
                updateDoc.put("field_check_result", checkResult);
                updateDoc.put("field_check_no", ruleNos);
                updateDoc.put("field_check_time", checkTime);
                // 添加新字段 field_check_nos，存储规则编号数组
                updateDoc.put("field_check_nos", ruleNosList);
                
                // 调用服务更新ES索引
                TariffCrawlCheckService checkService = new TariffCrawlCheckService();
                checkService.updateDocumentInEs(tariffId, updateDoc);
                
                logger.info("更新ES索引中的公示库资费检查结果成功，资费ID: {}", tariffId);
            } catch (Exception e) {
                logger.error("更新ES索引中的公示库资费检查结果失败: {}", e.getMessage(), e);
                // 不中断执行流程，仅记录错误日志，因为数据库已更新成功
                // 返回部分成功的消息
                JSONObject result = new JSONObject();
                result.put("tariffId", tariffId);
                result.put("ruleNos", ruleNos);
                result.put("checkResult", checkResult);
                result.put("checkTime", checkTime);
                result.put("dbUpdateSuccess", true);
                result.put("esUpdateSuccess", false);
                
                return EasyResult.ok(result, "更新公示库资费检查结果在数据库中成功，但在ES索引中失败：" + e.getMessage());
            }
            
            logger.info("更新公示库资费检查结果成功，资费ID: {}, 规则编号: {}, 检查结果: {}, 检查时间: {}", 
                    tariffId, ruleNos, checkResult, checkTime);
            
            // 返回成功结果
            JSONObject result = new JSONObject();
            result.put("tariffId", tariffId);
            result.put("ruleNos", ruleNos);
            result.put("checkResult", checkResult);
            result.put("checkTime", checkTime);
            result.put("dbUpdateSuccess", true);
            result.put("esUpdateSuccess", true);
            
            return EasyResult.ok(result, "更新公示库资费检查结果成功");
        } catch (Exception e) {
            logger.error("更新公示库资费检查结果时发生错误: {}", e.getMessage(), e);
            return EasyResult.fail("更新公示库资费检查结果失败：" + e.getMessage());
        }
    }

    /**
     * 根据省份和企业编码重新核查公示库资费记录
     * 
     * 请求参数：
     * provinceCode: 省份编码数组，可包含特殊代码JT表示集团
     * entType: 企业编码数组
     * ruleNo: 规则编号，可选，如果不为空，则只检查指定规则
     * isAll: 是否全量检查，可选，默认为true
     * publicLibId: 公示库资费ID，可选，如果不为空，则只检查指定ID的记录
     * versionNo: 版本号，可选，如果不为空，则只检查匹配该版本号的记录
     * 
     * 请求示例：
     * /servlet/tariffRule/recheckPublicLibTariffByProvinceAndEnt
     * {
     *   "provinceCode": ["11", "12"],
     *   "entType": ["1", "2"],
     *   "ruleNo": "1,2,3",
     *   "isAll": true,
     *   "publicLibId": "1234567890",
     *   "versionNo": "v1.0"
     * }
     */
    public JSONObject actionForRecheckPublicLibTariffByProvinceAndEnt() {
        try {
            UserModel user = UserUtil.getUser(getRequest());
            JSONObject jsonParam = getJSONObject();
            
            // 获取公示库ID参数
            String publicLibId = jsonParam.getString("publicLibId");
            
            // 如果指定了公示库ID，则其他参数可以为空
            boolean hasPublicLibId = StringUtils.isNotBlank(publicLibId);
            
            // 获取版本号参数
            String versionNo = jsonParam.getString("versionNo");
            boolean hasVersionNo = StringUtils.isNotBlank(versionNo);
            
            // 获取省份编码数组
            List<String> provinceCodes = null;
            boolean hasJTCode = false;
            if (jsonParam.containsKey("provinceCode")) {
                provinceCodes = jsonParam.getJSONArray("provinceCode").toJavaList(String.class);
                
                // 检查是否包含JT特殊代码
                if (provinceCodes != null && !provinceCodes.isEmpty()) {
                    Iterator<String> iterator = provinceCodes.iterator();
                    while (iterator.hasNext()) {
                        String code = iterator.next();
                        if ("JT".equals(code)) {
                            hasJTCode = true;
                            iterator.remove();
                        }
                    }
                }
                
                // 如果只有JT代码，清空省份代码列表
                if (hasJTCode && (provinceCodes == null || provinceCodes.isEmpty())) {
                    provinceCodes = null;
                    logger.info("仅有JT代码，清空省份代码列表，将在服务层特殊处理");
                }
            }
            
            // 获取企业编码数组
            List<String> entTypes = null;
            if (jsonParam.containsKey("entType")) {
                entTypes = jsonParam.getJSONArray("entType").toJavaList(String.class);
            }
            // 新增：如果entTypes为空，自动赋值为["1","2","3","5"]
            if (entTypes == null || entTypes.isEmpty()) {
                entTypes = Arrays.asList("1", "2", "3", "5");
            }
            
            // 获取规则编号和是否全量检查参数
            String ruleNo = jsonParam.getString("ruleNo");
            Boolean isAll = jsonParam.getBoolean("isAll");
            if (isAll == null) {
                isAll = true; // 默认为全量检查
            }
            // 新增：如果ruleNo为空，自动赋值为指定字符串
            //if (StringUtils.isBlank(ruleNo)) {
                ruleNo = "1,2,3,5,4,6,7,8,9,10,11,12,13,15,14,16";
            //}
            
            // 检查是否有必要的参数
            if (!hasPublicLibId && (provinceCodes == null || provinceCodes.isEmpty()) && !hasJTCode && 
                (entTypes == null || entTypes.isEmpty()) && !hasVersionNo) {
                logger.warn("公示库ID、省份编码、企业编码和版本号均为空，无法进行重新核查");
                return EasyResult.fail("请提供公示库ID或至少提供省份编码、企业编码或版本号");
            }

            // 检查是否有公示库任务正在运行
            boolean isRunning = TariffCheckService.isPublicCheckRunning();
            if (isRunning) {
                logger.warn("已有公示库检查任务正在运行，无法启动新的检查任务");
                return EasyResult.fail("已有公示库检查任务正在运行，请稍后再试");
            }
            
            if (hasPublicLibId) {
                logger.info("开始重新核查指定公示库资费，公示库ID: {}, 规则编号: {}, 操作人: {}", 
                        publicLibId, StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo, user.getUserName());
            } else {
                logger.info("开始重新核查公示库资费，省份编码: {}, 企业编码: {}, 版本号: {}, 规则编号: {}, 是否全量: {}, 操作人: {}", 
                        provinceCodes, entTypes, versionNo, StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo, 
                        isAll, user.getUserName());
            }
            
            // 创建任务记录ID
            String taskId = IdUtil.getSnowflakeNextIdStr();
            
            // 创建运行信息
            JSONObject runningInfo = new JSONObject();
            runningInfo.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            runningInfo.put("provinceCodes", provinceCodes);
            runningInfo.put("hasJTCode", hasJTCode);
            runningInfo.put("entTypes", entTypes);
            runningInfo.put("versionNo", versionNo);
            runningInfo.put("ruleNo", ruleNo);
            runningInfo.put("isAll", isAll);
            runningInfo.put("publicLibId", publicLibId);
            runningInfo.put("taskId", taskId);
            runningInfo.put("operator", user.getUserName());
            
//            // 设置检查任务运行状态
//            TariffCheckService.setCheckRunning(runningInfo, TariffCheckService.CHECK_TYPE_PUBLIC);
            
            // 创建一个新线程来执行检查任务，避免阻塞当前请求
            List<String> finalProvinceCodes = provinceCodes;
            List<String> finalEntTypes = entTypes;
            Boolean finalIsAll = isAll;
            String finalRuleNo = ruleNo;
            boolean finalHasJTCode = hasJTCode;
            String finalPublicLibId = publicLibId;
            String finalVersionNo = versionNo;
            checkTaskExecutor.submit(() -> {
                try {
                    // 调用检查服务进行重新核查公示库资费
                    TariffCheckService checkService = new TariffCheckService();
                    
                    if (StringUtils.isNotBlank(finalPublicLibId)) {
                        // 如果指定了公示库ID，则只检查该ID的记录
                        checkService.checkPublicLibTariffFields(null, null, finalIsAll, finalPublicLibId, finalRuleNo);
                    } else {
                        // 否则根据省份、企业编码和版本号进行检查
                        checkService.checkPublicLibTariffFieldsByProvinceAndEnt(null, null, finalIsAll, null, finalRuleNo, finalProvinceCodes, finalEntTypes, finalHasJTCode, finalVersionNo);
                    }
                    
                    // 检查完成后清除运行状态
                    TariffCheckService.clearCheckRunning(TariffCheckService.CHECK_TYPE_PUBLIC);
                    logger.info("公示库资费重新核查任务已完成，任务ID: {}", taskId);
                } catch (Exception e) {
                    // 发生异常时，确保清除运行状态
                    TariffCheckService.clearCheckRunning(TariffCheckService.CHECK_TYPE_PUBLIC);
                    logger.error("异步执行公示库资费重新核查任务失败: {}", e.getMessage(), e);
                }
            });
            
            // 获取当前时间作为检查时间
            String checkTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            
            // 返回成功结果
            JSONObject result = new JSONObject();
            result.put("taskId", taskId);
            
            if (hasPublicLibId) {
                result.put("publicLibId", publicLibId);
            } else {
                result.put("provinceCodes", provinceCodes);
                if (hasJTCode) {
                    result.put("hasJTCode", true);
                }
                result.put("entTypes", entTypes);
                if (hasVersionNo) {
                    result.put("versionNo", versionNo);
                }
                result.put("isAll", isAll);
            }
            
            result.put("ruleNo", StringUtils.isBlank(ruleNo) ? "全部规则" : ruleNo);
            result.put("checkTime", checkTime);
            result.put("checkType", TariffCheckService.CHECK_TYPE_PUBLIC);
            result.put("checkTypeName", TariffCheckService.getCheckTypeName(TariffCheckService.CHECK_TYPE_PUBLIC));
            
            logger.info("公示库资费重新核查任务已提交，任务ID: {}", taskId);
            return EasyResult.ok(result, "公示库资费重新核查任务已提交");
        } catch (Exception e) {
            // 发生异常时，尝试清除运行状态
            try {
                TariffCheckService.clearCheckRunning(TariffCheckService.CHECK_TYPE_PUBLIC);
            } catch (Exception ex) {
                logger.error("清除检查任务运行状态时发生错误", ex);
            }
            
            logger.error("重新核查公示库资费时发生错误", e);
            return EasyResult.fail("重新核查公示库资费失败：" + e.getMessage());
        }
    }

    /**
     * 查询公示库同步任务状态
     * <p>
     * /servlet/tariffRule/getSyncStatus
     * </p>
     *
     * @return 同步任务状态信息
     */
    public JSONObject actionForGetSyncStatus() {
        try {
            logger.info("查询公示库同步任务状态");
            
            // 检查是否有任务正在运行
            boolean isRunning = TariffCrawlCheckService.isCheckRunning();
            
            // 返回结果
            JSONObject result = new JSONObject();
            result.put("isRunning", isRunning);
            
            if (isRunning) {
                // 如果有任务正在运行，尝试获取运行信息
                String runningInfoStr = RedissonUtil.get(TariffCrawlCheckService.CHECK_RUNNING_KEY);
                if (StringUtils.isNotBlank(runningInfoStr)) {
                    try {
                        JSONObject runningInfo = JSONObject.parseObject(runningInfoStr);
                        result.put("runningInfo", runningInfo);
                    } catch (Exception e) {
                        logger.error("解析运行信息失败: {}", e.getMessage(), e);
                    }
                }
            }
            
            logger.info("查询公示库同步任务状态完成，状态: {}", isRunning ? "运行中" : "未运行");
            return EasyResult.ok(result, "获取公示库同步任务状态成功");
        } catch (Exception e) {
            logger.error("查询公示库同步任务状态时发生错误", e);
            return EasyResult.fail("查询公示库同步任务状态失败：" + e.getMessage());
        }
    }



}