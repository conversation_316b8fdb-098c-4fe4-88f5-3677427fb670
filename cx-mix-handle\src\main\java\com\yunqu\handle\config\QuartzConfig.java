package com.yunqu.handle.config;

import com.yq.busi.common.util.CacheUtil;
import com.yunqu.handle.base.CommonLogger;
import com.yunqu.handle.base.Constants;
import com.yunqu.handle.job.OrderFetchJob;
import com.yunqu.handle.job.SystemWarnJob;
import com.yunqu.handle.job.AppealOrderCreateJob;
import com.yunqu.handle.job.NetworkMonitorJob;

import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Quartz调度器配置类
 * 负责初始化和管理定时任务
 */
public class QuartzConfig {
    
    private static Logger logger = LoggerFactory.getLogger(CommonLogger.getLogger("job").getName());
    
    private static Scheduler scheduler;

    private final static  String SCHEDULER_START_KEY = "xty:scheduler:start:";

    /**
     * 初始化调度器
     */
    public static void initScheduler() {
        try {
            String startFlag = CacheUtil.get(SCHEDULER_START_KEY);

            if ("01".equals(startFlag) || !Constants.getIsSync()) {
                logger.info("定时任务已启动;本机不做处理！");
                return;
            }
            CacheUtil.put(SCHEDULER_START_KEY,"01");

            logger.info("开始初始化Quartz调度器");
            
            // 创建调度器工厂
            SchedulerFactory schedulerFactory = new StdSchedulerFactory();
            
            // 获取调度器
            scheduler = schedulerFactory.getScheduler();
            
            // 创建工单拉取任务
            createOrderFetchJob();
            
            // 创建申诉工单创建任务
            createAppealOrderCreateJob();

            // 创建系统预警任务
            createSystemWarnJob();
            
            // 创建网络监控任务
            createNetworkMonitorJob();
            
            // 启动调度器
            scheduler.start();
            
            logger.info("Quartz调度器初始化完成并启动");
            
        } catch (Exception e) {
            logger.error("初始化Quartz调度器失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建工单拉取定时任务
     */
    private static void createOrderFetchJob() throws SchedulerException {
        // 创建JobDetail
        JobDetail jobDetail = JobBuilder.newJob(OrderFetchJob.class)
                .withIdentity("orderFetchJob", "orderGroup")
                .withDescription("工单拉取定时任务")
                .build();
        
        // 创建触发器 - 每5分钟执行一次
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("orderFetchTrigger", "orderGroup")
                .withDescription("工单拉取触发器")
                .withSchedule(CronScheduleBuilder.cronSchedule("0 */15 * * * ?")) // 每5分钟执行一次
                .build();
        
        // 将任务和触发器注册到调度器
        scheduler.scheduleJob(jobDetail, trigger);
        
        logger.info("工单拉取定时任务创建完成，每15分钟执行一次");
    }
    
    /**
     * 关闭调度器
     */
    public static void shutdownScheduler() {
        try {
            if (scheduler != null && !scheduler.isShutdown()) {
                CacheUtil.delete(SCHEDULER_START_KEY);
                logger.info("开始关闭Quartz调度器");
                scheduler.shutdown(true); // 等待正在执行的任务完成
                logger.info("Quartz调度器已关闭");
            }
        } catch (Exception e) {
            logger.error("关闭Quartz调度器失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建申诉工单创建定时任务
     */
    private static void createAppealOrderCreateJob() throws SchedulerException {
        // 创建JobDetail
        JobDetail jobDetail = JobBuilder.newJob(AppealOrderCreateJob.class)
                .withIdentity("appealOrderCreateJob", "appealGroup")
                .withDescription("申诉工单创建定时任务")
                .build();
        
        // 创建触发器 - 每5分钟执行一次
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("appealOrderCreateTrigger", "appealGroup")
                .withDescription("申诉工单创建触发器")
                .withSchedule(CronScheduleBuilder.cronSchedule("0 */10 * * * ?")) // 每5分钟执行一次
                .build();
        
        // 将任务和触发器注册到调度器
        scheduler.scheduleJob(jobDetail, trigger);
        
        logger.info("申诉工单创建定时任务创建完成，每10分钟执行一次");
    }


     /**
     * 创建系统预警定时任务
     */
    private static void createSystemWarnJob() throws SchedulerException {
        // 创建JobDetail
        JobDetail jobDetail = JobBuilder.newJob(SystemWarnJob.class)
                .withIdentity("systemWarnJob", "systemWarnGroup")
                .withDescription("系统预警定时任务")
                .build();
        
        // 创建触发器 - 每5分钟执行一次
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("systemWarnTrigger", "systemWarnGroup")
                .withDescription("系统预警创建触发器")
                .withSchedule(CronScheduleBuilder.cronSchedule("0 */59 * * * ?")) // 每5分钟执行一次
                .build();
        
        // 将任务和触发器注册到调度器
        scheduler.scheduleJob(jobDetail, trigger);
        
        logger.info("系统预警创建定时任务创建完成，每60分钟执行一次");
    }
    
    /**
     * 创建网络监控定时任务
     */
    private static void createNetworkMonitorJob() throws SchedulerException {
        // 创建JobDetail
        JobDetail jobDetail = JobBuilder.newJob(NetworkMonitorJob.class)
                .withIdentity("networkMonitorJob", "networkMonitorGroup")
                .withDescription("网络监控定时任务")
                .build();
        
        // 创建触发器 - 每1分钟执行一次
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("networkMonitorTrigger", "networkMonitorGroup")
                .withDescription("网络监控触发器")
                .withSchedule(CronScheduleBuilder.cronSchedule("0 */1 * * * ?")) // 每1分钟执行一次
                .build();
        
        // 将任务和触发器注册到调度器
        scheduler.scheduleJob(jobDetail, trigger);
        
        logger.info("网络监控定时任务创建完成，每1分钟执行一次");
    }
    
    /**
     * 获取调度器实例
     * @return 调度器实例
     */
    public static Scheduler getScheduler() {
        return scheduler;
    }
}